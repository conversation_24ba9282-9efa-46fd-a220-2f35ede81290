<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.integralN</groupId>
        <artifactId>concise-base</artifactId>
        <version>1.6.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <groupId>com.integralN.common</groupId>
    <artifactId>concise-common</artifactId>
    <version>1.6.0</version>

    <packaging>jar</packaging>

    <dependencies>
        <!-- aop -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- fastjson -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <!--阿里云上传文件客户端-->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>

        <!--腾讯云上传文件客户端-->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
        </dependency>

        <!--easypoi导入导出-->
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
        </dependency>

        <!--excel导入导出-->
        <dependency>
            <groupId>cn.idev.excel</groupId>
            <artifactId>fastexcel</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--libreoffice文档在线预览-->
        <dependency>
            <groupId>org.jodconverter</groupId>
            <artifactId>jodconverter-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jodconverter</groupId>
            <artifactId>jodconverter-local</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jodconverter</groupId>
            <artifactId>jodconverter-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.libreoffice</groupId>
            <artifactId>ridl</artifactId>
        </dependency>
        <!-- 数据库驱动,可根据自己需要自行删减，默认使用mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- oracle -->
        <!--<dependency>
            <groupId>com.oracle</groupId>
            <artifactId>ojdbc6</artifactId>
        </dependency>-->

        <!-- mssql -->
        <!--<dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>-->

        <!-- postgresql -->
        <!-- <dependency>
             <groupId>org.postgresql</groupId>
             <artifactId>postgresql</artifactId>
         </dependency>-->

        <!-- druid连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>

        <!-- mybatis-plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.4.1</version>
        </dependency>

        <!-- hutool -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <!--swagger接口文档-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
            <version>3.5.8</version>
        </dependency>
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-transports-http</artifactId>
            <version>3.5.8</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext7-core</artifactId>
            <version>7.1.18</version>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-rasterizer</artifactId>
            <version>1.14</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.27</version>
        </dependency>
        <!-- activiti工作流 -->
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-spring-boot-starter-basic</artifactId>
            <version>6.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-spring-boot-starter-rest-api</artifactId>
            <version>6.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-spring-boot-starter-actuator</artifactId>
            <version>6.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-spring-boot-starter-jpa</artifactId>
            <version>6.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.timvale</groupId>
            <artifactId>seal-platform-sdk</artifactId>
            <version>2.1.6-RELEASE</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/seal-platform-sdk-2.1.6-RELEASE.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.timvale</groupId>
            <artifactId>timevale-utils-basic</artifactId>
            <version>3.0.11</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/timevale-utils-basic-3.0.11.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>timevale</groupId>
            <artifactId>tgtext</artifactId>
            <version>3.4.50-RELEASE</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/tgtext-3.4.50-RELEASE.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.timvale</groupId>
            <artifactId>tgOfdSignUtils</artifactId>
            <version>1.0.33-RELEASE</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/tgOfdSignUtils-1.0.33-RELEASE.jar</systemPath>
        </dependency>


        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-core</artifactId>
            <version>9.5.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/feign-core-9.5.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-gson</artifactId>
            <version>9.5.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/feign-gson-9.7.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>timevale</groupId>
            <artifactId>timestampUtils</artifactId>
            <version>1.0.2-RELEASE</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/timestampUtils-1.0.2-RELEASE.jar</systemPath>
        </dependency>


        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/commons-collections-3.2.2.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.timevale</groupId>
            <artifactId>tgOfdSignUtils</artifactId>
            <version>1.0.33-RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>junit</artifactId>
                    <groupId>junit</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>gson</artifactId>
                    <groupId>com.google.code.gson</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>json-lib</artifactId>
                    <groupId>net.sf.json-lib</groupId>
                </exclusion>
            </exclusions>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/tgOfdSignUtils-1.0.33-RELEASE.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>smUtil</groupId>
            <artifactId>smUtil</artifactId>
            <version>1.3.3-RELEASE</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/smUtil-1.3.3-RELEASE.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>gmSignDataUtils</groupId>
            <artifactId>gmSignDataUtils</artifactId>
            <version>1.0.8-RELEASE</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/gmSignDataUtils-1.0.8-RELEASE.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>bcpkix-jdk15on</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
            <version>1.59</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/bcpkix-jdk15on-1.59.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>bcprov-jdk15on</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.59</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/bcprov-jdk15on-1.59.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>proguard-base</groupId>
            <artifactId>proguard-base</artifactId>
            <version>6.0.1</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/proguard-base-6.0.1.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.10</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/itextpdf-5.5.10.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/commons-io-2.3.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/dom4j-2.1.3.jar</systemPath>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>fastjson</groupId>-->
<!--            <artifactId>fastjson</artifactId>-->
<!--            <version>1.2.75</version>-->
<!--            <scope>system</scope>-->
<!--            <systemPath>${project.basedir}/lib/fastjson-1.2.75.jar</systemPath>-->
<!--        </dependency>-->
        <dependency>
            <groupId>seal-platform-sdk</groupId>
            <artifactId>seal-platform-sdk</artifactId>
            <version>1</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/seal-platform-sdk-1.jar</systemPath>
        </dependency>





        <!-- OCR 识别 -->
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.14.0</version>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.tess4j</groupId>
            <artifactId>tess4j</artifactId>
            <version>4.5.4</version>
        </dependency>
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.10.5</version>
        </dependency>
        <dependency>
            <groupId>e-iceblue</groupId>
            <artifactId>spire.doc.free</artifactId>
            <version>5.2.0</version>
        </dependency>
    </dependencies>
    <repositories>
        <repository>
            <id>com.e-iceblue</id>
            <name>e-iceblue</name>
            <url>https://repo.e-iceblue.cn/repository/maven-public/</url>
        </repository>
    </repositories>
</project>
