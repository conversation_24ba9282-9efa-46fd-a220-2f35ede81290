package com.concise.gen.revocationauditphase.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TaskInfo {

    private String activityId;

    private String taskId;

    private String processInstanceId;

    private String executionId;

    private String businessKey;

    private String processName;

    @ApiModelProperty("流程名称")
    private String taskName;

    private String starter;

    @ApiModelProperty("操作人")
    private String assignee;

    @ApiModelProperty("操作人所属部门")
    private String assigneeOrg;

    @ApiModelProperty("操作人所属法院")
    private String assigneeFy;

    @ApiModelProperty("关联对象")
    private String association;

    @ApiModelProperty("关联对象所属部门")
    private String associationOrg;

    @ApiModelProperty("关联对象所属法院")
    private String associationFy;


    @ApiModelProperty("流程开始时间")
    private String startTime;

    @ApiModelProperty("流程结束时间")
    private String endTime;

    private String createTime;

    private String formKey;

    @ApiModelProperty("备注")
    private String comment;

    private Integer pageSize;

    private Integer pageNum;

    /**
     * 审核类型1通过
     */
    private String approve;

    /**
     * 特殊标志
     */
    private Object flag;

    public TaskInfo() {
    }

    public TaskInfo(String processInstanceId, String assigneeOrg) {
        this.processInstanceId = processInstanceId;
        this.assigneeOrg = assigneeOrg;
    }
}
