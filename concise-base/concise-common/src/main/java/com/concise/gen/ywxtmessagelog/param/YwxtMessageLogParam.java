package com.concise.gen.ywxtmessagelog.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
* 一体化消息日志参数类
 *
 * <AUTHOR>
 * @date 2024-10-11 17:07:24
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class YwxtMessageLogParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private Long id;

    /**
     * 操作时间
     */
    private String opTime;

    /**
     * 服务编号
     */
    private String serverNum;

    /**
     * 发送单位
     */
    private String sendOrgCode;

    /**
     * 接收单位
     */
    private String receiveOrgCode;
    private String xxlx;

    /**
     *
     */
    private String taskId;

    /**
     * 解码后的xml
     */
    private String decodeXml;

    /**
     * 关联记录id
     */
    private String dataId;

    private String correctionObject;

}
