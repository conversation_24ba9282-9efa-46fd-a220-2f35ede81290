package com.concise.gen.investigationsign.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.investigationsign.entity.InvestigationSign;

import java.util.HashMap;

/**
 * 调查评估_手签
 *
 * <AUTHOR>
 * @date 2025-04-21 20:30:30
 */
public interface InvestigationSignMapper extends BaseMapper<InvestigationSign> {
    /**
     * 获取设备签名历史
     * @param infoId infoId
     * @param bizType bizType
     * @return String,String
     */
    HashMap<String, String> getDeviceSignHistory(String infoId, String bizType);

    /**
     * 获取设备签名历史
     * @param fileAfterId fileAfterId
     * @return String,String
     */
    HashMap<String, String> getDeviceSignHistoryByFileAfterId(String fileAfterId);

    /**
     * 获取设备签名历史
     * @param fileId fileId
     * @return fileId
     */
    String getDeviceSignedBefore(String fileId);
}
