package com.concise.gen.irsvocationalskillslevelcertificate.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 职业技能等级证书参数类
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:53
*/
@Data
public class IrsVocationalSkillsLevelCertificateParam extends BaseParam {

    /**
     * 出生日期
     */
    @NotBlank(message = "出生日期不能为空，请检查birthdate参数", groups = {add.class, edit.class})
    private String birthdate;

    /**
     * 证书类型
     */
    @NotBlank(message = "证书类型不能为空，请检查certificatetype参数", groups = {add.class, edit.class})
    private String certificatetype;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空，请检查gendername参数", groups = {add.class, edit.class})
    private String gendername;

    /**
     * 发证单位
     */
    @NotBlank(message = "发证单位不能为空，请检查issuedorgan参数", groups = {add.class, edit.class})
    private String issuedorgan;

    /**
     * 等级
     */
    @NotBlank(message = "等级不能为空，请检查jdrank参数", groups = {add.class, edit.class})
    private String jdrank;

    /**
     * 职业方向
     */
    @NotBlank(message = "职业方向不能为空，请检查jobfx参数", groups = {add.class, edit.class})
    private String jobfx;

    /**
     * 理论成绩
     */
    @NotBlank(message = "理论成绩不能为空，请检查llscore参数", groups = {add.class, edit.class})
    private String llscore;

    /**
     * 评定成绩
     */
    @NotBlank(message = "评定成绩不能为空，请检查pdscore参数", groups = {add.class, edit.class})
    private String pdscore;

    /**
     * 技能成绩
     */
    @NotBlank(message = "技能成绩不能为空，请检查scscore参数", groups = {add.class, edit.class})
    private String scscore;

    /**
     * 单位统一社会信用代码
     */
    @NotBlank(message = "单位统一社会信用代码不能为空，请检查uscc参数", groups = {add.class, edit.class})
    private String uscc;

    /**
     * 证书编号
     */
    @NotNull(message = "证书编号不能为空，请检查zsnum参数", groups = {edit.class, delete.class, detail.class})
    private String zsnum;

    /**
     * 综合成绩
     */
    @NotBlank(message = "综合成绩不能为空，请检查zhscore参数", groups = {add.class, edit.class})
    private String zhscore;

    /**
     * 证书颁发日期
     */
    @NotBlank(message = "证书颁发日期不能为空，请检查zsbftime参数", groups = {add.class, edit.class})
    private String zsbftime;

    /**
     * 职业工种
     */
    @NotBlank(message = "职业工种不能为空，请检查zygzname参数", groups = {add.class, edit.class})
    private String zygzname;

    /**
     * 矫正对象id
     */
    private String jzdxId;

    /**
     * 矫正机构id
     */
    private String jzjg;

    /**
     * 矫正机构
     */
    private String jzjgName;
}
