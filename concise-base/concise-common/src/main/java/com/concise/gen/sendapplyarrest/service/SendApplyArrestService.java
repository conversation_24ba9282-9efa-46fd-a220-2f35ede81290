package com.concise.gen.sendapplyarrest.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.dataCenter.arrest.entity.ArrestDc;
import com.concise.gen.sendapplyarrest.entity.SendApplyArrest;
import com.concise.gen.sendapplyarrest.param.SendApplyArrestParam;

import java.util.List;

/**
 * 发送提请逮捕service接口
 *
 * <AUTHOR>
 * @date 2023-09-20 14:47:19
 */
public interface SendApplyArrestService extends IService<SendApplyArrest> {

    /**
     * 查询发送提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-20 14:47:19
     */
    PageResult<SendApplyArrest> page(SendApplyArrestParam sendApplyArrestParam);

    /**
     * 发送提请逮捕列表
     *
     * <AUTHOR>
     * @date 2023-09-20 14:47:19
     */
    List<SendApplyArrest> list(SendApplyArrestParam sendApplyArrestParam);

    /**
     * 添加发送提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-20 14:47:19
     */
    void add(SendApplyArrestParam sendApplyArrestParam);

    /**
     * 删除发送提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-20 14:47:19
     */
    void delete(SendApplyArrestParam sendApplyArrestParam);

    /**
     * 编辑发送提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-20 14:47:19
     */
    void edit(SendApplyArrestParam sendApplyArrestParam);

    /**
     * 查看发送提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-20 14:47:19
     */
     SendApplyArrest detail(SendApplyArrestParam sendApplyArrestParam);

    void add(String id);
    void add(ArrestDc dc);

    /**
     * 根据TYFH更新反馈信息
     * @param param SendApplyArrestParam
     * @param step 2/3 对应zt字段
     * @return AcceptCorrectionDocParam
     */
    List<AcceptCorrectionDocParam> updateByTyfh(SendApplyArrestParam param,String step);
}
