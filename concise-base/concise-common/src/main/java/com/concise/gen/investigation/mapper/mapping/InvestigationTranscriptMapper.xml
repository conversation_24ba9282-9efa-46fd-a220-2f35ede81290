<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.investigation.mapper.InvestigationTranscriptMapper">

    <select id="selectPage" resultType="com.concise.gen.investigation.vo.DeviceInvestigationTranscriptDTO">
        select transcript.*,info.inve_dept,info.inve_dept_name,info.tag as info_tag,
        LEFT( info.entrustment_receive_time,10) as entrustment_receive_time,
        LEFT( info.inve_time_limit,10) as inve_time_limit,
        info.correction_obj_name
        from investigation_p2_transcript transcript
        left join investigation_info info
        on transcript.pid = info.id
        where info.inve_dept in
        <foreach collection="param.inveDeptList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and transcript.deleted = 0
        and transcript.tag = 1
        <if test="param.type != null and param.type == 1">
            and transcript.progress != '2'
        </if>
        <if test="param.type != null and param.type == 2">
            and transcript.progress = '2'
        </if>
        <if test="param.type != null and param.type == 1">
            and info.status_ = 'PGZT03' AND info.tag = 1
        </if>
        <if test="param.correctionObjName!=null and param.correctionObjName!=''">
            and transcript.title like CONCAT(CONCAT('%', #{param.correctionObjName}), '%')
        </if>

        <if test="param.inveDept!=null and param.inveDept!=''">
            and info.inve_dept =#{param.inveDept}
        </if>
        order by IFNULL(transcript.update_time, transcript.create_time) desc
    </select>

</mapper>
