package com.concise.gen.revocationauditphase.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.revocationauditphase.entity.RevocationAuditPhase;
import com.concise.gen.revocationauditphase.param.RevocationAuditPhaseParam;
import java.util.List;

/**
 * 提请撤销审核阶段表service接口
 *
 * <AUTHOR>
 * @date 2024-02-29 14:41:31
 */
public interface RevocationAuditPhaseService extends IService<RevocationAuditPhase> {

    /**
     * 查询提请撤销审核阶段表
     *
     * <AUTHOR>
     * @date 2024-02-29 14:41:31
     */
    PageResult<RevocationAuditPhase> page(RevocationAuditPhaseParam revocationAuditPhaseParam);

    /**
     * 提请撤销审核阶段表列表
     *
     * <AUTHOR>
     * @date 2024-02-29 14:41:31
     */
    List<RevocationAuditPhase> list(RevocationAuditPhaseParam revocationAuditPhaseParam);

    /**
     * 添加提请撤销审核阶段表
     *
     * <AUTHOR>
     * @date 2024-02-29 14:41:31
     */
    void add(RevocationAuditPhaseParam revocationAuditPhaseParam);

    /**
     * 删除提请撤销审核阶段表
     *
     * <AUTHOR>
     * @date 2024-02-29 14:41:31
     */
    void delete(RevocationAuditPhaseParam revocationAuditPhaseParam);

    /**
     * 编辑提请撤销审核阶段表
     *
     * <AUTHOR>
     * @date 2024-02-29 14:41:31
     */
    void edit(RevocationAuditPhaseParam revocationAuditPhaseParam);

    /**
     * 查看提请撤销审核阶段表
     *
     * <AUTHOR>
     * @date 2024-02-29 14:41:31
     */
     RevocationAuditPhase detail(RevocationAuditPhaseParam revocationAuditPhaseParam);
}
