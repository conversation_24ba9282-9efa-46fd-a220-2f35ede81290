package com.concise.gen.documentserial.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;

/**
 * 文书号维护
 *
 * <AUTHOR>
 * @date 2025-03-27 10:09:11
 */
@Data
@TableName("document_serial")
public class DocumentSerial {

    /**  主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**  文书类型，字典值：WSLX */
    private String type;

    /**  市、县区机构id */
    private String deptId;

    /**  市+区/县简称 */
    private String sName;

    /**  当前年份 */
    private Integer currentYear;

    /**  序列号 */
    private Integer serialNumber;

    /**  业务信息id */
    private String referId;

}
