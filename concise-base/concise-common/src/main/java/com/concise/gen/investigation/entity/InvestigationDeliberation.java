package com.concise.gen.investigation.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.concise.common.file.param.SysFileInfoVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 调查评估_合议
 *
 * <AUTHOR>
 * @date 2025-03-24 14:59:17
 */
@Data
@TableName("investigation_p3_deliberation")
public class InvestigationDeliberation{

    /**  主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**  调查对象姓名 */
    private String correctionObjName;

    /**  审核结果 */
    private String approvalResult;

    /**  审核意见 */
    private String approvalRemark;

    /**  审核人 */
    private String approvalPsn;

    /**  审核时间 */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    /**  调查机构 */
    private String inveDept;

    /**  合议审核事项 */
    private String matter;

    /**  主持人 */
    private String host;

    /**  合议审核地点 */
    private String address;

    /**  合议审核时间 */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date deliberationTime;

    /**  合议审核人员 */
    private String psnList;

    /**  记录人 */
    private String recorder;

    /**  合议审核情况 */
    private String context;

    /**  合议审核意见 */
    private String opinion;

    /**  备注 */
    private String remark;

    /**  调查评估文书号 */
    private String docNum;

    /**  调查评估结论 */
    private String conclusion;

    /**  调查评估情况 */
    private String particular;

    /**  是否删除 */
    private Integer deleted;

    /**
     * 小组合议 */
    @TableField(exist = false)
    private List<SysFileInfoVO> opinionFilesList;
    @TableField(exist = false)
    private List<SysFileInfoVO> evaluationFiles;

}
