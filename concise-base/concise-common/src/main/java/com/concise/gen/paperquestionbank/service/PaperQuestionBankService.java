package com.concise.gen.paperquestionbank.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.paperquestionbank.entity.PaperQuestionBank;
import com.concise.gen.paperquestionbank.param.PaperQuestionBankParam;
import java.util.List;

/**
 * 题库service接口
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:58
 */
public interface PaperQuestionBankService extends IService<PaperQuestionBank> {

    /**
     * 查询题库
     *
     * <AUTHOR>
     * @date 2025-03-20 15:02:58
     */
    PageResult<PaperQuestionBank> page(PaperQuestionBankParam paperQuestionBankParam);

    /**
     * 查询题库(带指标)
     *
     * <AUTHOR>
     * @date 2025-03-20 15:02:58
     */
    PageResult<PaperQuestionBank> pageDetail(PaperQuestionBankParam paperQuestionBankParam);

    /**
     * 题库列表
     *
     * <AUTHOR>
     * @date 2025-03-20 15:02:58
     */
    List<PaperQuestionBank> list(PaperQuestionBankParam paperQuestionBankParam);

    /**
     * 添加题库
     *
     * <AUTHOR>
     * @date 2025-03-20 15:02:58
     */
    void add(PaperQuestionBankParam paperQuestionBankParam);

    /**
     * 删除题库
     *
     * <AUTHOR>
     * @date 2025-03-20 15:02:58
     */
    void delete(PaperQuestionBankParam paperQuestionBankParam);

    /**
     * 编辑题库
     *
     * <AUTHOR>
     * @date 2025-03-20 15:02:58
     */
    void edit(PaperQuestionBankParam paperQuestionBankParam);

    /**
     * 查看题库
     *
     * <AUTHOR>
     * @date 2025-03-20 15:02:58
     */
     PaperQuestionBank detail(PaperQuestionBankParam paperQuestionBankParam);
}
