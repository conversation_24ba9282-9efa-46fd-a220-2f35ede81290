package com.concise.gen.papertopicitem.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.papertopicitem.entity.PaperTopicItem;
import com.concise.gen.papertopicitem.enums.PaperTopicItemExceptionEnum;
import com.concise.gen.papertopicitem.mapper.PaperTopicItemMapper;
import com.concise.gen.papertopicitem.param.PaperTopicItemParam;
import com.concise.gen.papertopicitem.service.PaperTopicItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 量卷维护-题目-指标选项service接口实现类
 *
 * <AUTHOR>
 * @date 2025-03-20 15:03:05
 */
@Service
public class PaperTopicItemServiceImpl extends ServiceImpl<PaperTopicItemMapper, PaperTopicItem> implements PaperTopicItemService {

    @Override
    public PageResult<PaperTopicItem> page(PaperTopicItemParam paperTopicItemParam) {
        QueryWrapper<PaperTopicItem> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(paperTopicItemParam)) {

            // 根据量卷-题目主键 查询
            if (ObjectUtil.isNotEmpty(paperTopicItemParam.getPaperTopicId())) {
                queryWrapper.lambda().eq(PaperTopicItem::getPaperTopicId, paperTopicItemParam.getPaperTopicId());
            }
            // 根据指标选项内容 查询
            if (ObjectUtil.isNotEmpty(paperTopicItemParam.getContent())) {
                queryWrapper.lambda().eq(PaperTopicItem::getContent, paperTopicItemParam.getContent());
            }
            // 根据分数 查询
            if (ObjectUtil.isNotEmpty(paperTopicItemParam.getItemScore())) {
                queryWrapper.lambda().eq(PaperTopicItem::getItemScore, paperTopicItemParam.getItemScore());
            }
            // 根据排序号 查询
            if (ObjectUtil.isNotEmpty(paperTopicItemParam.getSerialNumber())) {
                queryWrapper.lambda().eq(PaperTopicItem::getSerialNumber, paperTopicItemParam.getSerialNumber());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<PaperTopicItem> list(PaperTopicItemParam paperTopicItemParam) {
        return this.list();
    }

    @Override
    public void add(PaperTopicItemParam paperTopicItemParam) {
        PaperTopicItem paperTopicItem = new PaperTopicItem();
        BeanUtil.copyProperties(paperTopicItemParam, paperTopicItem);
        this.save(paperTopicItem);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(PaperTopicItemParam paperTopicItemParam) {
        this.removeById(paperTopicItemParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(PaperTopicItemParam paperTopicItemParam) {
        PaperTopicItem paperTopicItem = this.queryPaperTopicItem(paperTopicItemParam);
        BeanUtil.copyProperties(paperTopicItemParam, paperTopicItem);
        this.updateById(paperTopicItem);
    }

    @Override
    public PaperTopicItem detail(PaperTopicItemParam paperTopicItemParam) {
        return this.queryPaperTopicItem(paperTopicItemParam);
    }

    /**
     * 获取量卷维护-题目-指标选项
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:05
     */
    private PaperTopicItem queryPaperTopicItem(PaperTopicItemParam paperTopicItemParam) {
        PaperTopicItem paperTopicItem = this.getById(paperTopicItemParam.getId());
        if (ObjectUtil.isNull(paperTopicItem)) {
            throw new ServiceException(PaperTopicItemExceptionEnum.NOT_EXIST);
        }
        return paperTopicItem;
    }
}
