package com.concise.gen.ywxtwslog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ywxtwslog.entity.YwxtWsLog;
import com.concise.gen.ywxtwslog.param.YwxtWsLogParam;
import java.util.List;

/**
 * ws接口获取日志service接口
 *
 * <AUTHOR>
 * @date 2023-05-11 14:49:29
 */
public interface YwxtWsLogService extends IService<YwxtWsLog> {

    /**
     * 查询ws接口获取日志
     *
     * <AUTHOR>
     * @date 2023-05-11 14:49:29
     */
    PageResult<YwxtWsLog> page(YwxtWsLogParam ywxtWsLogParam);

}
