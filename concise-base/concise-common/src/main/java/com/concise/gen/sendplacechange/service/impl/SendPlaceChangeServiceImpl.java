package com.concise.gen.sendplacechange.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectionobject.entity.AcceptCorrectionObject;
import com.concise.gen.acceptcorrectionobject.service.AcceptCorrectionObjectService;
import com.concise.gen.accepttemporarilyoutsideprison.entity.AcceptTemporarilyOutsidePrison;
import com.concise.gen.accepttemporarilyoutsideprison.service.AcceptTemporarilyOutsidePrisonService;
import com.concise.gen.dataCenter.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.dataCenter.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.gen.dataCenter.correctplacechange.entity.PlaceChangeDc;
import com.concise.gen.dataCenter.correctplacechange.service.PlaceChangeDcService;
import com.concise.gen.extorginfo.service.ExtOrgInfoService;
import com.concise.gen.sendplacechange.entity.SendPlaceChange;
import com.concise.gen.sendplacechange.enums.SendPlaceChangeExceptionEnum;
import com.concise.gen.sendplacechange.mapper.SendPlaceChangeMapper;
import com.concise.gen.sendplacechange.param.SendPlaceChangeParam;
import com.concise.gen.sendplacechange.service.SendPlaceChangeService;
import com.concise.gen.sysorg.entity.OrgCommon;
import com.concise.gen.sysorg.service.OrgCommonService;
import com.concise.gen.webservice.service.SendPlacechangeService;
import com.concise.gen.webservice.utils.DictWdToHyUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发送变更执行地通知service接口实现类
 *
 * <AUTHOR>
 * @date 2023-09-13 14:39:13
 */
@Service
public class SendPlaceChangeServiceImpl extends ServiceImpl<SendPlaceChangeMapper, SendPlaceChange> implements SendPlaceChangeService {

    @Resource
    private PlaceChangeDcService placeChangeDcService;
    @Resource
    private CorrectionObjectInformationService objInfoDcService;
    @Resource
    private ExtOrgInfoService extOrgInfoService;
    @Resource
    private OrgCommonService orgService;
    @Resource
    private AcceptCorrectionObjectService acceptCorrectionObjectService;
    @Resource
    private AcceptTemporarilyOutsidePrisonService acceptTemporarilyOutsidePrisonService;
    @Resource
    private SendPlacechangeService sendService;

    @Override
    public PageResult<SendPlaceChange> page(SendPlaceChangeParam param) {
        QueryWrapper<SendPlaceChange> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据状态 查询
            if (ObjectUtil.isNotEmpty(param.getZt())) {
                queryWrapper.lambda().eq(SendPlaceChange::getZt, param.getZt());
            }
            if ("0".equals(param.getType())) {
                //迁出
                queryWrapper.lambda().likeRight(SendPlaceChange::getType, param.getType())
                        .apply("jzjg_id in (select id from sys_org where pids like '%"+param.getJzjgId()+"%' or id = '"+param.getJzjgId()+"')");
            }else if ("1".equals(param.getType())) {
                //迁入
                queryWrapper.lambda().likeRight(SendPlaceChange::getType, param.getType())
                        .apply("qrdw_id in (select id from sys_org where pids like '%"+param.getJzjgId()+"%' or id = '"+param.getJzjgId()+"')");
            }

            // 根据社区矫正决定机关 查询
            if (ObjectUtil.isNotEmpty(param.getSqjzjdjg())) {
                queryWrapper.lambda().eq(SendPlaceChange::getSqjzjdjg, param.getSqjzjdjg());
            }
            // 根据发送时间 查询
            if (ObjectUtil.isNotEmpty(param.getSearchBeginTime())) {
                queryWrapper.lambda().ge(SendPlaceChange::getFssj, param.getSearchBeginTime());
            }
            if (ObjectUtil.isNotEmpty(param.getSearchEndTime())) {
                queryWrapper.lambda().le(SendPlaceChange::getFssj, param.getSearchEndTime());
            }
            // 根据统一赋号 查询
            if (ObjectUtil.isNotEmpty(param.getTyfh())) {
                queryWrapper.lambda().eq(SendPlaceChange::getTyfh, param.getTyfh());
            }
            // 根据社区矫正案件编号 查询
            if (ObjectUtil.isNotEmpty(param.getSqjzajbh())) {
                queryWrapper.lambda().eq(SendPlaceChange::getSqjzajbh, param.getSqjzajbh());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(SendPlaceChange::getXm, param.getXm().trim());
            }
        }
        queryWrapper.lambda().eq(SendPlaceChange::getDeleted, 0).orderByDesc(SendPlaceChange::getSqrq);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<SendPlaceChange> list(SendPlaceChangeParam sendPlaceChangeParam) {
        return this.list();
    }

    @Override
    public void saveOrUpdate(SendPlaceChangeParam sendPlaceChangeParam) {
        SendPlaceChange sendPlaceChange = new SendPlaceChange();
        BeanUtil.copyProperties(sendPlaceChangeParam, sendPlaceChange);
        this.save(sendPlaceChange);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(SendPlaceChangeParam sendPlaceChangeParam) {
        this.lambdaUpdate().set(SendPlaceChange::getDeleted, 1).eq(SendPlaceChange::getId, sendPlaceChangeParam.getId()).update();
    }

    @Override
    public void edit(SendPlaceChangeParam sendPlaceChangeParam) {
        SendPlaceChange sendPlaceChange = this.querySendPlaceChange(sendPlaceChangeParam);
        BeanUtil.copyProperties(sendPlaceChangeParam, sendPlaceChange);
        this.updateById(sendPlaceChange);
        send(this.querySendPlaceChange(sendPlaceChangeParam));
    }

    @Override
    public SendPlaceChange detail(SendPlaceChangeParam sendPlaceChangeParam) {
        return this.querySendPlaceChange(sendPlaceChangeParam);
    }

    /**
     * 获取发送变更执行地通知
     *
     * <AUTHOR>
     * @date 2023-09-13 14:39:13
     */
    private SendPlaceChange querySendPlaceChange(SendPlaceChangeParam sendPlaceChangeParam) {
        SendPlaceChange sendPlaceChange = this.getById(sendPlaceChangeParam.getId());
        if (ObjectUtil.isNull(sendPlaceChange)) {
            throw new ServiceException(SendPlaceChangeExceptionEnum.NOT_EXIST);
        }
        return sendPlaceChange;
    }

    @Override
    public void add(String id){
        if ("all".equals(id)) {
            placeChangeDcService.list(new LambdaQueryWrapper<PlaceChangeDc>().eq(PlaceChangeDc::getZzcljgName, "已矫正终止").gt(PlaceChangeDc::getLastModifiedTime, "2024-12-01 00:00:00")).forEach(e -> save(e,"00"));
            placeChangeDcService.list(new LambdaQueryWrapper<PlaceChangeDc>().eq(PlaceChangeDc::getZzcljgName, "拟接收区县局局领导已接收").gt(PlaceChangeDc::getLastModifiedTime, "2024-12-01 00:00:00")).forEach(e -> save(e,"0"));

        }else if (ObjectUtil.isNotEmpty(id)) {
            PlaceChangeDc dc = placeChangeDcService.getById(id);
            if (dc == null) {
                return;
            }
            if (ObjectUtil.isEmpty(dc.getNjsjzdwid())) {
                save(dc, "00");
            }else {
                save(dc, "0");
            }
        }
    }

    @Override
    public void save(PlaceChangeDc dc, String target) {

        CorrectionObjectInformation obj = objInfoDcService.getById(dc.getPid());
        if (obj == null) {
            return;
        }

        SendPlaceChange placeChange = new SendPlaceChange();
        placeChange.setId(dc.getId());
        placeChange.setType(target);
        placeChange.setTyfh(dc.getId());
        placeChange.setSqjzajbh(dc.getPid());
        placeChange.setZfbh(dc.getPid());
        placeChange.setXm(dc.getPname());
        placeChange.setXb(obj.getXb());
        placeChange.setZjlx("111");
        placeChange.setZjhm(obj.getSfzh());
        placeChange.setCsrq(obj.getCsrq());
        placeChange.setHjd(obj.getHjszs()+","+obj.getHjszds()+","+obj.getHjszxq()+","+obj.getHjszd());
        placeChange.setHjdxz(obj.getHjszdmx());
        placeChange.setXzd(obj.getGdjzdszs()+","+obj.getGdjzdszds()+","+obj.getGdjzdszxq()+","+obj.getGdjzd());
        placeChange.setXzdxz(obj.getGdjzdmx());

        placeChange.setSxpjjg(obj.getSxpjjg());
        placeChange.setPjwswh(obj.getPjszh());
        placeChange.setPjrq(obj.getPjrq());
        placeChange.setPjzm(obj.getZmName());
        placeChange.setPjqtzm(obj.getPjqtzm());
        placeChange.setZx(obj.getZx());
        placeChange.setFjx(obj.getFjx());


        placeChange.setJzjgId(dc.getJiedaoId());
        placeChange.setJzjgName(dc.getJiedaoName());
        placeChange.setJzlb(DictWdToHyUtils.jzlb(obj.getJzlb()));
        placeChange.setJdjg(extOrgInfoService.getExtOrgCode(obj.getJdjglx() + obj.getJdjgmc()));
        placeChange.setSfs(obj.getJzjgName());
        placeChange.setSqjzksrq(obj.getSqjzksrq());
        placeChange.setSqjzjsrq(obj.getSqjzjsrq());
        placeChange.setJzqx(obj.getSqjzqx());
        placeChange.setQrdwId(dc.getNjsjzdwid());
        placeChange.setQrdwName(dc.getNjsjzdwName());
        placeChange.setQcd(obj.getGdjzdszsName()+obj.getGdjzdszdsName()+obj.getGdjzdszxqName()+obj.getGdjzdName()+obj.getGdjzdmx());
        placeChange.setQrd(dc.getQrdszsName()+dc.getQrdszdName()+dc.getQrdszxName()+dc.getQrdxzName()+dc.getQrdmx());
        placeChange.setQrdCode(dc.getQrdszs()+","+dc.getQrdszd()+","+dc.getQrdszx()+","+dc.getQrdxz());
        placeChange.setSyjyj(dc.getJzdbgsy());
        placeChange.setSqrq(dc.getSqsj());

        placeChange.setLxdh(obj.getDwhm());
        OrgCommon byId = orgService.getById(placeChange.getJzjgId());
        if (byId != null) {
            OrgCommon byId1 = orgService.getById(byId.getPid());
            placeChange.setCbr(byId1.getLxr());
            placeChange.setCbrdh(byId1.getLxdh());
            placeChange.setCbrbm(byId1.getName());
            placeChange.setFsdw(byId1.getCode());
        }

        saveOrUpdate(placeChange);

        if ("0".equals(target)) {
            //省内记录
            placeChange.setId(dc.getId()+"_1");
            placeChange.setType("1");

            byId = orgService.getById(placeChange.getQrdwId());
            if (byId != null) {
                OrgCommon byId1 = orgService.getById(byId.getPid());
                placeChange.setCbr(byId1.getLxr());
                placeChange.setCbrdh(byId1.getLxdh());
                placeChange.setCbrbm(byId1.getName());
                placeChange.setFsdw(byId1.getCode());
            }
            saveOrUpdate(placeChange);
        }
    }

    @Override
    public void send(SendPlaceChange xt){
        CorrectionObjectInformation correctionObj = objInfoDcService.getById(xt.getSqjzajbh());
        OrgCommon byId = orgService.getById(xt.getJzjgId());
        if (byId == null) {
            return;
        }
        OrgCommon sfj = orgService.getById(byId.getPid());

        String JSDW = "330000020101030001";
        String ga = "0";
        String fy = "0";
        String jy = "0";
        OrgCommon qrsfs = orgService.getById(xt.getQrdwId());
        if (qrsfs != null) {
            OrgCommon qrsfj = orgService.getById(qrsfs.getPid());
            JSDW = qrsfj.getCode();
        }
        AcceptCorrectionObject xt4003 = acceptCorrectionObjectService.lambdaQuery().
                eq(AcceptCorrectionObject::getZjhm, correctionObj.getSfzh())
                .orderByDesc(AcceptCorrectionObject::getSdsj).last("limit 1").one();
        if (xt4003 != null) {
            fy = xt4003.getFyajbs();
            xt.setTyfh(xt4003.getTyfh());
            updateById(xt);
        }
        AcceptTemporarilyOutsidePrison top = acceptTemporarilyOutsidePrisonService.lambdaQuery().eq(AcceptTemporarilyOutsidePrison::getZjhm, correctionObj.getSfzh()).orderByDesc(AcceptTemporarilyOutsidePrison::getSdsj).last("limit 1").one();
        if (top != null) {
            switch (top.getSjlylx()) {
                case "61":jy=top.getAjbs();break;
                case "20":fy=top.getAjbs();break;
                case "40":ga=top.getAjbs();break;
                default:
            }
            xt.setTyfh(top.getTyfh());
            updateById(xt);
        }
        sendService.requestXTBH4601_x(xt,correctionObj,JSDW,ga,fy,jy,sfj,xt4003==null?"" :xt4003.getJcybmsah());
    }
}
