package com.concise.gen.papertopic.entity;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.gen.papertopicitem.entity.PaperTopicItem;

import lombok.Data;

/**
 * 量卷维护-题目
 *
 * <AUTHOR>
 * @date 2025-03-20 15:03:03
 */
@Data
@TableName("paper_topic")
public class PaperTopic {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 量卷主键
     */
    private String paperMaintenanceId;

    /**
     * 问题名称
     */
    private String topicName;

    /**
     * 指标
     */
    private String indexName;

    /**
     * 问题类型，字典：paper_topic_type
     */
    private String topicType;
    /**
     * 分数
     */
    private Integer topicScore;

    /**
     * 备注
     */
    private String remark;

    /**
     * 排序号
     */
    private Integer serialNumber;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    @TableField(exist = false)
    private List<PaperTopicItem> itemList;

    /**
     * 用户回答
     */
    @TableField(exist = false)
    private String userAnswer;

    /**
     * 用户选择
     */
    @TableField(exist = false)
    private String userSelectId;

}
