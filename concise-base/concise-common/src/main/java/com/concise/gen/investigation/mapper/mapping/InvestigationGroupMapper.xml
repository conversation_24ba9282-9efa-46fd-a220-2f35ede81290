<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.investigation.mapper.InvestigationGroupMapper">

    <select id="dictName" resultType="java.lang.String">
        select sys_dict_data.value
        from sys_dict_data
            left join sys_dict_type on sys_dict_type.id = sys_dict_data.type_id
        where sys_dict_type.code = #{type} and sys_dict_data.code = #{code}
    </select>
</mapper>
