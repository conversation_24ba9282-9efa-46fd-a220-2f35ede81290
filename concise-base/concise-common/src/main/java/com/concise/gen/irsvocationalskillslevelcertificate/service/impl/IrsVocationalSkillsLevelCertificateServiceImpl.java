package com.concise.gen.irsvocationalskillslevelcertificate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.irsvocationalskillslevelcertificate.entity.IrsVocationalSkillsLevelCertificate;
import com.concise.gen.irsvocationalskillslevelcertificate.enums.IrsVocationalSkillsLevelCertificateExceptionEnum;
import com.concise.gen.irsvocationalskillslevelcertificate.mapper.IrsVocationalSkillsLevelCertificateMapper;
import com.concise.gen.irsvocationalskillslevelcertificate.param.IrsVocationalSkillsLevelCertificateParam;
import com.concise.gen.irsvocationalskillslevelcertificate.service.IrsVocationalSkillsLevelCertificateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * 职业技能等级证书service接口实现类
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:53
 */
@DS("dataCenter")
@Slf4j
@Service
public class IrsVocationalSkillsLevelCertificateServiceImpl extends ServiceImpl<IrsVocationalSkillsLevelCertificateMapper, IrsVocationalSkillsLevelCertificate> implements IrsVocationalSkillsLevelCertificateService {


    @Override
    public PageResult<IrsVocationalSkillsLevelCertificate> page(IrsVocationalSkillsLevelCertificateParam irsVocationalSkillsLevelCertificateParam, Set<String> orgIds) {
        QueryWrapper<IrsVocationalSkillsLevelCertificate> queryWrapper = new QueryWrapper<>();
        if (CollectionUtil.isNotEmpty(orgIds)) {
            queryWrapper.lambda().in(IrsVocationalSkillsLevelCertificate::getJzjg, orgIds);
        }
        if (ObjectUtil.isNotNull(irsVocationalSkillsLevelCertificateParam)) {

            // 根据出生日期 查询
            if (ObjectUtil.isNotEmpty(irsVocationalSkillsLevelCertificateParam.getBirthdate())) {
                queryWrapper.lambda().eq(IrsVocationalSkillsLevelCertificate::getBirthdate, irsVocationalSkillsLevelCertificateParam.getBirthdate());
            }
            // 根据证书类型 查询
            if (ObjectUtil.isNotEmpty(irsVocationalSkillsLevelCertificateParam.getCertificatetype())) {
                queryWrapper.lambda().eq(IrsVocationalSkillsLevelCertificate::getCertificatetype, irsVocationalSkillsLevelCertificateParam.getCertificatetype());
            }
            // 根据性别 查询
            if (ObjectUtil.isNotEmpty(irsVocationalSkillsLevelCertificateParam.getGendername())) {
                queryWrapper.lambda().eq(IrsVocationalSkillsLevelCertificate::getGendername, irsVocationalSkillsLevelCertificateParam.getGendername());
            }
            // 根据发证单位 查询
            if (ObjectUtil.isNotEmpty(irsVocationalSkillsLevelCertificateParam.getIssuedorgan())) {
                queryWrapper.lambda().eq(IrsVocationalSkillsLevelCertificate::getIssuedorgan, irsVocationalSkillsLevelCertificateParam.getIssuedorgan());
            }
            // 根据等级 查询
            if (ObjectUtil.isNotEmpty(irsVocationalSkillsLevelCertificateParam.getJdrank())) {
                queryWrapper.lambda().eq(IrsVocationalSkillsLevelCertificate::getJdrank, irsVocationalSkillsLevelCertificateParam.getJdrank());
            }
            // 根据职业方向 查询
            if (ObjectUtil.isNotEmpty(irsVocationalSkillsLevelCertificateParam.getJobfx())) {
                queryWrapper.lambda().eq(IrsVocationalSkillsLevelCertificate::getJobfx, irsVocationalSkillsLevelCertificateParam.getJobfx());
            }
            // 根据理论成绩 查询
            if (ObjectUtil.isNotEmpty(irsVocationalSkillsLevelCertificateParam.getLlscore())) {
                queryWrapper.lambda().eq(IrsVocationalSkillsLevelCertificate::getLlscore, irsVocationalSkillsLevelCertificateParam.getLlscore());
            }
            // 根据评定成绩 查询
            if (ObjectUtil.isNotEmpty(irsVocationalSkillsLevelCertificateParam.getPdscore())) {
                queryWrapper.lambda().eq(IrsVocationalSkillsLevelCertificate::getPdscore, irsVocationalSkillsLevelCertificateParam.getPdscore());
            }
            // 根据技能成绩 查询
            if (ObjectUtil.isNotEmpty(irsVocationalSkillsLevelCertificateParam.getScscore())) {
                queryWrapper.lambda().eq(IrsVocationalSkillsLevelCertificate::getScscore, irsVocationalSkillsLevelCertificateParam.getScscore());
            }
            // 根据单位统一社会信用代码 查询
            if (ObjectUtil.isNotEmpty(irsVocationalSkillsLevelCertificateParam.getUscc())) {
                queryWrapper.lambda().eq(IrsVocationalSkillsLevelCertificate::getUscc, irsVocationalSkillsLevelCertificateParam.getUscc());
            }
            // 根据综合成绩 查询
            if (ObjectUtil.isNotEmpty(irsVocationalSkillsLevelCertificateParam.getZhscore())) {
                queryWrapper.lambda().eq(IrsVocationalSkillsLevelCertificate::getZhscore, irsVocationalSkillsLevelCertificateParam.getZhscore());
            }
            // 根据证书颁发日期 查询
            if (ObjectUtil.isNotEmpty(irsVocationalSkillsLevelCertificateParam.getZsbftime())) {
                queryWrapper.lambda().eq(IrsVocationalSkillsLevelCertificate::getZsbftime, irsVocationalSkillsLevelCertificateParam.getZsbftime());
            }
            // 根据职业工种 查询
            if (ObjectUtil.isNotEmpty(irsVocationalSkillsLevelCertificateParam.getZygzname())) {
                queryWrapper.lambda().eq(IrsVocationalSkillsLevelCertificate::getZygzname, irsVocationalSkillsLevelCertificateParam.getZygzname());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<IrsVocationalSkillsLevelCertificate> list(IrsVocationalSkillsLevelCertificateParam irsVocationalSkillsLevelCertificateParam) {
        return this.list();
    }

    @Override
    public void add(IrsVocationalSkillsLevelCertificateParam irsVocationalSkillsLevelCertificateParam) {
        IrsVocationalSkillsLevelCertificate irsVocationalSkillsLevelCertificate = new IrsVocationalSkillsLevelCertificate();
        BeanUtil.copyProperties(irsVocationalSkillsLevelCertificateParam, irsVocationalSkillsLevelCertificate);
        this.save(irsVocationalSkillsLevelCertificate);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(IrsVocationalSkillsLevelCertificateParam irsVocationalSkillsLevelCertificateParam) {
        this.removeById(irsVocationalSkillsLevelCertificateParam.getZsnum());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(IrsVocationalSkillsLevelCertificateParam irsVocationalSkillsLevelCertificateParam) {
        IrsVocationalSkillsLevelCertificate irsVocationalSkillsLevelCertificate = this.queryIrsVocationalSkillsLevelCertificate(irsVocationalSkillsLevelCertificateParam);
        BeanUtil.copyProperties(irsVocationalSkillsLevelCertificateParam, irsVocationalSkillsLevelCertificate);
        this.updateById(irsVocationalSkillsLevelCertificate);
    }

    @Override
    public IrsVocationalSkillsLevelCertificate detail(IrsVocationalSkillsLevelCertificateParam irsVocationalSkillsLevelCertificateParam) {
        return this.queryIrsVocationalSkillsLevelCertificate(irsVocationalSkillsLevelCertificateParam);
    }

    /**
     * 获取职业技能等级证书
     *
     * <AUTHOR>
     * @date 2024-01-11 09:21:53
     */
    private IrsVocationalSkillsLevelCertificate queryIrsVocationalSkillsLevelCertificate(IrsVocationalSkillsLevelCertificateParam irsVocationalSkillsLevelCertificateParam) {
        IrsVocationalSkillsLevelCertificate irsVocationalSkillsLevelCertificate = this.getById(irsVocationalSkillsLevelCertificateParam.getZsnum());
        if (ObjectUtil.isNull(irsVocationalSkillsLevelCertificate)) {
            throw new ServiceException(IrsVocationalSkillsLevelCertificateExceptionEnum.NOT_EXIST);
        }
        return irsVocationalSkillsLevelCertificate;
    }

}
