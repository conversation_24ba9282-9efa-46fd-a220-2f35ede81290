package com.concise.gen.notice.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 协同信息通知表参数类
 *
 * <AUTHOR>
 * @date 2023-12-08 11:38:39
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class YwxtNoticeParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 协同通知前缀
     */
    @NotBlank(message = "协同通知前缀不能为空，请检查prefix参数", groups = {add.class, edit.class})
    private String prefix;

    /**
     * 通知类型,关联消息模版id
     */
    @NotBlank(message = "通知类型,关联消息模版id不能为空，请检查noticeType参数", groups = {add.class, edit.class})
    private String noticeType;

    /**
     * 填充参数 [,]分隔
     */
    @NotBlank(message = "填充参数 [,]分隔不能为空，请检查noticeParam参数", groups = {add.class, edit.class})
    private String noticeParam;

    /**
     * 发布时间
     */
    @NotNull(message = "发布时间不能为空，请检查noticeTime参数", groups = {add.class, edit.class})
    private String noticeTime;

    private boolean sendZwddMsg;

}
