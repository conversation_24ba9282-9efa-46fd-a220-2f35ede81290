package com.concise.gen.investigationsign.param;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.concise.common.pojo.base.param.BaseParam;

import lombok.Data;

/**
 * 调查评估_手签参数类
 *
 * <AUTHOR>
 * @date 2025-04-21 20:30:30
 */
@Data
public class InvestigationSignParam extends BaseParam {

    /**
     * id
     */
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 调查评估id
     */
    @NotBlank(message = "调查评估id不能为空，请检查pid参数", groups = {add.class, edit.class})
    private String pid;

    /**
     * 用户id
     */
    private String receiveUserId;

    /**
     * 用户类型（1-浙政钉用户；2-浙里办用户）
     */
    private Integer receiveUserType;

    /**
     * 用户姓名
     */
    @NotBlank(message = "用户姓名不能为空，请检查receiveUserName参数", groups = {add.class, edit.class})
    private String receiveUserName;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 文书id
     */
    @NotBlank(message = "文书id不能为空，请检查paperId参数", groups = {add.class, edit.class})
    private String paperId;

    /**
     * 文书标题
     */
    @NotBlank(message = "文书标题不能为空，请检查title参数", groups = {add.class, edit.class})
    private String title;

    /**
     * 文书类型
     */
    @NotBlank(message = "文书类型不能为空，请检查paperType参数", groups = {add.class, edit.class})
    private String paperType;

    /**
     * 类型： 1：表示手动上传的
     */
    private String type;

    /**
     * 文书类型_中文值
     */
    private String paperTypeName;

    /**
     * 发送单位id
     */
    private String sendOrgId;

    /**
     * 发送单位
     */
    private String sendOrgName;

    /**
     * 发送时间
     */
    private String sendTime;

    /**
     * 状态（0-待签；1-已签；2-退回）
     */
    private String status;

    /**
     * 签名时间
     */
    private String signTime;

    /**
     * 签名顺序
     */
    private Integer sort;

    /**
     * 签名图片base64
     */
    private String signFileBase64;

    /**
     * 通知信息
     */
    @NotBlank(message = "通知信息不能为空，请检查noticeInfo参数", groups = {add.class, edit.class})
    private String noticeInfo;

    //笔录id
    private String blId;
}
