package com.concise.gen.papermaintenance.entity;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.gen.papertopic.entity.PaperTopic;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 量卷维护
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:55
 */
@Data
@TableName("paper_maintenance")
public class PaperMaintenance {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 量卷名称
     */
    private String title;

    /**
     * 量卷类型，字典值：LJLX
     */
    private String paperType;

    /**
     * 笔录类型，字典值：BLLX
     */
    private String blType;

    /**
     * 笔录类型名称
     */
    @Excel(name = "笔录类型", width = 30)
    private String blTypeName;

    /**
     * 状态，0：暂存 1：启用 2：禁用
     */
    @Excel(name = "状态", replace = {"暂存_0", "启用_1", "禁用_2"}, width = 20)
    private Integer status;

    /**
     * 使用单位
     */
    private String jzjg;

    /**
     * 使用单位名称
     */
    @Excel(name = "使用单位名称", width = 40)
    private String jzjgName;

    /**
     * 分数，该分数及上结论是适宜社区矫正、以下是不适宜社区矫正
     */
    @Excel(name = "分数", width = 20)
    private Integer score;

    /**
     * 删除状态， 0：未删除  1：已删除
     */
    private Integer delFlag;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 30)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 30)
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    @TableField(exist = false)
    private List<PaperTopic> topicList;
}
