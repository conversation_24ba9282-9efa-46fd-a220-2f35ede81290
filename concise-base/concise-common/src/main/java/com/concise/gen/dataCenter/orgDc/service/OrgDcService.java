package com.concise.gen.dataCenter.orgDc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.dataCenter.orgDc.entity.OrgDc;
import com.concise.gen.dataCenter.orgDc.param.OrgDcParam;
import com.concise.gen.dataCenter.orgDc.vo.CcgfUser;

import java.util.List;

/**
 * 数据中心机构表service接口
 *
 * <AUTHOR>
 * @date 2023-11-23 16:15:33
 */
public interface OrgDcService extends IService<OrgDc> {

    /**
     * 查询数据中心机构表
     * @param orgDcParam orgDcParam
     * @return OrgDc
     */
    PageResult<OrgDc> page(OrgDcParam orgDcParam);

    /**
     * 根据id获取机构code
     * @param id id
     * @return String
     */
    String getOrgCodeById(String id);

    /**
     * 获取浙政钉通知用id
     * @param orgId 所属单位
     * @return GE_xxxx
     */
    List<CcgfUser> getZwddUserList(String orgId);

    /**
     * 获取所有上级机构id和本级id
     * @param org org
     * @return ids
     */
    String getIds(OrgDc org);
}
