package com.concise.gen.dataCenter.correctplacechange.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.dataCenter.correctplacechange.entity.PlaceChangeDc;
import com.concise.gen.dataCenter.correctplacechange.param.PlaceChangeDcParam;

/**
 * 居住地变更_数据中心service接口
 *
 * <AUTHOR>
 * @date 2023-09-13 15:07:59
 */
public interface PlaceChangeDcService extends IService<PlaceChangeDc> {

    /**
     * 查询居住地变更_数据中心
     *
     * <AUTHOR>
     * @date 2023-09-13 15:07:59
     */
    PageResult<PlaceChangeDc> page(PlaceChangeDcParam placeChangeDcParam);

}
