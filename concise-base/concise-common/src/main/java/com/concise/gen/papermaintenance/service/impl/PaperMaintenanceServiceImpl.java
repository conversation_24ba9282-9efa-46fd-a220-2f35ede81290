package com.concise.gen.papermaintenance.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.papermaintenance.entity.PaperMaintenance;
import com.concise.gen.papermaintenance.enums.PaperMaintenanceExceptionEnum;
import com.concise.gen.papermaintenance.mapper.PaperMaintenanceMapper;
import com.concise.gen.papermaintenance.param.PaperMaintenanceParam;
import com.concise.gen.papermaintenance.service.PaperMaintenanceService;
import com.concise.gen.papertopic.entity.PaperTopic;
import com.concise.gen.papertopic.param.PaperTopicParam;
import com.concise.gen.papertopic.service.PaperTopicService;
import com.concise.gen.papertopicitem.entity.PaperTopicItem;
import com.concise.gen.papertopicitem.param.PaperTopicItemParam;
import com.concise.gen.papertopicitem.service.PaperTopicItemService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * 量卷维护service接口实现类
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:55
 */
@Service
public class PaperMaintenanceServiceImpl extends ServiceImpl<PaperMaintenanceMapper, PaperMaintenance> implements PaperMaintenanceService {

    private static final Logger logger = LoggerFactory.getLogger(PaperMaintenanceServiceImpl.class);

    private final PaperTopicService paperTopicService;
    private final PaperTopicItemService paperTopicItemService;

    public PaperMaintenanceServiceImpl(PaperTopicService paperTopicService, PaperTopicItemService paperTopicItemService) {
        this.paperTopicService = paperTopicService;
        this.paperTopicItemService = paperTopicItemService;
    }

    @Override
    public PageResult<PaperMaintenance> page(PaperMaintenanceParam paperMaintenanceParam) {
        QueryWrapper<PaperMaintenance> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(paperMaintenanceParam)) {
            // 量卷名称查询
            if (ObjectUtil.isNotEmpty(paperMaintenanceParam.getTitle())) {
                queryWrapper.lambda().like(PaperMaintenance::getTitle, paperMaintenanceParam.getTitle());
            }
            //机构查询
            if (CollectionUtil.isNotEmpty(paperMaintenanceParam.getJzjglist())) {
                queryWrapper.lambda().in(PaperMaintenance::getJzjg, paperMaintenanceParam.getJzjglist());
            }
            // 根据量卷类型，字典值：LJLX 查询
            if (ObjectUtil.isNotEmpty(paperMaintenanceParam.getPaperType())) {
                queryWrapper.lambda().eq(PaperMaintenance::getPaperType, paperMaintenanceParam.getPaperType());
            }
            // 根据笔录类型，字典值：BLLX 查询
            if (ObjectUtil.isNotEmpty(paperMaintenanceParam.getBlType())) {
                queryWrapper.lambda().eq(PaperMaintenance::getBlType, paperMaintenanceParam.getBlType());
            }
            // 根据笔录类型名称 查询
            if (ObjectUtil.isNotEmpty(paperMaintenanceParam.getBlTypeName())) {
                queryWrapper.lambda().eq(PaperMaintenance::getBlTypeName, paperMaintenanceParam.getBlTypeName());
            }
            // 根据状态，0：启用 1：禁用 查询
            if (ObjectUtil.isNotEmpty(paperMaintenanceParam.getStatus())) {
                queryWrapper.lambda().eq(PaperMaintenance::getStatus, paperMaintenanceParam.getStatus());
            }
        }
        queryWrapper.lambda().eq(PaperMaintenance::getDelFlag, 0);
        queryWrapper.lambda().orderByDesc(PaperMaintenance::getUpdateTime);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<PaperMaintenance> list(PaperMaintenanceParam paperMaintenanceParam) {
        return this.list();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(PaperMaintenanceParam paperMaintenanceParam) {
        // 校验题目列表不能为空
        if (CollectionUtil.isEmpty(paperMaintenanceParam.getTopicList())) {
            throw new ServiceException(PaperMaintenanceExceptionEnum.TOPIC_LIST_NOT_NULL);
        }

        // 复制并保存量卷维护主表信息
        PaperMaintenance paperMaintenance = new PaperMaintenance();
        BeanUtil.copyProperties(paperMaintenanceParam, paperMaintenance);
        paperMaintenance.setCreateTime(DateUtil.date());
        paperMaintenance.setUpdateTime(DateUtil.date());
        this.save(paperMaintenance);

        // 获取生成的主键ID
        String paperMaintenanceId = paperMaintenance.getId();

        // 保存题目和选项
        savePaperTopicsAndItems(paperMaintenanceParam.getTopicList(), paperMaintenanceId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(PaperMaintenanceParam paperMaintenanceParam) {
        this.removeById(paperMaintenanceParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(PaperMaintenanceParam paperMaintenanceParam) {
        // 校验题目列表不能为空
        if (CollectionUtil.isEmpty(paperMaintenanceParam.getTopicList())) {
            throw new ServiceException(PaperMaintenanceExceptionEnum.TOPIC_LIST_NOT_NULL);
        }

        // 获取并更新量卷维护主表信息
        PaperMaintenance paperMaintenance = this.queryPaperMaintenance(paperMaintenanceParam);
        BeanUtil.copyProperties(paperMaintenanceParam, paperMaintenance);
        this.updateById(paperMaintenance);

        // 获取量卷维护ID
        String paperMaintenanceId = paperMaintenance.getId();

        // 处理题目和选项的更新
        updatePaperTopicsAndItems(paperMaintenanceParam.getTopicList(), paperMaintenanceId);
    }

    @Override
    public PaperMaintenance detail(PaperMaintenanceParam paperMaintenanceParam) {
        // 获取量卷维护基本信息
        PaperMaintenance paperMaintenance = this.queryPaperMaintenance(paperMaintenanceParam);

        // 查询并设置题目和选项信息
        List<PaperTopic> topicList = getTopicList(paperMaintenance.getId());
        paperMaintenance.setTopicList(topicList);

        return paperMaintenance;
    }

    /**
     * 获取量卷维护
     *
     * <AUTHOR>
     * @date 2025-03-20 15:02:55
     */
    private PaperMaintenance queryPaperMaintenance(PaperMaintenanceParam paperMaintenanceParam) {
        PaperMaintenance paperMaintenance = this.getById(paperMaintenanceParam.getId());
        if (ObjectUtil.isNull(paperMaintenance)) {
            throw new ServiceException(PaperMaintenanceExceptionEnum.NOT_EXIST);
        }
        return paperMaintenance;
    }

    /**
     * 获取题目及其选项列表
     *
     * @param paperMaintenanceId 量卷维护ID
     * @return 题目列表（包含选项）
     * <AUTHOR>
     * @date 2025-03-24 10:06:35
     */
    private List<PaperTopic> getTopicList(String paperMaintenanceId) {
        // 查询量卷下的所有题目
        QueryWrapper<PaperTopic> topicQueryWrapper = new QueryWrapper<>();
        topicQueryWrapper.lambda().eq(PaperTopic::getPaperMaintenanceId, paperMaintenanceId);
        topicQueryWrapper.lambda().orderByAsc(PaperTopic::getSerialNumber); // 按排序号升序排列
        List<PaperTopic> topicList = paperTopicService.list(topicQueryWrapper);

        if (CollectionUtil.isEmpty(topicList)) {
            return new ArrayList<>();
        }

        // 为每个题目查询对应的选项
        for (PaperTopic topic : topicList) {
            // 查询题目下的所有选项
            QueryWrapper<PaperTopicItem> itemQueryWrapper = new QueryWrapper<>();
            itemQueryWrapper.lambda().eq(PaperTopicItem::getPaperTopicId, topic.getId());
            itemQueryWrapper.lambda().orderByAsc(PaperTopicItem::getSerialNumber); // 按排序号升序排列
            List<PaperTopicItem> itemList = paperTopicItemService.list(itemQueryWrapper);

            // 设置选项列表
            if (CollectionUtil.isNotEmpty(itemList)) {
                topic.setItemList(itemList);
            }
        }

        return topicList;
    }

    /**
     * 保存题目和选项
     *
     * @param topicList          题目列表
     * @param paperMaintenanceId 量卷维护ID
     * <AUTHOR>
     * @date 2025-03-24 09:51:02
     */
    private void savePaperTopicsAndItems(List<PaperTopicParam> topicList, String paperMaintenanceId) {
        if (CollectionUtil.isEmpty(topicList)) {
            return;
        }

        for (int i = 0; i < topicList.size(); i++) {
            PaperTopicParam topicParam = topicList.get(i);
            // 新增时强制ID为null
            topicParam.setId(null);
            topicParam.setPaperMaintenanceId(paperMaintenanceId);
            topicParam.setSerialNumber(i + 1);

            PaperTopic paperTopic = new PaperTopic();
            BeanUtil.copyProperties(topicParam, paperTopic);
            paperTopic.setCreateTime(DateUtil.date());
            // 只用save
            paperTopicService.save(paperTopic);

            String paperTopicId = paperTopic.getId();
            saveTopicItems(topicParam.getItemList(), paperTopicId);
        }
    }

    /**
     * 保存题目选项
     *
     * @param itemList     选项列表
     * @param paperTopicId 题目ID
     * <AUTHOR>
     * @date 2025-03-24 09:51:02
     */
    private void saveTopicItems(List<PaperTopicItemParam> itemList, String paperTopicId) {
        if (CollectionUtil.isEmpty(itemList)) {
            return;
        }

        for (int i = 0; i < itemList.size(); i++) {
            PaperTopicItemParam itemParam = itemList.get(i);
            // 新增时强制ID为null
            itemParam.setId(null);
            itemParam.setPaperTopicId(paperTopicId);
            itemParam.setSerialNumber(i + 1);

            PaperTopicItem paperTopicItem = new PaperTopicItem();
            BeanUtil.copyProperties(itemParam, paperTopicItem);
            paperTopicItem.setCreateTime(DateUtil.date());
            // 只用save
            paperTopicItemService.save(paperTopicItem);
        }
    }

    /**
     * 更新题目和选项
     *
     * @param topicList          题目列表
     * @param paperMaintenanceId 量卷维护ID
     * <AUTHOR>
     * @date 2025-03-24 09:51:02
     */
    private void updatePaperTopicsAndItems(List<PaperTopicParam> topicList, String paperMaintenanceId) {
        if (CollectionUtil.isEmpty(topicList)) {
            return;
        }

        // 查询当前量卷下的所有题目
        QueryWrapper<PaperTopic> topicQueryWrapper = new QueryWrapper<>();
        topicQueryWrapper.lambda().eq(PaperTopic::getPaperMaintenanceId, paperMaintenanceId);
        List<PaperTopic> existingTopics = paperTopicService.list(topicQueryWrapper);

        // 收集现有题目ID，用于后续判断哪些需要删除
        List<String> existingTopicIds = existingTopics.stream()
                .map(PaperTopic::getId)
                .collect(java.util.stream.Collectors.toList());

        // 收集新提交的题目ID，用于后续判断哪些需要保留
        List<String> newTopicIds = new java.util.ArrayList<>();

        // 处理每个题目，并设置排序号
        for (int i = 0; i < topicList.size(); i++) {
            PaperTopicParam topicParam = topicList.get(i);
            // 设置量卷维护ID
            topicParam.setPaperMaintenanceId(paperMaintenanceId);
            // 设置排序号，确保按列表顺序排序
            topicParam.setSerialNumber(i + 1);

            if (ObjectUtil.isEmpty(topicParam.getId())) {
                // 新增题目
                PaperTopic paperTopic = new PaperTopic();
                BeanUtil.copyProperties(topicParam, paperTopic);
                paperTopicService.save(paperTopic);

                // 保存题目选项
                saveTopicItems(topicParam.getItemList(), paperTopic.getId());
            } else {
                // 更新题目
                newTopicIds.add(topicParam.getId());

                PaperTopic paperTopic = paperTopicService.getById(topicParam.getId());
                if (ObjectUtil.isNotNull(paperTopic)) {
                    BeanUtil.copyProperties(topicParam, paperTopic);
                    paperTopicService.updateById(paperTopic);

                    // 更新题目选项
                    updateTopicItems(topicParam.getItemList(), paperTopic.getId());
                }
            }
        }

        // 删除不再存在的题目
        existingTopicIds.removeAll(newTopicIds);
        if (!existingTopicIds.isEmpty()) {
            // 删除这些题目
            paperTopicService.removeByIds(existingTopicIds);

            // 删除这些题目下的所有选项
            for (String topicId : existingTopicIds) {
                QueryWrapper<PaperTopicItem> itemQueryWrapper = new QueryWrapper<>();
                itemQueryWrapper.lambda().eq(PaperTopicItem::getPaperTopicId, topicId);
                paperTopicItemService.remove(itemQueryWrapper);
            }
        }
    }

    /**
     * 更新题目选项
     *
     * @param itemList     选项列表
     * @param paperTopicId 题目ID
     * <AUTHOR>
     * @date 2025-03-24 09:51:02
     */
    private void updateTopicItems(List<PaperTopicItemParam> itemList, String paperTopicId) {
        if (CollectionUtil.isEmpty(itemList)) {
            // 如果选项列表为空，则删除该题目下的所有选项
            QueryWrapper<PaperTopicItem> itemQueryWrapper = new QueryWrapper<>();
            itemQueryWrapper.lambda().eq(PaperTopicItem::getPaperTopicId, paperTopicId);
            paperTopicItemService.remove(itemQueryWrapper);
            return;
        }

        // 查询当前题目下的所有选项
        QueryWrapper<PaperTopicItem> itemQueryWrapper = new QueryWrapper<>();
        itemQueryWrapper.lambda().eq(PaperTopicItem::getPaperTopicId, paperTopicId);
        List<PaperTopicItem> existingItems = paperTopicItemService.list(itemQueryWrapper);

        // 收集现有选项ID，用于后续判断哪些需要删除
        List<String> existingItemIds = existingItems.stream()
                .map(PaperTopicItem::getId)
                .collect(java.util.stream.Collectors.toList());

        // 收集新提交的选项ID，用于后续判断哪些需要保留
        List<String> newItemIds = new java.util.ArrayList<>();

        // 处理每个选项，并设置排序号
        for (int i = 0; i < itemList.size(); i++) {
            PaperTopicItemParam itemParam = itemList.get(i);
            // 设置题目ID
            itemParam.setPaperTopicId(paperTopicId);
            // 设置排序号，确保按列表顺序排序
            itemParam.setSerialNumber(i + 1);

            if (ObjectUtil.isEmpty(itemParam.getId())) {
                // 新增选项
                PaperTopicItem paperTopicItem = new PaperTopicItem();
                BeanUtil.copyProperties(itemParam, paperTopicItem);
                paperTopicItemService.save(paperTopicItem);
            } else {
                // 更新选项
                newItemIds.add(itemParam.getId());

                PaperTopicItem paperTopicItem = paperTopicItemService.getById(itemParam.getId());
                if (ObjectUtil.isNotNull(paperTopicItem)) {
                    BeanUtil.copyProperties(itemParam, paperTopicItem);
                    paperTopicItemService.updateById(paperTopicItem);
                }
            }
        }

        // 删除不再存在的选项
        existingItemIds.removeAll(newItemIds);
        if (!existingItemIds.isEmpty()) {
            paperTopicItemService.removeByIds(existingItemIds);
        }
    }
    
    /**
     * 计算题目总分数
     * 支持单选、多选和不选的情况
     *
     * @param topicList 题目列表
     * @return 总分数
     * <AUTHOR>
     * @date 2025-03-26 09:49:00
     */
    @Override
    public int calculateTotalScore(List<PaperTopicParam> topicList) {
        int totalScore = 0;
        
        if (CollectionUtil.isEmpty(topicList)) {
            return totalScore;
        }
        
        for (PaperTopicParam topic : topicList) {
            // 如果题目为空，跳过
            if (topic == null) {
                continue;
            }
            
            // 根据题目类型处理不同的计分逻辑
            String userSelectId = topic.getUserSelectId();
            if (userSelectId != null && !userSelectId.isEmpty()) {
                // 选择题：根据用户选择的选项ID计算分数
                List<PaperTopicItemParam> itemList = topic.getItemList();
                if (CollectionUtil.isNotEmpty(itemList)) {
                    // 判断是否为多选题（假设多选题的ID用逗号分隔）
                    if (userSelectId.contains(",")) {
                        // 多选题处理
                        String[] selectedIds = userSelectId.split(",");
                        for (String selectedId : selectedIds) {
                            if (selectedId != null && !selectedId.trim().isEmpty()) {
                                // 找到对应选项并累加分数
                                for (PaperTopicItemParam item : itemList) {
                                    if (selectedId.trim().equals(item.getId())) {
                                        totalScore += item.getItemScore();
                                        break;
                                    }
                                }
                            }
                        }
                    } else {
                        // 单选题处理
                        for (PaperTopicItemParam item : itemList) {
                            if (userSelectId.equals(item.getId())) {
                                totalScore += item.getItemScore();
                                break;
                            }
                        }
                    }
                }
            } else if (topic.getUserAnswer() != null && !topic.getUserAnswer().isEmpty()) {
                // 填空题或主观题：直接使用题目分数
                totalScore += topic.getTopicScore();
            }
            // 如果既没有选择也没有填写答案，则不计分
        }
        
        return totalScore;
    }
}
