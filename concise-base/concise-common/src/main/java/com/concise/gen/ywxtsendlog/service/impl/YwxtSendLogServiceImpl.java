package com.concise.gen.ywxtsendlog.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.gen.ywxtsendlog.entity.YwxtSendLog;
import com.concise.gen.ywxtsendlog.mapper.YwxtSendLogMapper;
import com.concise.gen.ywxtsendlog.service.YwxtSendLogService;
import org.springframework.stereotype.Service;

/**
 * ws发送日志service接口实现类
 *
 * <AUTHOR>
 * @date 2024-11-18 13:22:13
 */
@Service
public class YwxtSendLogServiceImpl extends ServiceImpl<YwxtSendLogMapper, YwxtSendLog> implements YwxtSendLogService {

    @Override
    public String add(String typeName, String correctionObject, String deptName, String dataId, String sendOrgName, String sendOrgId, String sendOrgPids, String receiveOrgName, String receiveOrgType) {
        YwxtSendLog ywxtSendLog = new YwxtSendLog();
        String taskId = IdUtil.fastSimpleUUID();
        ywxtSendLog.setTaskId(taskId);
        ywxtSendLog.setOpTime(DateUtil.date());
        ywxtSendLog.setSuccess(1);
        ywxtSendLog.setTypeName(typeName);
        ywxtSendLog.setCorrectionObject(correctionObject);
        ywxtSendLog.setDeptName(deptName);
        ywxtSendLog.setDataId(dataId);
        ywxtSendLog.setSendOrgName(sendOrgName);
        ywxtSendLog.setSendOrgId(sendOrgId);
        ywxtSendLog.setSendOrgPids(sendOrgPids);
        ywxtSendLog.setReceiveOrgName(receiveOrgName);
        ywxtSendLog.setReceiveOrgType(receiveOrgType);
        save(ywxtSendLog);
        return taskId;
    }

    @Override
    public void fail(String taskId) {
        this.lambdaUpdate().set(YwxtSendLog::getSuccess, 0).eq(YwxtSendLog::getTaskId, taskId).update();
    }
}
