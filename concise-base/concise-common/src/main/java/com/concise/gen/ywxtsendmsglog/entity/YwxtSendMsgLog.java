package com.concise.gen.ywxtsendmsglog.entity;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 消息发送日志
 *
 * <AUTHOR>
 * @date 2025-02-27 14:26:45
 */
@Data
@NoArgsConstructor
@TableName("ywxt_send_msg_log")
public class YwxtSendMsgLog{

    /**  主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private String taskId;

    /**  操作时间 */
    private Date opTime;

    /**  类别 */
    private Integer success;

    /**  类别 */
    private String typeName;

    /**  发送单位 */
    private String sendOrg;

    /**  接收单位 */
    private String receiveOrg;

    /**   */
    private String param;

    /**   */
    private String result;

    public YwxtSendMsgLog(String taskId, Integer success, String typeName, String sendOrg, String receiveOrg, String param, String result) {
        this.taskId = taskId;
        this.opTime = DateUtil.date();
        this.success = success;
        this.typeName = typeName;
        this.sendOrg = sendOrg;
        this.receiveOrg = receiveOrg;
        this.param = param;
        this.result = result;
    }

}
