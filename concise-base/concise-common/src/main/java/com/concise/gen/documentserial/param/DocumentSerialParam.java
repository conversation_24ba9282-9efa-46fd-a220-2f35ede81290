package com.concise.gen.documentserial.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 文书号维护参数类
 *
 * <AUTHOR>
 * @date 2025-03-27 10:09:11
*/
@Data
public class DocumentSerialParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 文书类型，字典值：WSLX
     */
    private String type;

    /**
     * 市、县区机构id
     */
    private String deptId;

    /**  市+区/县简称 */
    private String sName;

    /**
     * 当前年份
     */
    private Integer currentYear;

    /**
     * 序列号
     */
    private Integer serialNumber;

    /**  业务信息id */
    private String referId;

}
