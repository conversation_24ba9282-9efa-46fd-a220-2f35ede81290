package com.concise.gen.dataCenter.correctionterminate.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 矫正对象解矫列表参数类
 *
 * <AUTHOR>
 * @date 2022-06-17 15:09:52
*/
@Data
public class CorrectionTerminateParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空，请检查zhuangtai参数", groups = {add.class, edit.class})
    private String zhuangtai;

    /**
     * 状态中文值
     */
    @NotBlank(message = "状态中文值不能为空，请检查zhuangtaiName参数", groups = {add.class, edit.class})
    private String zhuangtaiName;

    /**
     * 终止日期
     */
    @NotNull(message = "终止日期不能为空，请检查zhongzhiriqi参数", groups = {add.class, edit.class})
    private String zhongzhiriqi;

    /**
     * 最后修改时间
     */
    @NotNull(message = "最后修改时间不能为空，请检查lastModifiedTime参数", groups = {add.class, edit.class})
    private String lastModifiedTime;



    private String zzrqs;
    private String zzrqe;
}
