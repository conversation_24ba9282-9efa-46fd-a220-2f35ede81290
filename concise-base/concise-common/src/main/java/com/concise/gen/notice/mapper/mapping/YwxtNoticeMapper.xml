<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.notice.mapper.YwxtNoticeMapper">
    <insert id="insertNoticeUserIdList">
        insert into ywxt_notice_user (notice_id,notice_type, user_id) values
        <foreach collection="userIdList" item="userId" separator=",">
            (#{noticeId}, #{noticeType}, #{userId})
        </foreach>
    </insert>
    <insert id="insertNoticeOrgIdList">
        insert into ywxt_notice_org (notice_id,notice_type, org_id) values
        <foreach collection="orgIdList" item="orgId" separator=",">
            (#{noticeId}, #{noticeType}, #{orgId})
        </foreach>
    </insert>

    <select id="getAllTypeLatestNoticeList" resultType="com.concise.gen.notice.entity.YwxtNotice">
        select ywxt_notice.*, ywxt_notice_user.id as notice_user_id
        from ywxt_notice_user
                 left join ywxt_notice on ywxt_notice.id = ywxt_notice_user.notice_id
        where id in (
            select max(id) as id
            from ywxt_notice_user
            where user_id = #{param1}
            group notice_type
        )
    </select>
    <select id="getLastNoticeList" resultType="com.concise.gen.notice.entity.YwxtNotice">
        select ywxt_notice.*, ywxt_notice_user.id as notice_user_id
        from ywxt_notice_user
                 left join ywxt_notice on ywxt_notice.id = ywxt_notice_user.notice_id
        where user_id = #{param1}
        order by ywxt_notice_user.id desc
            limit #{param2}
    </select>
    <select id="getLastNoticeListByOrgId" resultType="com.concise.gen.notice.entity.YwxtNotice">
        select *
        from ywxt_notice
        where id in (
            select notice_id
            from ywxt_notice_org
            where
                  (org_id in (select id from sys_org where pids like concat('%',#{orgId},'%') or id = #{orgId}) and notice_type != 'terminate_01')
               or (org_id = #{orgId} and notice_type = 'terminate_01')
        )
        order by notice_time desc
        limit #{size}
    </select>
</mapper>
