package com.concise.gen.papertopic.param;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.papertopicitem.param.PaperTopicItemParam;

import lombok.Data;

/**
* 量卷维护-题目参数类
 *
 * <AUTHOR>
 * @date 2025-03-20 15:03:03
*/
@Data
public class PaperTopicParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 量卷主键
     */
    @NotBlank(message = "量卷主键不能为空，请检查paperMaintenanceId参数", groups = {add.class, edit.class})
    private String paperMaintenanceId;

    /**
     * 问题名称
     */
    @NotBlank(message = "问题名称不能为空，请检查topicName参数", groups = {add.class, edit.class})
    private String topicName;

    /**
     * 指标
     */
    @NotBlank(message = "指标不能为空，请检查indexName参数", groups = {add.class, edit.class})
    private String indexName;

    /**
     * 问题类型
     */
    private String topicType;

    /**
     * 分数
     */
    @NotNull(message = "分数不能为空，请检查topicScore参数", groups = {add.class, edit.class})
    private Integer topicScore;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空，请检查remark参数", groups = {add.class, edit.class})
    private String remark;

    /**
     * 排序号
     */
    @NotNull(message = "排序号不能为空，请检查serialNumber参数", groups = {add.class, edit.class})
    private Integer serialNumber;

    /**
     * 指标选项列表
     */
    private List<PaperTopicItemParam> itemList;

    /**
     * 用户回答
     */
    @TableField(exist = false)
    private String userAnswer;

    /**
     * 用户选择
     */
    @TableField(exist = false)
    private String userSelectId;
}
