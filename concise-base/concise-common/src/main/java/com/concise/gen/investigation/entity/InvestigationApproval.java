package com.concise.gen.investigation.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.file.param.SysFileInfoVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 调查评估_审批
 *
 * <AUTHOR>
 * @date 2025-03-24 14:59:22
 */
@Data
@TableName("investigation_p5_approval")
public class InvestigationApproval{

    /**  主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**  调查对象姓名 */
    private String correctionObjName;

    /**  审核结果 */
    private String approvalResult;

    /**  审核意见 */
    private String approvalRemark;

    /**  审核人 */
    private String approvalPsn;

    /**  审核时间 */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    /**  调查评估文书号 */
    private String docNum;

    /**  调查评估结论 */
    private String conclusion;

    /**  调查评估情况 */
    private String particular;

    /**  评估开始(收到委托)日期 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date entrustmentReceiveTime;

    /**  调查结束时间 */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**  是否删除 */
    private Integer deleted;

    /**
     * 集体评议意见表 */
    @TableField(exist = false)
    private List<SysFileInfoVO> opinionFilesList;
    @TableField(exist = false)
    private List<SysFileInfoVO> evaluationFiles;

}
