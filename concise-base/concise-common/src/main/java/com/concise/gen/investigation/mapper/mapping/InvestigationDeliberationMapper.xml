<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.investigation.mapper.InvestigationDeliberationMapper">
    <insert id="saveHistory">
        insert into investigation_history_field
        (id, dept_id, type, host, address, psn_list, recorder, deleted)
        values
        (#{model.id}, #{model.deptId}, #{model.type}, #{model.host}, #{model.address}, #{model.psnList}, #{model.recorder}, 0)
    </insert>

    <delete id="deleteHistory">
        update investigation_history_field set deleted = 1 where id = #{id}
    </delete>

    <select id="historyList" resultType="com.concise.gen.investigation.entity.InvestigationHistoryField">
        select * from investigation_history_field
        where dept_id = #{deptId} and type = #{type} and deleted = 0
    </select>
</mapper>
