package com.concise.gen.ywxtsendmsglog.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.gen.ywxtsendmsglog.entity.YwxtSendMsgLog;
import com.concise.gen.ywxtsendmsglog.mapper.YwxtSendMsgLogMapper;
import com.concise.gen.ywxtsendmsglog.service.YwxtSendMsgLogService;
import org.springframework.stereotype.Service;

/**
 * 消息发送日志service接口实现类
 *
 * <AUTHOR>
 * @date 2025-02-27 14:26:45
 */
@Service
public class YwxtSendMsgLogServiceImpl extends ServiceImpl<YwxtSendMsgLogMapper, YwxtSendMsgLog> implements YwxtSendMsgLogService {

}
