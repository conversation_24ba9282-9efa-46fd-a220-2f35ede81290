package com.concise.gen.papermaintenance.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.papermaintenance.entity.PaperMaintenance;
import com.concise.gen.papermaintenance.param.PaperMaintenanceParam;
import com.concise.gen.papertopic.param.PaperTopicParam;

/**
 * 量卷维护service接口
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:55
 */
public interface PaperMaintenanceService extends IService<PaperMaintenance> {

    /**
     * 查询量卷维护
     *
     * <AUTHOR>
     * @date 2025-03-20 15:02:55
     */
    PageResult<PaperMaintenance> page(PaperMaintenanceParam paperMaintenanceParam);

    /**
     * 量卷维护列表
     *
     * <AUTHOR>
     * @date 2025-03-20 15:02:55
     */
    List<PaperMaintenance> list(PaperMaintenanceParam paperMaintenanceParam);

    /**
     * 添加量卷维护
     *
     * <AUTHOR>
     * @date 2025-03-20 15:02:55
     */
    void add(PaperMaintenanceParam paperMaintenanceParam);

    /**
     * 删除量卷维护
     *
     * <AUTHOR>
     * @date 2025-03-20 15:02:55
     */
    void delete(PaperMaintenanceParam paperMaintenanceParam);

    /**
     * 编辑量卷维护
     *
     * <AUTHOR>
     * @date 2025-03-20 15:02:55
     */
    void edit(PaperMaintenanceParam paperMaintenanceParam);

    /**
     * 查看量卷维护
     *
     * <AUTHOR>
     * @date 2025-03-20 15:02:55
     */
     PaperMaintenance detail(PaperMaintenanceParam paperMaintenanceParam);
     
    /**
     * 计算题目总分数
     *
     * @param topicList 题目列表
     * @return 总分数
     * <AUTHOR>
     * @date 2025-03-26 09:49:00
     */
    int calculateTotalScore(List<PaperTopicParam> topicList);
}
