package com.concise.gen.petitionletterinfo.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.concise.gen.dataCenter.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.petitionletterinfo.param.DecryptParamDto;
import com.concise.gen.petitionletterinfo.param.EncrytMsgDto;

import java.util.List;
import java.util.Random;

/**
 * 信访接口工具类
 *
 * <AUTHOR>
 */
public class LetterUtil {

    /**test*/
    //private static final String URL = "59.202.41.118:80";
    //private static final String TOKEN = "m8gqvzoiap9nfnfop8e8rkgq7sxh9y5n";
    //private static final String ENCODING_AES_KEY = "sym2w9x8b7w9zja2wkk41a68dlo0b91i2dg3cji7510";
    //private static final String THIRD_PART_ID = "sfsqjzApp";
    //private static final String APP_SECRET = "yjm62toah0zsxdqjptta63unrpja7dt7";

    /**prod*/
    private static final String URL = "http://************:8310/service/api";
    private static final String TOKEN = "faf5kfzh356d35mwrrt0w3x0cbmmn2fi";
    private static final String ENCODING_AES_KEY = "pzp4iahfek5hw00heipw3ft7ik354iezk0035rbppmt";
    private static final String THIRD_PART_ID = "ssfjzAppProd";
    private static final String APP_SECRET = "8yc10z0w4r1bymkhp2f4p652ifycjth9";

    public static void main(String[] args) {
        String accessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRJZCI6InNmc3FqekFwcCIsImV4cCI6MTcxMjEzODQ0Mn0.5PBgdNxQhDD57tWs1yoGluLLkqqGCwvW9TmRy94TkwY";

        //JSONObject jsonObject = new JSONObject();
        //jsonObject.put("1",1);
        //EncrytMsgDto encrytMsgDto = encryptionParam(jsonObject);
        //
        //String params = JSON.toJSONString(encrytMsgDto);
        //System.out.println(params);
        //Object o = decryptParam(params);
        //System.out.println(o);
        String s = receiveLetter(createAccessToken(),"", "2023-05-01", "2024-04-30", "1", "5");
        System.out.println(s);

    }

    public static Boolean sendPerson(String accessToken,
                                     String deptCode,
                                     List<CorrectionObjectInformation> psnList) {

        JSONArray arr = new JSONArray();
        for (CorrectionObjectInformation psn : psnList) {
            JSONObject param = new JSONObject();
            param.put("accessToken", accessToken);
            param.put("psnId", psn.getId());
            param.put("psnName", psn.getXm());
            param.put("correctionDate", DateUtil.formatDate(psn.getRujiaoriqi()));
            param.put("idCode", psn.getSfzh());
            param.put("deptCode", deptCode);
            param.put("correctionStartDate", DateUtil.formatDate(psn.getSqjzksrq()));
            param.put("correctionEndDate", DateUtil.formatDate(psn.getSqjzjsrq()));
            param.put("correctionStatus", "200".equals(psn.getZhuangtai())?"1":"0");
            arr.add(param);
        }
        JSONObject result = post("/xfpt/justice/person-receive", arr);
        return result.getBoolean("data");
    }


    public static Boolean sendPerson(String accessToken,
                                     String deptCode,
                                     CorrectionObjectInformation psn) {
        JSONArray arr = new JSONArray();
        JSONObject param = new JSONObject();
        param.put("accessToken", accessToken);
        param.put("psnId", psn.getId());
        param.put("psnName", psn.getXm());
        param.put("correctionDate", DateUtil.formatDate(psn.getRujiaoriqi()));
        param.put("idCode", psn.getSfzh());
        param.put("deptCode", deptCode);
        param.put("correctionStartDate", DateUtil.formatDate(psn.getSqjzksrq()));
        param.put("correctionEndDate", DateUtil.formatDate(psn.getSqjzjsrq()));
        param.put("correctionStatus", "200".equals(psn.getZhuangtai())?"1":"0");
        arr.add(param);
        JSONObject result = post("/xfpt/justice/person-receive", arr);
        return result.getBoolean("data");
    }

    public static String receiveLetter(
            String accessToken,
            String letterNo,
            String startTime,
            String endTime,
            String pageNum,
            String pageSize) {
        JSONObject param = new JSONObject();
        param.put("accessToken", accessToken);
        param.put("letterNo", letterNo);
        //"yyyy-MM-dd"
        param.put("startTime", startTime);
        param.put("endTime", endTime);
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        JSONObject result = post("/xfpt/justice/letter-receive", param);
        decryptParam(result.getString("data"));
        return decryptParam(result.getString("data")).toJSONString();
    }

    /**
     * 获取accessToken
     * 4小时过期
     */
    public static String createAccessToken() {
        String url = URL + "/xfpt/zjxftyptAPI/query/createAccessToken";
        JSONObject param = new JSONObject();
        param.put("clientId", THIRD_PART_ID);
        param.put("clientSecret", APP_SECRET);
        String result = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .body(JSON.toJSONString(param))
                .execute().body();
        JSONObject object = JSON.parseObject(result);
        if (object != null) {
            return object.getString("accessToken");
        }
        return result;
    }

    public static EncrytMsgDto encryptionParam(Object obj) {
        // 获取系统时间
        String timestamp = String.valueOf(System.currentTimeMillis());
        // 生成10位随机数
        String nonce = generateRandomNumber(10);
        // 请求体数据转化
        String params = JSON.toJSONString(obj);
        // 加密/解密工具类
        EncrytMsgDto encryptObj = new EncrytMsgDto();
        try {
            VisitBizJsonMsgCrypt visitCrpt = new VisitBizJsonMsgCrypt(TOKEN, ENCODING_AES_KEY);
            // 获取加密字段
            String encryptMsg = visitCrpt.EncryptMsg(params, timestamp, nonce);
            encryptObj = JSON.parseObject(encryptMsg, EncrytMsgDto.class);
        } catch (AesException e) {
            e.printStackTrace();
        }
        return encryptObj;
    }

    public static String generateRandomNumber(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            int randomNumber = random.nextInt(10);
            sb.append(randomNumber);
        }

        return sb.toString();
    }

    public static JSONObject decryptParam(String params) {
        EncrytMsgDto encryptObj = JSON.parseObject(params, EncrytMsgDto.class);
        DecryptParamDto decryptDto = new DecryptParamDto();
        decryptDto.setThirdPartId(THIRD_PART_ID);
        decryptDto.setEncrypt(encryptObj.getEncrypt());
        String postData = JSON.toJSONString(decryptDto);
        JSONObject result = null;
        try {
            VisitBizJsonMsgCrypt visitCrpt = new VisitBizJsonMsgCrypt(TOKEN, ENCODING_AES_KEY);
            String sMsg = visitCrpt.DecryptMsg(encryptObj.getMsgsignature(), encryptObj.getTimestamp(),
                    encryptObj.getNonce(), postData);
            result = JSON.parseObject(sMsg);
        } catch (AesException e) {
            e.printStackTrace();
        }
        return result;
    }

    private static JSONObject post(String url, Object param) {
        EncrytMsgDto encrytMsgDto = encryptionParam(param);
        JSONObject encrytParam = new JSONObject();
        encrytParam.put("thirdPartId", THIRD_PART_ID);
        encrytParam.put("encrypt", encrytMsgDto.getEncrypt());

        String finalUrl = URL + url + "/" + encrytMsgDto.getMsgsignature() + "/" + encrytMsgDto.getTimestamp() + "/" + encrytMsgDto.getNonce();
        String body = encrytParam.toJSONString();
        String result = HttpRequest.post(finalUrl)
                .header("Content-Type", "application/json")
                .body(body)
                .execute().body();
        return JSONObject.parseObject(result);
    }
}
