package com.concise.gen.investigationsigncommonuser.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 调查评估_手签_常用人员
 *
 * <AUTHOR>
 * @date 2025-04-22 11:33:29
 */

@Data
@TableName("investigation_sign_common_user")
public class InvestigationSignCommonUser {

    /**  id */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**  姓名 */
    private String receiveUserName;

    /**  联系电话 */
    private String phone;

    /**  常用单位id */
    private String sendOrgId;

    /**  常用单位 */
    private String sendOrgName;

}
