package com.concise.gen.paperquestionitem.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.paperquestionitem.entity.PaperQuestionItem;
import com.concise.gen.paperquestionitem.param.PaperQuestionItemParam;
import java.util.List;

/**
 * 题库-指标选项service接口
 *
 * <AUTHOR>
 * @date 2025-03-20 15:03:00
 */
public interface PaperQuestionItemService extends IService<PaperQuestionItem> {

    /**
     * 查询题库-指标选项
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:00
     */
    PageResult<PaperQuestionItem> page(PaperQuestionItemParam paperQuestionItemParam);

    /**
     * 题库-指标选项列表
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:00
     */
    List<PaperQuestionItem> list(PaperQuestionItemParam paperQuestionItemParam);

    /**
     * 添加题库-指标选项
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:00
     */
    void add(PaperQuestionItemParam paperQuestionItemParam);

    /**
     * 删除题库-指标选项
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:00
     */
    void delete(PaperQuestionItemParam paperQuestionItemParam);

    /**
     * 编辑题库-指标选项
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:00
     */
    void edit(PaperQuestionItemParam paperQuestionItemParam);

    /**
     * 查看题库-指标选项
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:00
     */
     PaperQuestionItem detail(PaperQuestionItemParam paperQuestionItemParam);
}
