package com.concise.gen.petitionletterinfo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 信访信息
 *
 * <AUTHOR>
 * @date 2024-04-02 14:11:20
 */
@Data
@TableName("petition_letter_info")
public class PetitionLetterInfo{

    /**  主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**  信访件编号 */
    private String letterNo;

    /**  姓名 */
    @TableField(value = "NAME_")
    @Excel(name = "姓名", width = 20)
    private String name;

    /**  矫正单位 */
    @Excel(name = "矫正单位", width = 20 ,orderNum = "1")
    private String deptName;
    private String deptIds;

    /**  联系电话 */
    private String phone;

    /**  证件类型 */
    private String idType;

    /**  证件类型 */
    private String idTypeName;

    /**  证件号码 */
    private String idNumber;

    /**  信访时间 */
    @Excel(name = "信访时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", width = 20,orderNum = "4")
    private Date letterTime;

    /**  信访类型 */
    private String letterType;

    /**  信访类型 */
    @Excel(name = "信访类型", width = 20 ,orderNum = "2")
    private String letterTypeName;

    /**  信访渠道 */
    private String letterChannel;

    /**  信访渠道 */
    @Excel(name = "信访渠道", width = 20 ,orderNum = "3")
    private String letterChannelName;

    /**  是否异地信访 */
    private String isOffsite;
    @TableField(exist = false)
    @Excel(name = "是否异地信访", width = 20 ,orderNum = "5")
    private String offsite;
    public String getOffsite() {
        if ("1".equals(this.isOffsite)) {
            return "是";
        }else {
            return "否";
        }
    }

    @Excel(name = "信访时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", width = 20,orderNum = "6")
    private Date updateTime;

    private String xbName;
    private String jzlbName;
    private String sqjzksrq;
    private String sqjzjsrq;
    private String letterContext;
    private String zhuangtai;
}
