package com.concise.gen.dataCenter.orgDc.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 数据中心机构表参数类
 *
 * <AUTHOR>
 * @date 2023-11-23 16:15:33
*/
@Data
public class OrgDcParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 父id
     */
    @NotBlank(message = "父id不能为空，请检查pid参数", groups = {add.class, edit.class})
    private String pid;

    /**
     * 父ids
     */
    @NotBlank(message = "父ids不能为空，请检查pids参数", groups = {add.class, edit.class})
    private String pids;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空，请检查name参数", groups = {add.class, edit.class})
    private String name;

    /**
     * 编码
     */
    @NotBlank(message = "编码不能为空，请检查code参数", groups = {add.class, edit.class})
    private String code;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空，请检查sort参数", groups = {add.class, edit.class})
    private Integer sort;

    /**
     *
     */
    @NotBlank(message = "不能为空，请检查type参数", groups = {add.class, edit.class})
    private String type;

    /**
     * 描述
     */
    @NotBlank(message = "描述不能为空，请检查remark参数", groups = {add.class, edit.class})
    private String remark;

    /**
     * 状态（字典 0正常 1停用 2删除）
     */
    @NotNull(message = "状态（字典 0正常 1停用 2删除）不能为空，请检查status参数", groups = {add.class, edit.class})
    private Integer status;

    /**
     * 机构完整路径
     */
    @NotBlank(message = "机构完整路径不能为空，请检查fullName参数", groups = {add.class, edit.class})
    private String fullName;

    /**
     * 1省级，2市级，3区县级 4所街道
     */
    @NotNull(message = "1省级，2市级，3区县级 4所街道不能为空，请检查level参数", groups = {add.class, edit.class})
    private Integer level;

    /**
     *
     */
    @NotNull(message = "不能为空，请检查adcode参数", groups = {add.class, edit.class})
    private Integer adcode;

}
