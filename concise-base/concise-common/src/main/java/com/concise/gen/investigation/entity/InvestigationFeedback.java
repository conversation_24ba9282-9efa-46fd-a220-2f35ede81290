package com.concise.gen.investigation.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 调查评估_反馈
 *
 * <AUTHOR>
 * @date 2025-03-24 14:59:23
 */
@Data
@TableName("investigation_p6_feedback")
public class InvestigationFeedback{

    /**  主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**  委托类别(字典) */
    private String entrustmentType;

    /**  统一赋号 */
    private String uniformCode;

    /**  案件标识 */
    private String caseCode;

    /**  案件名称 */
    private String caseName;

    /**  调查机构 */
    private String inveDept;

    /**  调查单位联系人 */
    private String inveDeptContactPsn;

    /**  调查单位联系电话 */
    private String inveDeptTel;

    /**  调查评估文书号 */
    private String docNum;

    /**  调查评估结论 */
    private String conclusion;

    /**  调查评估情况 */
    private String particular;

    /**  调查评估日期 */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date inveTime;

    /**  调查过程信息 jsonArray */
    private String processInfo;

    /**  评估开始(收到委托)日期 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date entrustmentReceiveTime;

    /**  调查结束时间 */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**  住所地 */
    private String residenceCode;

    /**  住所地详细地址 */
    private String residence;

    /**  监护人姓名 */
    private String guardianName;

    /**  监护人与罪犯关系 */
    private String guardianRelationship;

    /**  反馈委托单位 */
    private String feedbackTo;

    /**  抄送单位 */
    private String ccTo;

    /**  反馈人 */
    private String feedbackPsn;

    /**  反馈时间 */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTime;

    /**  反馈说明 */
    private String remark;

    /**  是否删除 */
    private Integer deleted;


    private String inveItem;
    private String inveTargetName;
    private String inveTargetRelationship;
    private String inveAddress;

}
