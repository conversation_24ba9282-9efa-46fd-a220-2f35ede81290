package com.concise.gen.ywxtmessagelog.service;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ywxtmessagelog.entity.YwxtMessageLog;
import com.concise.gen.ywxtmessagelog.param.YwxtMessageLogParam;

/**
 * 一体化消息日志service接口
 *
 * <AUTHOR>
 * @date 2024-10-11 17:07:24
 */
public interface YwxtMessageLogService extends IService<YwxtMessageLog> {

    /**
     * 查询一体化消息日志
     *
     * <AUTHOR>
     * @date 2024-10-11 17:07:24
     */
    PageResult<YwxtMessageLog> page(YwxtMessageLogParam ywxtMessageLogParam);

    JSONArray detailByDataId(String dataId);

    /**
     * 删除一体化消息日志
     *
     * <AUTHOR>
     * @date 2024-10-11 17:07:24
     */
    void delete(YwxtMessageLogParam ywxtMessageLogParam);

}
