package com.concise.gen.revocationauditphase.constant;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 */
public interface RevocationPhaseVar {
    JSONObject PROCESS_DEFINITION_KEY = new JSONObject() {{
        put("1", "sttqcx");
        put("2", "dstqcx");
        put("3", "qxtqcx");
    }};
    JSONObject PROCESS_DEFINITION = new JSONObject() {{
        put("1", "xz,sh,sh1,sp1,sh2,sp2,sh3,sp3,zqjcjy,jsjcjy,tqcx,cljg");
        put("2", "xz,sh,sh1,sp1,sh2,sp2,zqjcjy,jsjcjy,tqcx,cljg");
        put("3", "xz,sh,sh1,sp1,zqjcjy,jsjcjy,tqcx,cljg");
    }};
}
