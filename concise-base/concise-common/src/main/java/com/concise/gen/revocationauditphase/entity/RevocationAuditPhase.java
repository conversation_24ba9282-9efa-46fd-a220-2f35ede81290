package com.concise.gen.revocationauditphase.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 提请撤销审核阶段表
 *
 * <AUTHOR>
 * @date 2024-02-29 14:41:31
 */
@Data
@TableName("revocation_audit_phase")
public class RevocationAuditPhase{

    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 阶段
     */
    private String auditPhase;
    private String delFlag = "0";

    /**
     *
     */
    private String contactId;

    /**
     * 初审结果
     */
    private String csjg;

    /**
     * 初审人员
     */
    private String csry;

    /**
     * 初审时间
     */
    private Date cssj;

    /**
     * 初审意见
     */
    private String csyj;

    /**
     * 审核事项
     */
    private String shsx;

    /**
     * 主持人
     */
    private String zcr;

    /**
     * 审核地点
     */
    private String shdd;

    /**
     * 审核时间
     */
    private Date shsj;

    /**
     * 审核人员
     */
    private String shry;

    /**
     * 记录人
     */
    private String jlr;

    /**
     * 评议审核情况
     */
    private String pyshqk;

    /**
     * 评议审核意见
     */
    private String pyshyj;

    /**
     * 负责人
     */
    private String fzr;

    /**
     * 备注
     */
    private String bz;

    /**
     * 审核意见表
     */
    private String file1;

    /**
     * 撤销审核表
     */
    private String file2;

}
