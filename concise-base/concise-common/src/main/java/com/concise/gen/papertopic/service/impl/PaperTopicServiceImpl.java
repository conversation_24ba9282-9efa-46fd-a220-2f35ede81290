package com.concise.gen.papertopic.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.papertopic.entity.PaperTopic;
import com.concise.gen.papertopic.enums.PaperTopicExceptionEnum;
import com.concise.gen.papertopic.mapper.PaperTopicMapper;
import com.concise.gen.papertopic.param.PaperTopicParam;
import com.concise.gen.papertopic.service.PaperTopicService;
import com.concise.gen.papertopicitem.entity.PaperTopicItem;
import com.concise.gen.papertopicitem.service.PaperTopicItemService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * 量卷维护-题目service接口实现类
 *
 * <AUTHOR>
 * @date 2025-03-20 15:03:03
 */
@Service
public class PaperTopicServiceImpl extends ServiceImpl<PaperTopicMapper, PaperTopic> implements PaperTopicService {

    private final PaperTopicItemService paperTopicItemService;

    public PaperTopicServiceImpl(PaperTopicItemService paperTopicItemService) {
        this.paperTopicItemService = paperTopicItemService;
    }

    @Override
    public PageResult<PaperTopic> page(PaperTopicParam paperTopicParam) {
        QueryWrapper<PaperTopic> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(paperTopicParam)) {

            // 根据量卷主键 查询
            if (ObjectUtil.isNotEmpty(paperTopicParam.getPaperMaintenanceId())) {
                queryWrapper.lambda().eq(PaperTopic::getPaperMaintenanceId, paperTopicParam.getPaperMaintenanceId());
            }
            // 根据问题名称 查询
            if (ObjectUtil.isNotEmpty(paperTopicParam.getTopicName())) {
                queryWrapper.lambda().eq(PaperTopic::getTopicName, paperTopicParam.getTopicName());
            }
            // 根据指标 查询
            if (ObjectUtil.isNotEmpty(paperTopicParam.getIndexName())) {
                queryWrapper.lambda().eq(PaperTopic::getIndexName, paperTopicParam.getIndexName());
            }
            // 根据分数 查询
            if (ObjectUtil.isNotEmpty(paperTopicParam.getTopicScore())) {
                queryWrapper.lambda().eq(PaperTopic::getTopicScore, paperTopicParam.getTopicScore());
            }
            // 根据备注 查询
            if (ObjectUtil.isNotEmpty(paperTopicParam.getRemark())) {
                queryWrapper.lambda().eq(PaperTopic::getRemark, paperTopicParam.getRemark());
            }
            // 根据排序号 查询
            if (ObjectUtil.isNotEmpty(paperTopicParam.getSerialNumber())) {
                queryWrapper.lambda().eq(PaperTopic::getSerialNumber, paperTopicParam.getSerialNumber());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<PaperTopic> list(PaperTopicParam paperTopicParam) {
        return this.list();
    }

    @Override
    public void add(PaperTopicParam paperTopicParam) {
        PaperTopic paperTopic = new PaperTopic();
        BeanUtil.copyProperties(paperTopicParam, paperTopic);
        this.save(paperTopic);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(PaperTopicParam paperTopicParam) {
        this.removeById(paperTopicParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(PaperTopicParam paperTopicParam) {
        PaperTopic paperTopic = this.queryPaperTopic(paperTopicParam);
        BeanUtil.copyProperties(paperTopicParam, paperTopic);
        this.updateById(paperTopic);
    }

    @Override
    public PaperTopic detail(PaperTopicParam paperTopicParam) {
        return this.queryPaperTopic(paperTopicParam);
    }

    /**
     * 获取量卷维护-题目
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:03
     */
    private PaperTopic queryPaperTopic(PaperTopicParam paperTopicParam) {
        PaperTopic paperTopic = this.getById(paperTopicParam.getId());
        if (ObjectUtil.isNull(paperTopic)) {
            throw new ServiceException(PaperTopicExceptionEnum.NOT_EXIST);
        }
        return paperTopic;
    }

    @Override
    public void batchUpdateTopicScore() {
        //批量遍历题目里面topicScore为空的题目，选项如果有分数，则取最高分，更新topicScore
        List<PaperTopic> paperTopics = this.list(new QueryWrapper<PaperTopic>().lambda().isNull(PaperTopic::getTopicScore));
        for (PaperTopic paperTopic : paperTopics) {
            this.updateTopicScore(paperTopic);
        }
    }
    
    /**
     * 更新题目分数为选项中的最高分
     *
     * @param paperTopic 题目对象
     * <AUTHOR>
     * @date 2025-03-27 10:05:00
     */
    private void updateTopicScore(PaperTopic paperTopic) {
        // 如果题目分数已经存在，则不处理
        if (ObjectUtil.isNotNull(paperTopic.getTopicScore()) && paperTopic.getTopicScore() > 0) {
            return;
        }
        
        // 查询题目的所有选项
        QueryWrapper<PaperTopicItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PaperTopicItem::getPaperTopicId, paperTopic.getId());
        List<PaperTopicItem> itemList = paperTopicItemService.list(queryWrapper);
        
        // 如果没有选项或选项列表为空，则跳过
        if (CollectionUtil.isEmpty(itemList)) {
            return;
        }
        
        // 找出选项中的最高分
        Integer maxScore = 0;
        for (PaperTopicItem item : itemList) {
            if (ObjectUtil.isNotNull(item.getItemScore()) && item.getItemScore() > maxScore) {
                maxScore = item.getItemScore();
            }
        }
        
        // 如果有有效的最高分，则更新题目分数
        if (maxScore > 0) {
            paperTopic.setTopicScore(maxScore);
            this.updateById(paperTopic);
        }
    }
}
