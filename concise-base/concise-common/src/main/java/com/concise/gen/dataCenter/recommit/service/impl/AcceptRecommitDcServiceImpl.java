package com.concise.gen.dataCenter.recommit.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.gen.acceptrecommit.entity.AcceptRecommit;
import com.concise.gen.dataCenter.recommit.entity.AcceptRecommitDc;
import com.concise.gen.dataCenter.recommit.mapper.AcceptRecommitDcMapper;
import com.concise.gen.dataCenter.recommit.service.AcceptRecommitDcService;
import org.springframework.stereotype.Service;

/**
 * 公安再犯罪协同接收表service接口实现类
 *
 * <AUTHOR>
 * @date 2023-08-02 11:42:25
 */
@Service
@DS("dataCenter")
public class AcceptRecommitDcServiceImpl extends ServiceImpl<AcceptRecommitDcMapper, AcceptRecommitDc> implements AcceptRecommitDcService {

    @Override
    public void sync(AcceptRecommit model) {
        AcceptRecommitDc recommitDc = new AcceptRecommitDc();
        BeanUtil.copyProperties(model, recommitDc);
        this.saveOrUpdate(recommitDc);
    }
}
