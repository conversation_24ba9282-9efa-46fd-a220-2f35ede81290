package com.concise.gen.ywxtcount.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ywxtcount.entity.YwxtCount;
import com.concise.gen.ywxtcount.param.YwxtCountParam;
import java.util.List;

/**
 * 协同信息统计表service接口
 *
 * <AUTHOR>
 * @date 2024-01-23 14:08:03
 */
public interface YwxtCountService extends IService<YwxtCount> {

    /**
     * 查询协同信息统计表
     *
     * <AUTHOR>
     * @date 2024-01-23 14:08:03
     */
    PageResult<YwxtCount> page(YwxtCountParam ywxtCountParam);

    /**
     * 协同信息统计表列表
     *
     * <AUTHOR>
     * @date 2024-01-23 14:08:03
     */
    List<YwxtCount> list(YwxtCountParam ywxtCountParam);

    /**
     * 添加协同信息统计表
     *
     * <AUTHOR>
     * @date 2024-01-23 14:08:03
     */
    void add(YwxtCountParam ywxtCountParam);

    /**
     * 删除协同信息统计表
     *
     * <AUTHOR>
     * @date 2024-01-23 14:08:03
     */
    void delete(YwxtCountParam ywxtCountParam);

    /**
     * 编辑协同信息统计表
     *
     * <AUTHOR>
     * @date 2024-01-23 14:08:03
     */
    void edit(YwxtCountParam ywxtCountParam);

    /**
     * 查看协同信息统计表
     *
     * <AUTHOR>
     * @date 2024-01-23 14:08:03
     */
     YwxtCount detail(YwxtCountParam ywxtCountParam);

    /**
     * 保存或更新计数
     * @param type type
     * @param date yyyy-MM-dd
     * @param orgId orgId
     * @param count count
     * @param updateParent 是否更新上级
     */
     void saveOrUpdateCount(String type, String date, String orgId, int count, boolean updateParent);
    /**
     * 构建44001统计 T02
     * @param date 发送日期
     */
     void build44001Count(String date);

    /**
     * 构建信访统计 T08
     * @param date 接收日期
     */
    void buildLetterCount(String date);
}
