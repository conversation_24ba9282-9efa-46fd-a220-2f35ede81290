package com.concise.gen.dataCenter.correctplacechange.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.dataCenter.correctplacechange.entity.PlaceChangeDc;
import com.concise.gen.dataCenter.correctplacechange.mapper.PlaceChangeDcMapper;
import com.concise.gen.dataCenter.correctplacechange.param.PlaceChangeDcParam;
import com.concise.gen.dataCenter.correctplacechange.service.PlaceChangeDcService;
import org.springframework.stereotype.Service;

/**
 * 居住地变更_数据中心service接口实现类
 *
 * <AUTHOR>
 * @date 2023-09-13 15:07:59
 */
@Service
@DS("dataCenter")
public class PlaceChangeDcServiceImpl extends ServiceImpl<PlaceChangeDcMapper, PlaceChangeDc> implements PlaceChangeDcService {

    @Override
    public PageResult<PlaceChangeDc> page(PlaceChangeDcParam param) {
        QueryWrapper<PlaceChangeDc> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据社区矫正人员姓名 查询
            if (ObjectUtil.isNotEmpty(param.getPname())) {
                queryWrapper.lambda().like(PlaceChangeDc::getPname, param.getPname());
            }
            if (param.getOrgs().size() < 1000) {
                queryWrapper.lambda().in(PlaceChangeDc::getJiedaoId, param.getOrgs());
            }
        }
        queryWrapper.lambda()
                .eq(PlaceChangeDc::getQrdszs, "2791")
                .eq(PlaceChangeDc::getFlowstatusname, "区县局审批通过")
                .orderByDesc(PlaceChangeDc::getSqsj);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

}
