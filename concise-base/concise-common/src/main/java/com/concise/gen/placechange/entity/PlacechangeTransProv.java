package com.concise.gen.placechange.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.file.param.SysFileInfoVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 跨省执行地变更
 *
 * <AUTHOR>
 * @date 2025-04-01 11:09:06
 */
@Data
@TableName("correction_placechange_trans_prov")
public class PlacechangeTransProv{

    /**  主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    private Integer deleted;

    private Date createTime;

    /**  变更类型
     * 0 迁出
     * 1 迁入
     * */
    private String transType;

    /**  统一赋号 */
    private String uniformCode;

    /**  流程状态 */
    private String processStatus;

    /**  迁出结果 */
    private String receiveStatus;

    /**  矫正单位 */
    private String deptCode;

    /**  矫正单位 */
    private String deptId;

    /**  矫正单位 */
    private String deptPids;

    /**  矫正单位 */
    private String deptName;

    /**  矫正单位 */
    private String extDeptCode;

    /**  矫正单位 */
    private String extDeptName;

    /**  社区矫正对象姓名 */
    private String correctionObjName;

    /**  社区矫正对象 */
    private String correctionObjId;

    /**  社区矫正对象编号 */
    private String correctionObjCode;

    /**  证件类型（字典） */
    private String certType;

    /**  证件号码 */
    private String certNum;

    /**  民族（字典） */
    private String nation;

    /**  国籍 */
    private String nationality;

    /**  文化程度 */
    private String educationalBackground;

    /**  婚姻状况 */
    private String maritalStatus;

    /**  政治面貌 */
    private String politicalStatus;

    /**  捕前职业 */
    private String job;

    /**  就业就学情况 */
    private String employment;

    /**  社区矫正类别（字典） */
    private String correctionType;

    /**  住所地 */
    private String residenceCode;

    /**  住所地详细地址 */
    private String residence;

    /**  户籍所在地 */
    private String registeredAddressCode;

    /**  户籍所在地明细 */
    private String registeredAddress;

    /**  矫正开始日期 */
    private Date correctionStart;

    /**  矫正结束日期 */
    private Date correctionEnd;

    /**  矫正期限 */
    private String correctionDuration;

    /**  决定机关 */
    private String decisionDept;

    /**  迁出单位联系人 */
    private String deptContactPsn;

    /**  迁出单位联系电话 */
    private String deptContactTel;

    /**  申请日期 */
    private Date applicationDate;
    /**迁入地*/
    private String destinationProvName;
    /**  迁入地 */
    private String destinationCode;

    /**  迁入地详细地址 */
    private String destination;

    /**  变更理由 */
    private String changeReason;

    /**  接收结果 */
    private String opinionResult;

    /**  情况说明 */
    private String opinionRemark;

    /**  意见反馈日期 */
    private Date opinionDate;

    /**  入矫报到情况 */
    private String registrationResult;

    /**  入矫日期 */
    private String correctionStartAt;

    /**  入矫报到备注 */
    private String registrationRemark;

    /**  移送日期 */
    private Date transDate;

    /**
     *同步执法办案
     * 0 不同步
     * 1 需要同步
     * */
    private String sync;
    private String receiveDept;
    private String sendDept;
    private String taskId1;
    private String taskId2;
    private String taskId3;
    private String taskId4;

    /**承办信息*/
    private String processPsn;
    private String processDept;
    private String processDeptTel;

    /**外部承办信息*/
    private String extProcessPsn;
    private String extProcessDept;
    private String extProcessDeptTel;

    @TableField(exist = false)
    private List<SysFileInfoVO> files46001;
    @TableField(exist = false)
    private List<SysFileInfoVO> files46002;
    @TableField(exist = false)
    private List<SysFileInfoVO> files46003;
    @TableField(exist = false)
    private List<SysFileInfoVO> files46004;

}
