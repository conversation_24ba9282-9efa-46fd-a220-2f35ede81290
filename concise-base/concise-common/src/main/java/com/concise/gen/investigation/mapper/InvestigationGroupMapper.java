package com.concise.gen.investigation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.investigation.entity.InvestigationGroup;
import org.apache.ibatis.annotations.Param;

/**
 * 调查评估_小组调查
 *
 * <AUTHOR>
 * @date 2025-03-20 10:49:49
 */
public interface InvestigationGroupMapper extends BaseMapper<InvestigationGroup> {
    String dictName(@Param("type")String type, @Param("code")String code);
}
