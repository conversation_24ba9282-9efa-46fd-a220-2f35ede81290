package com.concise.gen.documentserial.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.documentserial.entity.DocumentSerial;
import org.apache.ibatis.annotations.Param;

/**
 * 文书号维护
 *
 * <AUTHOR>
 * @date 2025-03-27 10:09:11
 */
public interface DocumentSerialMapper extends BaseMapper<DocumentSerial> {

    /**
     * 查找当前最大的文书号
     * @param deptId
     * @param type
     * @param year
     * @return
     */
    Integer getMaxSerialNumber(@Param("deptId") String deptId, @Param("type") String type, @Param("year") int year);
}
