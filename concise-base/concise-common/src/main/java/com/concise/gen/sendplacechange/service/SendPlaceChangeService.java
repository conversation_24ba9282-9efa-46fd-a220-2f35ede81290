package com.concise.gen.sendplacechange.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.dataCenter.correctplacechange.entity.PlaceChangeDc;
import com.concise.gen.sendplacechange.entity.SendPlaceChange;
import com.concise.gen.sendplacechange.param.SendPlaceChangeParam;

import java.util.List;

/**
 * 发送变更执行地通知service接口
 *
 * <AUTHOR>
 * @date 2023-09-13 14:39:13
 */
public interface SendPlaceChangeService extends IService<SendPlaceChange> {

    /**
     * 查询发送变更执行地通知
     *
     * <AUTHOR>
     * @date 2023-09-13 14:39:13
     */
    PageResult<SendPlaceChange> page(SendPlaceChangeParam sendPlaceChangeParam);

    /**
     * 发送变更执行地通知列表
     *
     * <AUTHOR>
     * @date 2023-09-13 14:39:13
     */
    List<SendPlaceChange> list(SendPlaceChangeParam sendPlaceChangeParam);

    /**
     * 添加发送变更执行地通知
     *
     * <AUTHOR>
     * @date 2023-09-13 14:39:13
     */
    void saveOrUpdate(SendPlaceChangeParam sendPlaceChangeParam);

    /**
     * 删除发送变更执行地通知
     *
     * <AUTHOR>
     * @date 2023-09-13 14:39:13
     */
    void delete(SendPlaceChangeParam sendPlaceChangeParam);

    /**
     * 编辑发送变更执行地通知
     *
     * <AUTHOR>
     * @date 2023-09-13 14:39:13
     */
    void edit(SendPlaceChangeParam sendPlaceChangeParam);

    /**
     * 查看发送变更执行地通知
     *
     * <AUTHOR>
     * @date 2023-09-13 14:39:13
     */
     SendPlaceChange detail(SendPlaceChangeParam sendPlaceChangeParam);

    void add(String id);

    /**
     * save
     * @param dc 数据源
     * @param target 0省内记录 00省外记录
     */
    void save(PlaceChangeDc dc, String target);
    void send(SendPlaceChange xt);
}
