package com.concise.gen.paperquestionbank.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.paperquestionbank.entity.PaperQuestionBank;
import com.concise.gen.paperquestionbank.enums.PaperQuestionBankExceptionEnum;
import com.concise.gen.paperquestionbank.mapper.PaperQuestionBankMapper;
import com.concise.gen.paperquestionbank.param.PaperQuestionBankParam;
import com.concise.gen.paperquestionbank.service.PaperQuestionBankService;
import com.concise.gen.paperquestionitem.entity.PaperQuestionItem;
import com.concise.gen.paperquestionitem.service.PaperQuestionItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 题库service接口实现类
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:58
 */
@Service
public class PaperQuestionBankServiceImpl extends ServiceImpl<PaperQuestionBankMapper, PaperQuestionBank> implements PaperQuestionBankService {

    @Autowired
    private PaperQuestionItemService paperQuestionItemService;

    @Override
    public PageResult<PaperQuestionBank> page(PaperQuestionBankParam paperQuestionBankParam) {
        QueryWrapper<PaperQuestionBank> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(paperQuestionBankParam)) {

            // 根据问题名称 查询
            if (ObjectUtil.isNotEmpty(paperQuestionBankParam.getTopicName())) {
                queryWrapper.lambda().like(PaperQuestionBank::getTopicName, paperQuestionBankParam.getTopicName());
            }
            // 根据指标 查询
            if (ObjectUtil.isNotEmpty(paperQuestionBankParam.getIndexName())) {
                queryWrapper.lambda().like(PaperQuestionBank::getIndexName, paperQuestionBankParam.getIndexName());
            }
        }
        queryWrapper.lambda().eq(PaperQuestionBank::getDelFlag, 0);
        queryWrapper.lambda().orderByDesc(PaperQuestionBank::getUpdateTime);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public PageResult<PaperQuestionBank> pageDetail(PaperQuestionBankParam paperQuestionBankParam) {
        QueryWrapper<PaperQuestionBank> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(paperQuestionBankParam)) {

            // 根据问题名称 查询
            if (ObjectUtil.isNotEmpty(paperQuestionBankParam.getTopicName())) {
                queryWrapper.lambda().like(PaperQuestionBank::getTopicName, paperQuestionBankParam.getTopicName());
            }
            // 根据指标 查询
            if (ObjectUtil.isNotEmpty(paperQuestionBankParam.getIndexName())) {
                queryWrapper.lambda().like(PaperQuestionBank::getIndexName, paperQuestionBankParam.getIndexName());
            }
        }
        queryWrapper.lambda().eq(PaperQuestionBank::getDelFlag, 0);
        queryWrapper.lambda().orderByDesc(PaperQuestionBank::getUpdateTime);
        Page<PaperQuestionBank> res = this.page(PageFactory.defaultPage(), queryWrapper);
        for (PaperQuestionBank paperQuestionBank : res.getRecords()) {
            // 查找指标选项
            QueryWrapper<PaperQuestionItem> queryWrapperItem = new QueryWrapper<>();
            queryWrapperItem.lambda().eq(PaperQuestionItem::getPaperQuestionBankId, paperQuestionBank.getId());
            queryWrapperItem.lambda().orderByAsc(PaperQuestionItem::getSerialNumber);
            paperQuestionBank.setItemList(paperQuestionItemService.list(queryWrapperItem));
        }
        return new PageResult<>(res);
    }

    @Override
    public List<PaperQuestionBank> list(PaperQuestionBankParam paperQuestionBankParam) {
        return this.list();
    }

    @Override
    @Transactional
    public void add(PaperQuestionBankParam paperQuestionBankParam) {
        String id = UUID.randomUUID().toString().replace("-", "");
        PaperQuestionBank paperQuestionBank = new PaperQuestionBank();
        BeanUtil.copyProperties(paperQuestionBankParam, paperQuestionBank);
        //保存指标选项
        int score = 0;
        String items = "";
        List<PaperQuestionItem> itemList = new ArrayList<>();
        for (PaperQuestionItem paperQuestionItem : paperQuestionBankParam.getItemList()) {
            if (ObjectUtil.isEmpty(paperQuestionItem.getContent())) {
                //指标项为空直接跳过
                continue;
            }
            paperQuestionItem.setPaperQuestionBankId(id);
            if (ObjectUtil.isNotEmpty(paperQuestionItem.getItemScore())) {
                score += paperQuestionItem.getItemScore();
                items += paperQuestionItem.getContent() + "(" + paperQuestionItem.getItemScore() + ")" + "/";
            } else {
                //没输分数默认0分
                score += 0;
                items += paperQuestionItem.getContent() + "/";
            }
            itemList.add(paperQuestionItem);
        }
        if (itemList.size() > 0) {
            paperQuestionItemService.saveBatch(itemList);
        }
        //保存主表
        paperQuestionBank.setId(id);
        paperQuestionBank.setTopicScore(score);
        paperQuestionBank.setItems(items.length() > 0 ? items.substring(0, items.length() - 1) : "");
        paperQuestionBank.setUpdateTime(new Date());
        paperQuestionBank.setCreateTime(new Date());
        this.save(paperQuestionBank);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(PaperQuestionBankParam paperQuestionBankParam) {
        this.removeById(paperQuestionBankParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(PaperQuestionBankParam paperQuestionBankParam) {
        if ("null".equals(paperQuestionBankParam.getRemark())) {
            paperQuestionBankParam.setRemark("");
        }
        //先删除之前的指标选项
        QueryWrapper<PaperQuestionItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PaperQuestionItem::getPaperQuestionBankId, paperQuestionBankParam.getId());
        paperQuestionItemService.remove(queryWrapper);

        PaperQuestionBank paperQuestionBank = this.queryPaperQuestionBank(paperQuestionBankParam);
        BeanUtil.copyProperties(paperQuestionBankParam, paperQuestionBank);
        //保存指标选项
        int score = 0;
        String items = "";
        List<PaperQuestionItem> itemList = new ArrayList<>();
        for (PaperQuestionItem paperQuestionItem : paperQuestionBankParam.getItemList()) {
            if (ObjectUtil.isEmpty(paperQuestionItem.getContent())) {
                //指标项为空直接跳过
                continue;
            }
            paperQuestionItem.setPaperQuestionBankId(paperQuestionBankParam.getId());
            if (ObjectUtil.isNotEmpty(paperQuestionItem.getItemScore())) {
                score += paperQuestionItem.getItemScore();
                items += paperQuestionItem.getContent() + "(" + paperQuestionItem.getItemScore() + ")" + "/";
            } else {
                //没输分数默认0分
                score += 0;
                items += paperQuestionItem.getContent() + "/";
            }
            itemList.add(paperQuestionItem);
        }
        if (itemList.size() > 0) {
            paperQuestionItemService.saveBatch(itemList);
        }
        //更新主表
        paperQuestionBank.setTopicScore(score);
        paperQuestionBank.setItems(items.length() > 0 ? items.substring(0, items.length() - 1) : "");
        this.updateById(paperQuestionBank);
    }

    @Override
    public PaperQuestionBank detail(PaperQuestionBankParam paperQuestionBankParam) {
        return this.queryPaperQuestionBank(paperQuestionBankParam);
    }

    /**
     * 获取题库
     *
     * <AUTHOR>
     * @date 2025-03-20 15:02:58
     */
    private PaperQuestionBank queryPaperQuestionBank(PaperQuestionBankParam paperQuestionBankParam) {
        PaperQuestionBank paperQuestionBank = this.getById(paperQuestionBankParam.getId());
        if (ObjectUtil.isNull(paperQuestionBank)) {
            throw new ServiceException(PaperQuestionBankExceptionEnum.NOT_EXIST);
        }
        // 查找指标选项
        QueryWrapper<PaperQuestionItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PaperQuestionItem::getPaperQuestionBankId, paperQuestionBankParam.getId());
        queryWrapper.lambda().orderByAsc(PaperQuestionItem::getSerialNumber);
        paperQuestionBank.setItemList(paperQuestionItemService.list(queryWrapper));
        return paperQuestionBank;
    }
}
