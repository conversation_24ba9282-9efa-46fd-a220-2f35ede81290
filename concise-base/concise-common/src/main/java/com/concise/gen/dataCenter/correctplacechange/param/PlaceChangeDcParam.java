package com.concise.gen.dataCenter.correctplacechange.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
* 居住地变更_数据中心参数类
 *
 * <AUTHOR>
 * @date 2023-09-13 15:07:59
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class PlaceChangeDcParam extends BaseParam {

    private Set<String> orgs;

    private String jiedaoId;
    /**
     * 唯一标识
     */
    @NotNull(message = "唯一标识不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 社区矫正人员标识
     */
    @NotBlank(message = "社区矫正人员标识不能为空，请检查pid参数", groups = {add.class, edit.class})
    private String pid;

    /**
     * 社区矫正人员姓名
     */
    @NotBlank(message = "社区矫正人员姓名不能为空，请检查pname参数", groups = {add.class, edit.class})
    private String pname;

    /**
     * 申请时间
     */
    @NotNull(message = "申请时间不能为空，请检查sqsj参数", groups = {add.class, edit.class})
    private String sqsj;

    /**
     * 迁入地所在省（区、市）
     */
    @NotBlank(message = "迁入地所在省（区、市）不能为空，请检查qrdszs参数", groups = {add.class, edit.class})
    private String qrdszs;

    /**
     * 迁入地所在省（区、市）中文值
     */
    @NotBlank(message = "迁入地所在省（区、市）中文值不能为空，请检查qrdszsName参数", groups = {add.class, edit.class})
    private String qrdszsName;

    /**
     * 迁入地所在地（市、州）
     */
    @NotBlank(message = "迁入地所在地（市、州）不能为空，请检查qrdszd参数", groups = {add.class, edit.class})
    private String qrdszd;

    /**
     * 迁入地所在地（市、州）中文值
     */
    @NotBlank(message = "迁入地所在地（市、州）中文值不能为空，请检查qrdszdName参数", groups = {add.class, edit.class})
    private String qrdszdName;

    /**
     * 迁入地所在县（市、区）
     */
    @NotBlank(message = "迁入地所在县（市、区）不能为空，请检查qrdszx参数", groups = {add.class, edit.class})
    private String qrdszx;

    /**
     * 迁入地所在县（市、区）中文值
     */
    @NotBlank(message = "迁入地所在县（市、区）中文值不能为空，请检查qrdszxName参数", groups = {add.class, edit.class})
    private String qrdszxName;

    /**
     * 迁入地（乡镇、街道）
     */
    @NotBlank(message = "迁入地（乡镇、街道）不能为空，请检查qrdxz参数", groups = {add.class, edit.class})
    private String qrdxz;

    /**
     * 迁入地（乡镇、街道）中文值
     */
    @NotBlank(message = "迁入地（乡镇、街道）中文值不能为空，请检查qrdxzName参数", groups = {add.class, edit.class})
    private String qrdxzName;

    /**
     * 迁入地明细
     */
    @NotBlank(message = "迁入地明细不能为空，请检查qrdmx参数", groups = {add.class, edit.class})
    private String qrdmx;

    /**
     * 拟接收矫正单位
     */
    @NotBlank(message = "拟接收矫正单位不能为空，请检查njsjzdwid参数", groups = {add.class, edit.class})
    private String njsjzdwid;

    /**
     * 拟接收矫正单位中文值
     */
    @NotBlank(message = "拟接收矫正单位中文值不能为空，请检查njsjzdwName参数", groups = {add.class, edit.class})
    private String njsjzdwName;

    /**
     * 变更理由
     */
    @NotBlank(message = "变更理由不能为空，请检查jzdbgsy参数", groups = {add.class, edit.class})
    private String jzdbgsy;

    /**
     * 流程状态
     */
    @NotBlank(message = "流程状态不能为空，请检查flowstatusname参数", groups = {add.class, edit.class})
    private String flowstatusname;

}
