package com.concise.gen.revocationprobation.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.revocationauditphase.param.RevocationAuditPhaseParam;
import com.concise.gen.revocationprobation.entity.RevocationProbation;
import com.concise.gen.revocationprobation.param.RevParam;
import com.concise.gen.revocationprobation.param.RevocationProbationParam;

import java.util.List;

/**
 * 提请撤销缓刑service接口
 *
 * <AUTHOR>
 * @date 2022-05-31 13:47:23
 */
public interface RevocationProbationService extends IService<RevocationProbation> {

    /**
     * 查询提请撤销缓刑
     *
     * <AUTHOR>
     * @date 2022-05-31 13:47:23
     */
    PageResult<RevocationProbation> page(RevocationProbationParam revocationProbationParam);

    /**
     * 提请撤销缓刑列表
     *
     * <AUTHOR>
     * @date 2022-05-31 13:47:23
     */
    List<RevocationProbation> list(RevocationProbationParam revocationProbationParam);

    /**
     * 添加提请撤销缓刑
     *
     * <AUTHOR>
     * @date 2022-05-31 13:47:23
     */
    void add(RevocationProbationParam revocationProbationParam);
    /**
     * 编辑提请撤销缓刑
     *
     * <AUTHOR>
     * @date 2022-05-31 13:47:23
     */
    void edit(RevocationProbationParam revocationProbationParam);

    /**
     * 提请撤销缓刑 审核
     * @param param
     */
    void audit(RevocationAuditPhaseParam param);

    /**
     * 提请撤销缓刑 提请
     * @param param
     */
    void submit(RevocationProbationParam param);

    /**
     * 删除提请撤销缓刑
     *
     * <AUTHOR>
     * @date 2022-05-31 13:47:23
     */
    void delete(RevocationProbationParam revocationProbationParam);


    /**
     * 查看提请撤销缓刑
     *
     * <AUTHOR>
     * @date 2022-05-31 13:47:23
     */
     RevocationProbation detail(RevocationProbationParam revocationProbationParam);

     void revocationProbation(RevParam param);

     void updateByTyfh(RevocationProbationParam param,String step);
}
