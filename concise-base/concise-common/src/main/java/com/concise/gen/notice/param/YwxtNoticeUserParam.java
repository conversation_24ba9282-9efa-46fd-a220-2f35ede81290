package com.concise.gen.notice.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 协同信息通知_用户表参数类
 *
 * <AUTHOR>
 * @date 2024-03-28 13:41:14
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class YwxtNoticeUserParam extends BaseParam {

    /**
     * 主键
     */
    private Long id;

    /**
     * ywxt_notice.id
     */
    private String noticeId;

    /**
     * 用户id
     */
    private String userId;
    private String userName;

    private String orgIds;

    /**
     * 查询关联冗余
     */
    private String noticeType;

}
