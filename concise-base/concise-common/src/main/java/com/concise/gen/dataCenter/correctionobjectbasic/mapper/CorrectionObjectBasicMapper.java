package com.concise.gen.dataCenter.correctionobjectbasic.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.dataCenter.correctionobjectbasic.entity.CorrectionObjectBasic;

import java.util.List;

/**
 * 社区矫正对象信息-基本信息
 *
 * <AUTHOR>
 * @date 2021-08-27 11:35:18
 */
@DS("dataCenter")
public interface CorrectionObjectBasicMapper extends BaseMapper<CorrectionObjectBasic> {
    /**
     * @return
     */
    List<String> getId();
}
