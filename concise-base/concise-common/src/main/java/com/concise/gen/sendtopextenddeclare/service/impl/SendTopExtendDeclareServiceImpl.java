package com.concise.gen.sendtopextenddeclare.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.param.SysFileInfoVO;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptcorrectiondoc.service.AcceptCorrectionDocService;
import com.concise.gen.dataCenter.orgDc.entity.OrgDc;
import com.concise.gen.dataCenter.orgDc.service.OrgDcService;
import com.concise.gen.sendtopextenddeclare.entity.SendTopExtendDeclare;
import com.concise.gen.sendtopextenddeclare.enums.SendTopExtendDeclareExceptionEnum;
import com.concise.gen.sendtopextenddeclare.mapper.SendTopExtendDeclareMapper;
import com.concise.gen.sendtopextenddeclare.param.SendTopExtendDeclareParam;
import com.concise.gen.sendtopextenddeclare.service.SendTopExtendDeclareService;
import com.concise.gen.steplog.entity.StepLog;
import com.concise.gen.steplog.service.StepLogService;
import com.concise.gen.webservice.service.SendTemporarilyOutsidePrisonService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发送暂外续报service接口实现类
 *
 * <AUTHOR>
 * @date 2023-05-25 17:23:48
 */
@Service
public class SendTopExtendDeclareServiceImpl extends ServiceImpl<SendTopExtendDeclareMapper, SendTopExtendDeclare> implements SendTopExtendDeclareService {

    @Resource
    private SendTemporarilyOutsidePrisonService sendService;
    @Resource
    private AcceptCorrectionDocService acceptCorrectionDocService;
    @Resource
    private SysFileInfoService sysFileInfoService;
    @Resource
    private OrgDcService orgDcService;
    @Resource
    private StepLogService stepLogService;
    @Override
    public PageResult<SendTopExtendDeclare> page(SendTopExtendDeclareParam param) {
        QueryWrapper<SendTopExtendDeclare> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(SendTopExtendDeclare::getXm, param.getXm());
            }
            // 矫正单位
            if (ObjectUtil.isNotEmpty(param.getJzjg())) {
                queryWrapper.lambda().likeRight(SendTopExtendDeclare::getJzjg, param.getJzjg());
            }
            if (param.getOrgs().size() < 1000) {
                queryWrapper.lambda().in(SendTopExtendDeclare::getJzjgId, param.getOrgs());
            }
            // 根据申请日期 查询
            if (ObjectUtil.isNotEmpty(param.getSqrq())) {
                queryWrapper.lambda().eq(SendTopExtendDeclare::getSqrq, param.getSqrq());
            }
            // 续报申请状态
            if (ObjectUtil.isNotEmpty(param.getZt())) {
                queryWrapper.lambda().eq(SendTopExtendDeclare::getZt, param.getZt());
            }
            // 征求检察院名称 ：
            if (ObjectUtil.isNotEmpty(param.getJcyCode())) {
                queryWrapper.lambda().like(SendTopExtendDeclare::getJcyCode, param.getJcyCode());
            }
            // 发送申报申请法院名称
            if (ObjectUtil.isNotEmpty(param.getFyCode())) {
                queryWrapper.lambda().like(SendTopExtendDeclare::getFyCode, param.getFyCode());
            }
            // 根据统一赋号 查询
            if (ObjectUtil.isNotEmpty(param.getTyfh())) {
                queryWrapper.lambda().eq(SendTopExtendDeclare::getTyfh, param.getTyfh());
            }
        }
        queryWrapper.lambda().orderByDesc(SendTopExtendDeclare::getSqrq);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<SendTopExtendDeclare> list(SendTopExtendDeclareParam sendTopExtendDeclareParam) {
        return this.list();
    }

    @Override
    public void add(SendTopExtendDeclareParam param) {
        SendTopExtendDeclare sendTopExtendDeclare = new SendTopExtendDeclare();
        BeanUtil.copyProperties(param, sendTopExtendDeclare);
        sendTopExtendDeclare.setTyfh(IdUtil.fastSimpleUUID());
        this.save(sendTopExtendDeclare);
        StepLog log = new StepLog();
        log.setId(IdUtil.fastSimpleUUID());
        log.setBizId(sendTopExtendDeclare.getId());
        log.setStep("1");
        log.setContext(sendTopExtendDeclare.getSqrxm());
        log.setCreateTime(DateUtil.date());
        stepLogService.save(log);
        sendToJcy(sendTopExtendDeclare);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(SendTopExtendDeclareParam sendTopExtendDeclareParam) {
        this.removeById(sendTopExtendDeclareParam.getId());
    }

    @Override
    public void edit(SendTopExtendDeclareParam sendTopExtendDeclareParam) {
        SendTopExtendDeclare sendTopExtendDeclare = this.querySendTopExtendDeclare(sendTopExtendDeclareParam);
        BeanUtil.copyProperties(sendTopExtendDeclareParam, sendTopExtendDeclare);
        this.updateById(sendTopExtendDeclare);
        StepLog log = new StepLog();
        log.setId(IdUtil.fastSimpleUUID());
        log.setBizId(sendTopExtendDeclare.getId());
        log.setStep("3");
        log.setContext(sendTopExtendDeclare.getSqrxm());
        log.setCreateTime(DateUtil.date());
        stepLogService.save(log);
        sendToFy(sendTopExtendDeclare.getId());
    }

    @Override
    public List<AcceptCorrectionDocParam> updateByTyfh(SendTopExtendDeclareParam param,String step) {
        SendTopExtendDeclare data = this.lambdaQuery()
                .eq(SendTopExtendDeclare::getTyfh, param.getTyfh())
                .orderByDesc(SendTopExtendDeclare::getSqrq)
                .last("limit 1")
                .one();
        if (data == null) {
            System.out.println("tyfh: "+param.getTyfh());
            return null;
        }
        param.setId(data.getId());
        param.setZt(step);
        BeanUtil.copyProperties(param, data);
        this.updateById(data);

        StepLog log = new StepLog();
        log.setId(IdUtil.fastSimpleUUID());
        log.setBizId(data.getId());
        log.setCreateTime(DateUtil.date());
        if ("2".equals(step)) {
            log.setStep("2");
            log.setContext(data.getZyjwzxjcyj());
        }else if ("4".equals(step)) {
            log.setStep("4");
            log.setContext(data.getSdwsnr());
        }
        stepLogService.save(log);

        param.getDocList().forEach(doc ->acceptCorrectionDocService.add(doc));
        return param.getDocList();
    }

    @Override
    public SendTopExtendDeclare detail(SendTopExtendDeclareParam sendTopExtendDeclareParam) {
        SendTopExtendDeclare sendTopExtendDeclare = this.querySendTopExtendDeclare(sendTopExtendDeclareParam);
        List<SysFileInfoVO> fileList = sysFileInfoService.getDetailByIds(sendTopExtendDeclare.getFile1());
        fileList.addAll(sysFileInfoService.getDetailByIds(sendTopExtendDeclare.getFile2()));
        sendTopExtendDeclare.setFileList(fileList);
        if (ObjectUtil.isNotEmpty(sendTopExtendDeclare.getTaskId4054())) {
            sendTopExtendDeclare.setFileList2(acceptCorrectionDocService.getSysFileInfoParamList(sendTopExtendDeclare.getTaskId4054()));
        }
        if (ObjectUtil.isNotEmpty(sendTopExtendDeclare.getTaskId4056())) {
            sendTopExtendDeclare.setFileList3(acceptCorrectionDocService.getSysFileInfoParamList(sendTopExtendDeclare.getTaskId4056()));
        }
        return sendTopExtendDeclare;
    }

    @Override
    public void sendToJcy(SendTopExtendDeclare data) {
        List<AcceptCorrectionDoc> list = sysFileInfoService.getDocListByIds(data.getFile1());
        list.addAll(sysFileInfoService.getDocListByIds(data.getFile2()));
        data.setDocList(list);
        OrgDc one = orgDcService.lambdaQuery().eq(OrgDc::getCode, data.getJzjg()).one();
        if (one != null) {
            OrgDc pOrg = orgDcService.getById(one.getPid());
            if (pOrg != null) {
                data.setFsdw(pOrg.getCode());
            }
        }
        if (ObjectUtil.isEmpty(data.getFsdw())) {
            data.setFsdw(data.getJzjg());
        }
        sendService.xtbh4053(data);

        //data.setZt("2");
        //data.setJcybmsah("检察院部门受案号");
        //data.setScwswh("审查文书文号");
        //data.setZyjwzxjcyj("暂予监外执行检察意见");
        //data.setYjfksj(DateUtil.date());
        //data.setJcyjbz("检察意见备注");
        //data.setWswh("文书文号");
        //data.setJcyfksj(DateUtil.date());

        //this.updateById(data);
        //
        //StepLog log = new StepLog();
        //log.setId(IdUtil.fastSimpleUUID());
        //log.setBizId(data.getId());
        //log.setStep("2");
        //log.setContext(data.getZyjwzxjcyj());
        //log.setCreateTime(DateUtil.date());
        //stepLogService.save(log);
    }

    @Override
    public void sendToFy(String id) {
        SendTopExtendDeclare data = this.getById(id);
        List<AcceptCorrectionDoc> list = sysFileInfoService.getDocListByIds(data.getFile1());
        list.addAll(sysFileInfoService.getDocListByIds(data.getFile2()));
        list.add(acceptCorrectionDocService.lambdaQuery().eq(AcceptCorrectionDoc::getContactId,data.getTaskId4054()).last(" limit 1").one());
        data.setDocList(list);
        OrgDc one = orgDcService.lambdaQuery().eq(OrgDc::getCode, data.getJzjg()).one();
        if (one != null) {
            OrgDc pOrg = orgDcService.getById(one.getPid());
            if (pOrg != null) {
                data.setFsdw(pOrg.getCode());
            }
        }
        if (ObjectUtil.isEmpty(data.getFsdw())) {
            data.setFsdw(data.getJzjg());
        }
        sendService.xtbh4055(data);

        //data.setZt("4");
        //data.setQsrq(DateUtil.date());
        //data.setQsdw("1");
        //data.setQsr("签收人");
        //data.setHzwsh("回执文书号");
        //data.setSdwsnr("送达文书内容");
        //data.setFyfksj(DateUtil.date());
        //this.updateById(data);
        //
        //StepLog log = new StepLog();
        //log.setId(IdUtil.fastSimpleUUID());
        //log.setBizId(data.getId());
        //log.setStep("4");
        //log.setContext(data.getSdwsnr());
        //log.setCreateTime(DateUtil.date());
        //stepLogService.save(log);
    }

    /**
     * 获取发送暂外续报
     *
     * <AUTHOR>
     * @date 2023-05-25 17:23:48
     */
    private SendTopExtendDeclare querySendTopExtendDeclare(SendTopExtendDeclareParam sendTopExtendDeclareParam) {
        SendTopExtendDeclare sendTopExtendDeclare = this.getById(sendTopExtendDeclareParam.getId());
        if (ObjectUtil.isNull(sendTopExtendDeclare)) {
            throw new ServiceException(SendTopExtendDeclareExceptionEnum.NOT_EXIST);
        }
        return sendTopExtendDeclare;
    }
}
