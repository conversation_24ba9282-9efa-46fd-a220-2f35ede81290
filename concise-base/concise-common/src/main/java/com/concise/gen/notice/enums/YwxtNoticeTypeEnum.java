package com.concise.gen.notice.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 协同信息通知类型
 *
 * <AUTHOR>
 * @date 2023-12-08 11:38:39
 */
@Getter
public enum YwxtNoticeTypeEnum{

    /**
     * 协同信息通知类型及对应模版
     */
    TERMINATE_01("terminate_01", "解矫通知",false,
            //【解矫通知】【协同日期】系统自动将您单位X条解矫数据自动通知至对应检察院等单位
            2,"【解矫通知】%s系统自动将您单位%s条解矫数据自动通知至对应检察院等单位",
            0,"")
    ,ADD_CORRECTION_01("add_correction_01", "入矫衔接",true,
            //【入矫衔接】【接收单位】于【协同日期】新收到一条X（变量：发送单位）推送的X（变量：姓名）矫正对象衔接信息，请尽快确认并处理
            4,"【入矫衔接】%s于%s新收到一条%s推送的%s矫正对象衔接信息，请尽快确认并处理，请尽快确认并处理",
            //【入矫衔接】您单位新收到一条X（变量：发送单位）推送的X（变量：姓名）的矫正对象衔接信息，请尽快确认并处理
            2,"【入矫衔接】您单位新收到一条%s推送的%s的矫正对象衔接信息，请尽快确认并处理")
    ,ARREST_01("arrest_01","提请逮捕",true,
            //【提请逮捕】【接收单位】于【协同日期】收到一条X（变量：决定机关法院）推送的X（变量：姓名）的提请逮捕回执信息，请尽快确认并处理
            4,"【提请逮捕】%s于%s收到一条%s推送的%s的提请逮捕回执信息，请尽快确认并处理，请尽快确认并处理",
            //【提请逮捕】您单位收到一条X（变量：决定机关法院）推送的X（变量：姓名）的提请逮捕回执信息，请尽快确认并处理
            2,"【提请逮捕】您单位收到一条%s推送的%s的提请逮捕回执信息，请尽快确认并处理")
    ,ARREST_02("arrest_02","提请逮捕",true,
            //【撤销逮捕】【接收单位】于【协同日期】收到一条X（变量：决定机关法院）推送的X（变量：姓名）的提请逮捕决定信息，请尽快确认并处理
            4,"【提请逮捕】%s于%s收到一条%s推送的%s的提请逮捕决定信息，请尽快确认并处理，请尽快确认并处理",
            //【撤销逮捕】您单位收到一条X（变量：决定机关法院）推送的X（变量：姓名）的提请逮捕决定信息，请尽快确认并处理
            2,"【提请逮捕】您单位收到一条%s推送的%s的提请逮捕决定信息，请尽快确认并处理")
    ,INVEST_01("invest_01","调查评估",true,
            //【调查评估】【接收单位】于【协同日期】新收到一条X（变量：发送单位）推送的X（变量：姓名）的调查评估协同信息，请尽快确认并处理
            4,"【调查评估】%s于%s新收到一条%s推送的%s的调查评估协同信息，请尽快确认并处理",
            //【调查评估】您单位新收到一条X（变量：发送单位）推送的X（变量：姓名）的调查评估协同信息，请尽快确认并处理
            2,"【调查评估】您单位新收到一条%s推送的%s的调查评估协同信息，请尽快确认并处理")
    ,RECOMMIT_01("recommit_01","公安处罚协同",true,
            //【公安处罚协同】【接收单位】于【协同日期】新收到一条公安推送的X（变量：姓名）的公安处罚信息及文书，请尽快确认并处理
            3,"【公安处罚协同】%s于%s新收到一条公安推送的%s的公安处罚信息及文书，请尽快确认并处理",
            //【公安处罚协同】您单位新收到一条公安推送的X（变量：姓名）的公安处罚信息及文书，请尽快确认并处理
            1,"【公安处罚协同】您单位新收到一条公安推送的%s的公安处罚信息及文书，请尽快确认并处理")
    ,REVOCATION_PAROLE_01("revocation_parole_01","撤销假释",true,
            //【撤销假释】【接收单位】于【协同日期】收到一条X（变量：征求意见的检察院）推送的X（变量：姓名）的撤销假释检察意见，请尽快确认并处理
            4,"【撤销假释】%s于%s收到一条%s推送的%s的撤销假释检察意见，请尽快确认并处理",
            //【撤销假释】您单位收到一条X（变量：征求意见的检察院）推送的X（变量：姓名）的撤销假释检察意见，请尽快确认并处理
            2,"【撤销假释】您单位收到一条%s推送的%s的撤销假释检察意见，请尽快确认并处理")
    ,REVOCATION_PAROLE_02("revocation_parole_02","撤销假释",true,
            //【撤销假释】【接收单位】于【协同日期】收到一条X（变量：决定机关法院）推送的X（变量：姓名）的撤销假释决定信息，请尽快确认并处理
            4,"【撤销假释】%s于%s收到一条%s推送的%s的撤销假释决定信息，请尽快确认并处理",
            //【撤销假释】您单位收到一条X（变量：决定机关法院）推送的X（变量：姓名）的撤销假释决定信息，请尽快确认并处理
            2,"【撤销假释】您单位收到一条%s推送的%s的撤销假释决定信息，请尽快确认并处理")
    ,REVOCATION_PROBATION_01("revocation_probation_01","撤销缓刑",true,
            //【撤销缓刑】【接收单位】于【协同日期】收到一条X（变量：征求意见的检察院）推送的X（变量：姓名）的撤销缓刑检察意见，请尽快确认并处理
            4,"【撤销缓刑】%s于%s收到一条%s推送的%s的撤销缓刑检察意见，请尽快确认并处理",
            //【撤销缓刑】您单位收到一条X（变量：征求意见的检察院）推送的X（变量：姓名）的撤销缓刑检察意见，请尽快确认并处理
            2,"【撤销缓刑】您单位收到一条%s推送的%s的撤销缓刑检察意见，请尽快确认并处理")
    ,REVOCATION_PROBATION_02("revocation_probation_02","撤销缓刑",true,
            //【撤销缓刑】【接收单位】于【协同日期】收到一条X（变量：决定机关法院）推送的X（变量：姓名）的撤销缓刑决定信息，请尽快确认并处理
            4,"【撤销缓刑】%s于%s收到一条%s推送的%s的撤销缓刑决定信息，请尽快确认并处理",
            //【撤销缓刑】您单位收到一条X（变量：决定机关法院）推送的X（变量：姓名）的撤销缓刑决定信息，请尽快确认并处理
            2,"【撤销缓刑】您单位收到一条%s推送的%s的撤销缓刑决定信息，请尽快确认并处理")
    ,PETITION_LETTER_INFO("petition_letter_info","信访协同",false,
            //【信访协同】:【矫正单位】于【更新日期】新收到一条【姓名】的信访信息
            3,"【信访协同】:%s于%s新收到一条%s的信访信息",
            0,"")
    //【多跨协同】【日期】收到一条【矫正单位】【姓名】的【协同单位】的【协同信息】
    ,IRS_01("irs_01","多跨协同",true, 2,"【多跨协同】%s收到一条%s的【协同单位】的【协同信息】", 2,"【多跨协同】您单位于%s收到一条%s的【协同单位】的【协同信息】，请前往社区矫正数据协同平台了解详情")
    ,IRS_02("irs_02","多跨协同",true, 2,"【多跨协同】%s收到一条%s的【协同单位】的【协同信息】", 2,"【多跨协同】您单位于%s收到一条%s的【协同单位】的【协同信息】，请前往社区矫正数据协同平台了解详情")
    ,IRS_03("irs_03","多跨协同",true, 2,"【多跨协同】%s收到一条%s的【协同单位】的【协同信息】", 2,"【多跨协同】您单位于%s收到一条%s的【协同单位】的【协同信息】，请前往社区矫正数据协同平台了解详情")
    ,IRS_04("irs_04","多跨协同",true, 2,"【多跨协同】%s收到一条%s的【协同单位】的【协同信息】", 2,"【多跨协同】您单位于%s收到一条%s的【协同单位】的【协同信息】，请前往社区矫正数据协同平台了解详情")
    ,IRS_05("irs_05","多跨协同",true, 2,"【多跨协同】%s收到一条%s的【协同单位】的【协同信息】", 2,"【多跨协同】您单位于%s收到一条%s的【协同单位】的【协同信息】，请前往社区矫正数据协同平台了解详情")
    ,IRS_06("irs_06","多跨协同",true, 2,"【多跨协同】%s收到一条%s的【协同单位】的【协同信息】", 2,"【多跨协同】您单位于%s收到一条%s的【协同单位】的【协同信息】，请前往社区矫正数据协同平台了解详情")
    ,IRS_07("irs_07","多跨协同",true, 2,"【多跨协同】%s收到一条%s的【协同单位】的【协同信息】", 2,"【多跨协同】您单位于%s收到一条%s的【协同单位】的【协同信息】，请前往社区矫正数据协同平台了解详情")
    ,IRS_08("irs_08","多跨协同",true, 2,"【多跨协同】%s收到一条%s的【协同单位】的【协同信息】", 2,"【多跨协同】您单位于%s收到一条%s的【协同单位】的【协同信息】，请前往社区矫正数据协同平台了解详情")
    ,IRS_09("irs_09","多跨协同",true, 2,"【多跨协同】%s收到一条%s的【协同单位】的【协同信息】", 2,"【多跨协同】您单位于%s收到一条%s的【协同单位】的【协同信息】，请前往社区矫正数据协同平台了解详情")
    ,IRS_10("irs_10","多跨协同",true, 2,"【多跨协同】%s收到一条%s的【协同单位】的【协同信息】", 2,"【多跨协同】您单位于%s收到一条%s的【协同单位】的【协同信息】，请前往社区矫正数据协同平台了解详情")
    ,IRS_11("irs_11","多跨协同",true, 2,"【多跨协同】%s收到一条%s的【协同单位】的【协同信息】", 2,"【多跨协同】您单位于%s收到一条%s的【协同单位】的【协同信息】，请前往社区矫正数据协同平台了解详情")
    ,IRS_12("irs_12","多跨协同",true, 2,"【多跨协同】%s收到一条%s的【协同单位】的【协同信息】", 2,"【多跨协同】您单位于%s收到一条%s的【协同单位】的【协同信息】，请前往社区矫正数据协同平台了解详情")
    ,IRS_13("irs_13","多跨协同",true, 2,"【多跨协同】%s收到一条%s的【协同单位】的【协同信息】", 2,"【多跨协同】您单位于%s收到一条%s的【协同单位】的【协同信息】，请前往社区矫正数据协同平台了解详情")
    ,IRS_14("irs_14","多跨协同",true, 2,"【多跨协同】%s收到一条%s的【协同单位】的【协同信息】", 2,"【多跨协同】您单位于%s收到一条%s的【协同单位】的【协同信息】，请前往社区矫正数据协同平台了解详情")
    ,IRS_15("irs_15","多跨协同",true, 2,"【多跨协同】%s收到一条%s的【协同单位】的【协同信息】", 2,"【多跨协同】您单位于%s收到一条%s的【协同单位】的【协同信息】，请前往社区矫正数据协同平台了解详情")
    ;

    private final String type;
    private final String prefix;
    private final Boolean sendZwddMsg;
    private final Integer paramSize;
    private final String template;
    private final Integer paramZwddSize;
    private final String templateZwdd;
    YwxtNoticeTypeEnum(String type, String prefix,Boolean sendZwddMsg,Integer paramSize,String template,Integer paramZwddSize,String templateZwdd) {
        this.type = type;
        this.prefix = prefix;
        this.sendZwddMsg = sendZwddMsg;
        this.paramSize = paramSize;
        this.template = template;
        this.paramZwddSize = paramZwddSize;
        this.templateZwdd = templateZwdd;
    }

    public static YwxtNoticeTypeEnum getEnumByCode(String type){
        YwxtNoticeTypeEnum[] enums = YwxtNoticeTypeEnum.class.getEnumConstants();
        Optional<YwxtNoticeTypeEnum> any = Arrays.stream(enums).filter(e -> e.getType().equals(type) ).findAny();
        return any.orElse(YwxtNoticeTypeEnum.TERMINATE_01);
    }

}
