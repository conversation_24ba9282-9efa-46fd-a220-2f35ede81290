package com.concise.gen.disabledbasiccard.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 残疾人信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:23:58
 */
@Data
@TableName("disabled_basic_card")
public class DisabledBasicCard {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String pkId;

    /**
     * 身份证号
     */
    private String identityCard;

    /**
     * 姓名
     */
    private String name;

    /**
     * 残疾类型
     */
    private String disableType;

    /**
     * 残疾人证号
     */
    private String disableCardNum;

    /**
     * 残疾等级
     */
    private String disableLevel;

    /**
     * 发证日期
     */
    private String issueDate;

    /**
     * 有效期开始
     */
    private String stime;

    /**
     * 有效期结束
     */
    private String etime;

    /**
     * 注销时间
     */
    private String zxtime;

    /**
     * 矫正对象id
     */
    private String jzdxId;

    /**
     * 矫正机构id
     */
    private String jzjg;

    /**
     * 矫正机构
     */
    private String jzjgName;

    private String zhuangtai;

    private Date updateTime;

}
