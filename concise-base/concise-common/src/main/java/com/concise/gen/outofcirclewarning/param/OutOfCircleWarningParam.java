package com.concise.gen.outofcirclewarning.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 出圈预警信息参数类
 *
 * <AUTHOR>
 * @date 2023-07-21 17:39:55
*/
@Data
public class OutOfCircleWarningParam extends BaseParam {

    /**
     * id
     */
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 矫正对象id
     */
    @NotBlank(message = "矫正对象id不能为空，请检查jzdxId参数", groups = {add.class, edit.class})
    private String jzdxId;

    /**
     * 矫正对象姓名
     */
    @NotBlank(message = "矫正对象姓名不能为空，请检查name参数", groups = {add.class, edit.class})
    private String name;

    /**
     * 矫正单位id
     */
    @NotBlank(message = "矫正单位id不能为空，请检查jzjg参数", groups = {add.class, edit.class})
    private String jzjg;

    /**
     * 矫正单位名称
     */
    @NotBlank(message = "矫正单位名称不能为空，请检查jzjgName参数", groups = {add.class, edit.class})
    private String jzjgName;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空，请检查sfzh参数", groups = {add.class, edit.class})
    private String sfzh;

    /**
     * 预警时间
     */
    @NotNull(message = "预警时间不能为空，请检查warningTime参数", groups = {add.class, edit.class})
    private String warningTime;

    /**
     * 是否出界
     */
    @NotBlank(message = "是否出界不能为空，请检查outOfBound参数", groups = {add.class, edit.class})
    private String outOfBound;

}
