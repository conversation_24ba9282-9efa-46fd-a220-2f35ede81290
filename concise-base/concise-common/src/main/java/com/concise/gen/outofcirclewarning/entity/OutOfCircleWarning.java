package com.concise.gen.outofcirclewarning.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.Date;
import java.util.Date;

/**
 * 出圈预警信息
 *
 * <AUTHOR>
 * @date 2023-07-21 17:39:55
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("out_of_circle_warning")
public class OutOfCircleWarning extends BaseEntity {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 矫正对象id
     */
    private String jzdxId;

    /**
     * 矫正对象姓名
     */
    private String name;

    /**
     * 矫正单位id
     */
    private String jzjg;

    /**
     * 矫正单位名称
     */
    private String jzjgName;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 预警时间
     */
    private Date warningTime;

    /**
     * 是否出界
     */
    private String outOfBound;

}
