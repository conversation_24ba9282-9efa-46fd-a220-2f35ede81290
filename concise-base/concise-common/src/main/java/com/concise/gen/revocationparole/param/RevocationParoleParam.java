package com.concise.gen.revocationparole.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
* 提请撤销假释参数类
 *
 * <AUTHOR>
 * @date 2022-06-13 10:14:21
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class RevocationParoleParam extends BaseParam {

    private String processPsnId;
    private String processPsnName;

    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /** 拟呈送法院(新增阶段) */
    private String fyP1;
    private String jcyP1;
    /** 提请依据(新增阶段) */
    private String tqyjP1;
    /** 提请理由(新增阶段) */
    private String tqlyP1;
    /** 备注 */
    private String bzP1;

    /**
     * 统一赋号
     */
    private String tyfh;

    /**
     * 社区矫正案件编号
     */
    private String sqjzajbh;

    /**
     * 社区矫正人员id
     */
    private String sqjzryId;

    /**
     * 罪犯编号
     */
    private String zfbh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    private String xb;

    /**
     * 证件类型
     */
    private String zjlx;

    /**
     * 证件号码
     */

    private String zjhm;

    /**
     * 出生日期
     */
    private String csrq;

    /**
     * 国籍
     */

    private String gj;

    /**
     * 民族
     */

    private String mz;

    /**
     * 户籍地
     */
    private String hjd;

    /**
     * 户籍地省
     */

    private String hjdp;

    /**
     * 户籍地市
     */
    private String hjdc;

    /**
     * 户籍地详址
     */

    private String hjdxz;

    /**
     * 现住地
     */

    private String xzd;

    /**
     * 现住地省
     */

    private String xzdp;

    /**
     * 现住地市
     */

    private String xzdc;

    /**
     * 现住地详址
     */

    private String xzdxz;

    /**
     * 户籍地行政区划编码
     */
    private String hjdCode;
    /**
     * 现住地行政区划编码
     */
    private String xzdCode;

    /**
     * 生效判决机关
     */

    private String sxpjjg;

    /**
     * 判决文书文号
     */

    private String pjwswh;

    /**
     * 判决日期
     */
    private String pjrq;

    /**
     * 判决罪名
     */

    private String pjzm;

    /**
     * 判决其他罪名
     */

    private String pjqtzm;

    /**
     * 是否数罪并罚
     */

    private String sfszbf;

    /**
     * 原判刑罚
     */

    private String ypxf;

    /**
     * 附加刑
     */

    private String fjx;

    /**
     * 禁止令内容
     */

    private String jzlnr;

    /**
     * 禁止期限起日
     */
    private String jzqxqr;

    /**
     * 禁止期止限日
     */
    private String jzqxzr;

    /**
     * 裁定假释法院
     */

    private String cdjsfy;

    /**
     * 裁定假释法院案号
     */

    private String cdjsfyah;

    /**
     * 裁定假释日期
     */
    private String cdjsrq;

    /**
     * 裁定假释文书号
     */

    private String cdjswsh;

    /**
     * 矫正类别
     */

    private String jzlb;

    /**
     * 决定机关
     */

    private String jdjg;

    /**
     * 司法所
     */

    private String sfs;

    /**
     * 社区矫正开始日期
     */
    private String sqjzksrq;

    /**
     * 社区矫正结束日期
     */
    private String sqjzjsrq;

    /**
     * 矫正期限
     */

    private String jzqx;

    /**
     * 社区矫正执行地
     */

    private String sqjzzxd;

    /**
     * 矫正期间表现情况
     */

    private String jzqjbxqk;

    /**
     * 征求意见建议书文号
     */

    private String zqyjjyswh;

    /**
     * 征求意见日期
     */
    private String zqyjrq;

    /**
     * 征求意见提请理由
     */

    private String zqyjtqly;

    /**
     * 征求意见提请依据
     */

    private String zqyjtqyj;

    /**
     * 检察院案件编号
     */

    private String jcyajbh;

    /**
     * 检察意见文书文号
     */

    private String jcyjwswh;

    /**
     * 撤销假释检察意见
     */

    private String cxjsjcyj;

    /**
     * 意见反馈时间
     */
    private String yjfksj;

    /**
     * 检察意见备注
     */

    private String jcyjbz;

    /**
     * 提请日期
     */
    private String tqrq;

    /**
     * 提请收监建议书文号
     */

    private String jyswh;

    /**
     * 提请收监理由
     */

    private String tqly;

    /**
     * 提请收监依据
     */

    private String tqyj;

    /**
     * 矫正单位
     */

    private String jzdwId;
    private String jzdwmc;

    /**
     * 征求检察院
     */

    private String jcy;

    /**
     * 征求检察院名称
     */

    private String jcymc;

    /**
     * 提请法院
     */

    private String fy;

    /**
     * 提请法院名称
     */
    private String fymc;

    /**
     * 提请状态
     */
    private String zt;

    private String files1;
    private String files2;
    private String files3;
    private String files4;

}
