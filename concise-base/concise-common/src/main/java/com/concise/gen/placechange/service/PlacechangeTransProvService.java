package com.concise.gen.placechange.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.placechange.entity.PlacechangeTransProv;
import com.concise.gen.placechange.param.PlacechangeTransProvParam;
import java.util.List;

/**
 * 跨省执行地变更service接口
 *
 * <AUTHOR>
 * @date 2025-04-01 11:09:06
 */
public interface PlacechangeTransProvService extends IService<PlacechangeTransProv> {

    /**
     * 查询跨省执行地变更
     *
     * <AUTHOR>
     * @date 2025-04-01 11:09:06
     */
    PageResult<PlacechangeTransProv> page(PlacechangeTransProvParam placechangeTransProvParam);

    /**
     * 跨省执行地变更列表
     *
     * <AUTHOR>
     * @date 2025-04-01 11:09:06
     */
    List<PlacechangeTransProv> list(PlacechangeTransProvParam placechangeTransProvParam);

    /**
     * 添加跨省执行地变更
     *
     * <AUTHOR>
     * @date 2025-04-01 11:09:06
     */
    void add(PlacechangeTransProvParam placechangeTransProvParam);
    void receive(PlacechangeTransProvParam param);
    String receivePsn(PlacechangeTransProvParam param);
    String receiveOpinion(PlacechangeTransProvParam param);
    String receiveResult(PlacechangeTransProvParam param);

    /**
     * 删除跨省执行地变更
     *
     * <AUTHOR>
     * @date 2025-04-01 11:09:06
     */
    void delete(PlacechangeTransProvParam placechangeTransProvParam);

    /**
     * 移送
     */
    void trans(PlacechangeTransProvParam placechangeTransProvParam);
    void approval(PlacechangeTransProvParam placechangeTransProvParam);
    void feedback(PlacechangeTransProvParam placechangeTransProvParam);

    /**
     * 查看跨省执行地变更
     *
     * <AUTHOR>
     * @date 2025-04-01 11:09:06
     */
     PlacechangeTransProv detail(PlacechangeTransProvParam placechangeTransProvParam);
     PlacechangeTransProv fillInfo(String id);

     void test(String id);
     void export(PlacechangeTransProvParam param);
}
