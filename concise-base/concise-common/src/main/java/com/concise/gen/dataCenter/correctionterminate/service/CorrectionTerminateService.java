package com.concise.gen.dataCenter.correctionterminate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.dataCenter.correctionterminate.entity.CorrectionTerminate;
import com.concise.gen.dataCenter.correctionterminate.param.CorrectionTerminateParam;
import java.util.List;

/**
 * 矫正对象解矫列表service接口
 *
 * <AUTHOR>
 * @date 2022-06-17 15:09:52
 */
public interface CorrectionTerminateService extends IService<CorrectionTerminate> {

    /**
     * 查询矫正对象解矫列表
     *
     * <AUTHOR>
     * @date 2022-06-17 15:09:52
     */
    PageResult<CorrectionTerminate> page(CorrectionTerminateParam correctionTerminateParam);

    /**
     * 矫正对象解矫列表列表
     *
     * <AUTHOR>
     * @date 2022-06-17 15:09:52
     */
    List<CorrectionTerminate> list(CorrectionTerminateParam correctionTerminateParam);

    /**
     * 添加矫正对象解矫列表
     *
     * <AUTHOR>
     * @date 2022-06-17 15:09:52
     */
    void add(CorrectionTerminateParam correctionTerminateParam);

    /**
     * 删除矫正对象解矫列表
     *
     * <AUTHOR>
     * @date 2022-06-17 15:09:52
     */
    void delete(CorrectionTerminateParam correctionTerminateParam);

    /**
     * 编辑矫正对象解矫列表
     *
     * <AUTHOR>
     * @date 2022-06-17 15:09:52
     */
    void edit(CorrectionTerminateParam correctionTerminateParam);

    /**
     * 查看矫正对象解矫列表
     *
     * <AUTHOR>
     * @date 2022-06-17 15:09:52
     */
     CorrectionTerminate detail(CorrectionTerminateParam correctionTerminateParam);

    /**
     * 添加 到协同信息
     * @param correctionTerminateParam
     */
    void addToXt(CorrectionTerminateParam correctionTerminateParam);
}
