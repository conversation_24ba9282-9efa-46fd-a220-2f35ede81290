package com.concise.gen.revocationparole.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.revocationauditphase.param.RevocationAuditPhaseParam;
import com.concise.gen.revocationparole.entity.RevocationParole;
import com.concise.gen.revocationparole.param.RevocationParoleParam;
import com.concise.gen.revocationprobation.param.RevParam;

import java.util.List;

/**
 * 提请撤销假释service接口
 *
 * <AUTHOR>
 * @date 2022-06-13 10:14:21
 */
public interface RevocationParoleService extends IService<RevocationParole> {

    /**
     * 查询提请撤销假释
     *
     * <AUTHOR>
     * @date 2022-06-13 10:14:21
     */
    PageResult<RevocationParole> page(RevocationParoleParam revocationParoleParam);

    /**
     * 提请撤销假释列表
     *
     * <AUTHOR>
     * @date 2022-06-13 10:14:21
     */
    List<RevocationParole> list(RevocationParoleParam revocationParoleParam);

    /**
     * 添加提请撤销假释
     *
     * <AUTHOR>
     * @date 2022-06-13 10:14:21
     */
    void add(RevocationParoleParam revocationParoleParam);

    /**
     * 删除提请撤销假释
     *
     * <AUTHOR>
     * @date 2022-06-13 10:14:21
     */
    void delete(RevocationParoleParam revocationParoleParam);

    /**
     * 编辑提请撤销假释
     *
     * <AUTHOR>
     * @date 2022-06-13 10:14:21
     */
    void edit(RevocationParoleParam revocationParoleParam);

    /**
     * 提请撤销假释 审核
     * @param param param
     */
    void audit(RevocationAuditPhaseParam param);
    /**
     * 提请撤销假释 提交提请
     * @param param param
     */
    void submit(RevocationParoleParam param);

    /**
     * 接收反馈
     * @param param param
     * @param step step
     */
    void updateByTyfh(RevocationParoleParam param, String step);
    /**
     * 查看提请撤销假释
     *
     * <AUTHOR>
     * @date 2022-06-13 10:14:21
     */
     RevocationParole detail(RevocationParoleParam revocationParoleParam);

     void revocationParole(RevParam param);
}
