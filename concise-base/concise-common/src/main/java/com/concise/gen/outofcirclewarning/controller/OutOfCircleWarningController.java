package com.concise.gen.outofcirclewarning. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.outofcirclewarning. param.OutOfCircleWarningParam;
import com.concise.gen.outofcirclewarning. service.OutOfCircleWarningService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 出圈预警信息控制器
 *
 * <AUTHOR>
 * @date 2023-07-21 17:39:55
 */
@Api(tags = "出圈预警信息")
@RestController
public class OutOfCircleWarningController {

    @Resource
    private OutOfCircleWarningService outOfCircleWarningService;

    /**
     * 查询出圈预警信息
     *
     * <AUTHOR>
     * @date 2023-07-21 17:39:55
     */
    @Permission
    @GetMapping("/outOfCircleWarning/page")
    @ApiOperation("出圈预警信息_分页查询")
    public ResponseData page(OutOfCircleWarningParam outOfCircleWarningParam) {
        return new SuccessResponseData(outOfCircleWarningService.page(outOfCircleWarningParam));
    }

    /**
     * 添加出圈预警信息
     *
     * <AUTHOR>
     * @date 2023-07-21 17:39:55
     */
    @Permission
    @PostMapping("/outOfCircleWarning/add")
    @ApiOperation("出圈预警信息_增加")
    @BusinessLog(title = "出圈预警信息_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(OutOfCircleWarningParam.add.class) OutOfCircleWarningParam outOfCircleWarningParam) {
        outOfCircleWarningService.add(outOfCircleWarningParam);
        return new SuccessResponseData();
    }

    /**
     * 删除出圈预警信息
     *
     * <AUTHOR>
     * @date 2023-07-21 17:39:55
     */
    @Permission
    @PostMapping("/outOfCircleWarning/delete")
    @ApiOperation("出圈预警信息_删除")
    @BusinessLog(title = "出圈预警信息_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(OutOfCircleWarningParam.delete.class) OutOfCircleWarningParam outOfCircleWarningParam) {
        outOfCircleWarningService.delete(outOfCircleWarningParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑出圈预警信息
     *
     * <AUTHOR>
     * @date 2023-07-21 17:39:55
     */
    @Permission
    @PostMapping("/outOfCircleWarning/edit")
    @ApiOperation("出圈预警信息_编辑")
    @BusinessLog(title = "出圈预警信息_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(OutOfCircleWarningParam.edit.class) OutOfCircleWarningParam outOfCircleWarningParam) {
        outOfCircleWarningService.edit(outOfCircleWarningParam);
        return new SuccessResponseData();
    }

    /**
     * 查看出圈预警信息
     *
     * <AUTHOR>
     * @date 2023-07-21 17:39:55
     */
    @Permission
    @GetMapping("/outOfCircleWarning/detail")
    @ApiOperation("出圈预警信息_查看")
    public ResponseData detail(@Validated(OutOfCircleWarningParam.detail.class) OutOfCircleWarningParam outOfCircleWarningParam) {
        return new SuccessResponseData(outOfCircleWarningService.detail(outOfCircleWarningParam));
    }

    /**
     * 出圈预警信息列表
     *
     * <AUTHOR>
     * @date 2023-07-21 17:39:55
     */
    @Permission
    @GetMapping("/outOfCircleWarning/list")
    @ApiOperation("出圈预警信息_列表")
    public ResponseData list(OutOfCircleWarningParam outOfCircleWarningParam) {
        return new SuccessResponseData(outOfCircleWarningService.list(outOfCircleWarningParam));
    }


    @GetMapping("/outOfCircleWarning/export")
    @ApiOperation("出圈预警信息_导出")
    public void export(OutOfCircleWarningParam outOfCircleWarningParam) {
        outOfCircleWarningService.list(outOfCircleWarningParam);
    }

}
