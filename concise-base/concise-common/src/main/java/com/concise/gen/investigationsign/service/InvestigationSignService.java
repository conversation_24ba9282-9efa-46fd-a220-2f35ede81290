package com.concise.gen.investigationsign.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.pojo.PdfSignatureParams;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.pojo.response.ResponseData;
import com.concise.gen.investigation.param.InvestigationTranscriptParam;
import com.concise.gen.investigationsign.entity.InvestigationSign;
import com.concise.gen.investigationsign.param.InvestigationSignParam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 调查评估_手签service接口
 *
 * <AUTHOR>
 * @date 2025-04-21 20:30:30
 */
public interface InvestigationSignService extends IService<InvestigationSign> {

    /**
     * 查询调查评估_手签
     *
     * <AUTHOR>
     * @date 2025-04-21 20:30:30
     */
    PageResult<InvestigationSign> page(InvestigationSignParam investigationSignParam);

    /**
     * 调查评估_手签列表
     *
     * <AUTHOR>
     * @date 2025-04-21 20:30:30
     */
    List<InvestigationSign> list(InvestigationSignParam investigationSignParam);

    /**
     * 添加调查评估_手签
     *
     * <AUTHOR>
     * @date 2025-04-21 20:30:30
     */
    void add(InvestigationSignParam investigationSignParam);

    /**
     * 删除调查评估_手签
     *
     * <AUTHOR>
     * @date 2025-04-21 20:30:30
     */
    void delete(InvestigationSignParam investigationSignParam);

    /**
     * 编辑调查评估_手签
     *
     * <AUTHOR>
     * @date 2025-04-21 20:30:30
     */
    void edit(InvestigationSignParam investigationSignParam);

    /**
     * 查看调查评估_手签
     *
     * <AUTHOR>
     * @date 2025-04-21 20:30:30
     */
    InvestigationSign detail(InvestigationSignParam investigationSignParam);

    /**
     * 新增浙政钉签名用户
     *
     * @param investigationSignParamList
     */
    void addZzDingUser(List<InvestigationSignParam> investigationSignParamList);

    /**
     * 新增浙里办签名用户
     *
     * @param investigationSignParamList
     */
    void addZlBUser(List<InvestigationSignParam> investigationSignParamList);

    /**
     * 更新pdf签名
     */
    void signed(InvestigationSign investigationSign);

    /**
     * 给pdf根据位置和参数进行签名
     *
     * @param sysFileInfo
     * @param signatureParams
     * @param sort
     * @return
     */
    SysFileInfo signByPdfAndPosition(SysFileInfo sysFileInfo, PdfSignatureParams signatureParams, int sort, byte[] bytes);

    /**
     * 删除旧的签名
     *
     * @param id
     * @param bizType
     */
    void deleteOld(String id, String bizType);

    /**
     * 给pdf根据位置进行签名,坐标从左下角计算
     *
     * @param id     文件id
     * @param x      x轴坐标
     * @param y      y轴坐标
     * @param pageNo 页码
     * @param signId 签名id
     * @param type   1：表示手动上传的
     * @return
     */
    SysFileInfo signByPosition(String id, float x, float y, Integer pageNo, String signId, String bizType, String infoId, String type);

    SysFileInfo signByDevice(String id, String bizType, String infoId, String imgBase64Signature, String imgBase64Fingerprint);


    /**
     * 批量签名
     *
     * @param paperId
     * @param response
     */
    void batchSign(String paperId, HttpServletResponse response);

    /**
     * 对base64pdf进行批量签名
     */
    String batchSignBase64(String base64Pdf, PdfSignatureParams params, List<byte[]> bytesList);

    /**
     * 在PDF中查找指定文本后添加多个图片并自动排列
     *
     * @param params   请求参数
     * @param response HTTP响应对象
     */
    void addImages(PdfSignatureParams params, HttpServletResponse response);

    /**
     * 定位文本位置
     *
     * @param params 请求参数
     */
    ResponseData findTextLocations(PdfSignatureParams params);

    /**
     * @param fileId
     * @param signId
     * @param sort
     * @return
     */
    byte[] test(String fileId, String signId, Integer sort);

    /**
     * 判断当前环节是否有待签名的
     *
     * @param id
     * @param bizType
     * @return
     */
    ResponseData check(String id, String bizType);

    /**
     * 处理还未来得及签名的，标记为已过期
     */
    void handleNoSign(String id, String bizType);

    /**
     * 适用于制作文书处没签完名，提交时补签名
     */
    String retroSignature(String id, String fileIds, String bizType);

    /**
     * 从设备签字记录中提取信息重新签字
     *
     * @param pdfBase64 pdfBase64
     * @param id        id
     * @param bizType   bizType
     * @param offsetY   offsetY
     * @return byte[]
     */
    byte[] getSinglePointAllDeviceSignedLog(String pdfBase64, String id, String bizType, float offsetY);

    /**
     * 适用于调查评估表，集体评议和审批，如果前面环节已经签字，将签字内容签在当前环节的pdf里面
     */
    String mergeSignature(String base64, String pgzt, String id, String bizType);

    /**
     * 给pdf进行预签名，查看效果,用于移动端
     *
     * @param id
     * @param base64Sign
     */
    String preSign(String id, String base64Sign);

    /**
     * 将旧的签名替换为新的
     *
     * @param oldId
     * @param newId
     */
    void changeNewSignFileInfo(String oldId, String newId);


    /**
     * 给pdf进行预签名，查看效果,用于移动端（用于笔录）
     *
     * @param id         附件表id
     * @param base64Sign
     */
    SysFileInfo preSignBl(String id, String base64Sign);

    /**
     * 给pdf进行预签名，查看效果,用于平板端（用于笔录）不旋转90度
     *
     * @param id         附件表id
     * @param base64Sign
     */
    SysFileInfo preSignBlBland(String id, String base64Sign);

    /**
     * 用于ccgf调用，笔录确认后提交
     *
     * @param investigationTranscriptParam
     */
    String subSign(InvestigationTranscriptParam investigationTranscriptParam);

    /**
     * 指定坐标签名
     *
     * @param id             文件id
     * @param x              起始坐标（x）
     * @param y              起始坐标（y）
     * @param width          图片宽度
     * @param height         图片高度
     * @param pageNo         页数
     * @param signFileBase64 签名base64
     * @return
     */
    SysFileInfo signByDrag(String id, float x, float y, float width, float height, Integer pageNo, String signFileBase64);
}
