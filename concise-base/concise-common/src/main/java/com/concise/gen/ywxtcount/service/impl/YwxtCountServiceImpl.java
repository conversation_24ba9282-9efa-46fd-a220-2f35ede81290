package com.concise.gen.ywxtcount.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.consts.CommonConstant;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.sysorg.entity.OrgCommon;
import com.concise.gen.sysorg.service.OrgCommonService;
import com.concise.gen.ywxtcount.entity.YwxtCount;
import com.concise.gen.ywxtcount.mapper.YwxtCountMapper;
import com.concise.gen.ywxtcount.param.YwxtCountParam;
import com.concise.gen.ywxtcount.service.YwxtCountService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 协同信息统计表service接口实现类
 *
 * <AUTHOR>
 * @date 2024-01-23 14:08:03
 */
@Service
public class YwxtCountServiceImpl extends ServiceImpl<YwxtCountMapper, YwxtCount> implements YwxtCountService {

    @Resource
    private OrgCommonService orgCommonService;
    @Override
    public PageResult<YwxtCount> page(YwxtCountParam param) {
        QueryWrapper<YwxtCount> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            queryWrapper.lambda().eq(YwxtCount::getDeptId, param.getDeptId());
            // 根据数据类型 查询
            if (ObjectUtil.isNotEmpty(param.getSjlx())) {
                queryWrapper.lambda().eq(YwxtCount::getSjlx, param.getSjlx());
            }

            // 交换日期
            if (ObjectUtil.isNotEmpty(param.getSearchBeginTime())) {
                queryWrapper.lambda().ge(YwxtCount::getJhrq, param.getSearchBeginTime());
            }
            if (ObjectUtil.isNotEmpty(param.getSearchEndTime())) {
                queryWrapper.lambda().le(YwxtCount::getJhrq, param.getSearchEndTime());
            }
        }
        queryWrapper.lambda().orderByDesc(YwxtCount::getJhrq);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<YwxtCount> list(YwxtCountParam ywxtCountParam) {
        return this.list();
    }

    @Override
    public void add(YwxtCountParam ywxtCountParam) {
        YwxtCount ywxtCount = new YwxtCount();
        BeanUtil.copyProperties(ywxtCountParam, ywxtCount);
        this.save(ywxtCount);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(YwxtCountParam ywxtCountParam) {
        this.removeById(ywxtCountParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(YwxtCountParam ywxtCountParam) {
        YwxtCount ywxtCount = this.getById(ywxtCountParam.getId());
        BeanUtil.copyProperties(ywxtCountParam, ywxtCount);
        this.updateById(ywxtCount);
    }

    @Override
    public YwxtCount detail(YwxtCountParam ywxtCountParam) {
        return this.getById(ywxtCountParam.getId());
    }

    @Override
    public void saveOrUpdateCount(String type, String date, String orgId, int count, boolean updateParent) {
        YwxtCount ywxtCount = this.lambdaQuery()
                .eq(YwxtCount::getSjlx, type)
                .eq(YwxtCount::getJhrq, date)
                .eq(YwxtCount::getDeptId, orgId)
                .last("limit 1")
                .one();
        if (ywxtCount == null) {
            ywxtCount = new YwxtCount();
            ywxtCount.setId(IdUtil.simpleUUID());
            ywxtCount.setSjlx(type);
            ywxtCount.setJhrq(date);
            ywxtCount.setDeptId(orgId);
        }
        ywxtCount.setTbsl(count);
        this.saveOrUpdate(ywxtCount);
        if (!updateParent) {
            return;
        }
        OrgCommon org = orgCommonService.getById(orgId);
        if (org.getLevel()!=1) {
            updateParentCount(type,date,org);
        }
    }

    @Override
    public void build44001Count(String date) {
        this.baseMapper.deleteCount(date,"T02");
        this.baseMapper.build44001Count(date);
        buildParent(date,"T02");

    }

    @Override
    public void buildLetterCount(String date) {
        this.baseMapper.deleteCount(date,"T08");
        this.baseMapper.buildLetterCount(date);
        buildParent(date,"T08");
    }

    private void buildParent(String date,String type) {
        List<OrgCommon> cityList = orgCommonService.lambdaQuery().eq(OrgCommon::getPid, CommonConstant.TOP_ORG).eq(OrgCommon::getType, "sfs").list();
        cityList.forEach(city -> {
            orgCommonService.lambdaQuery().eq(OrgCommon::getPid, city.getId()).eq(OrgCommon::getType, "sfs").list()
                    .forEach(country -> this.baseMapper.buildCount(date,type,country.getId()));
            this.baseMapper.buildCount(date,type,city.getId());
        });
        this.baseMapper.buildCount(date,type,CommonConstant.TOP_ORG);
    }

    private void updateParentCount(String type,String date, OrgCommon org){
        List<YwxtCount> list = this.lambdaQuery().eq(YwxtCount::getSjlx, type).eq(YwxtCount::getJhrq, date).eq(YwxtCount::getDeptId, org.getPid()).list();
        String id = IdUtil.simpleUUID();
        if (list.size()>0){
            id = list.get(0).getId();
        }
        this.baseMapper.saveOrUpdateCount(id,org.getPid(),type,date);
        OrgCommon pOrg = orgCommonService.getById(org.getPid());
        if (pOrg.getLevel()!=1) {
            updateParentCount(type,date,pOrg);
        }
    }

}
