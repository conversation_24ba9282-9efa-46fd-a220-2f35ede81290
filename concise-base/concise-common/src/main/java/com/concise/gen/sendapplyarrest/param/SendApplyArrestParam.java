package com.concise.gen.sendapplyarrest.param;

import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
* 发送提请逮捕参数类
 *
 * <AUTHOR>
 * @date 2023-09-20 14:47:19
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class SendApplyArrestParam extends BaseParam {


    private List<AcceptCorrectionDocParam> docList;

    /**
     *
     */
    @NotNull(message = "不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;
    /**
     * 矫正单位id
     */
    private String jzjgId;
    /**
     * 状态
     * 0待发送
     * 1待反馈回执
     * 2待作出决定
     * 3已完结
     */
    @NotBlank(message = "状态不能为空，请检查zt参数", groups = {add.class, edit.class})
    private String zt;
    /**
     * 文书材料
     */
    private String ws;

    /**
     * 协同受理法院
     */
    @NotBlank(message = "协同受理法院不能为空，请检查xtfy参数", groups = {add.class, edit.class})
    private String xtfy;
    private String xtfyName;

    /**
     * 协同检察院
     */
    @NotBlank(message = "协同检察院不能为空，请检查xtjcy参数", groups = {add.class, edit.class})
    private String xtjcy;
    private String xtjcyName;

    /**
     * 发送单位
     */
    @NotBlank(message = "发送单位不能为空，请检查fsdw参数", groups = {add.class, edit.class})
    private String fsdw;

    /**
     * 发送时间
     */
    @NotNull(message = "发送时间不能为空，请检查fssj参数", groups = {add.class, edit.class})
    private String fssj;

    /**
     * 统一赋号
     */
    @NotBlank(message = "统一赋号不能为空，请检查tyfh参数", groups = {add.class, edit.class})
    private String tyfh;

    /**
     * 社区矫正案件编号
     */
    @NotBlank(message = "社区矫正案件编号不能为空，请检查sqjzajbh参数", groups = {add.class, edit.class})
    private String sqjzajbh;

    /**
     * 罪犯编号
     */
    @NotBlank(message = "罪犯编号不能为空，请检查zfbh参数", groups = {add.class, edit.class})
    private String zfbh;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空，请检查xb参数", groups = {add.class, edit.class})
    private String xb;

    /**
     * 证件类型
     */
    @NotBlank(message = "证件类型不能为空，请检查zjlx参数", groups = {add.class, edit.class})
    private String zjlx;

    /**
     * 证件号码
     */
    @NotBlank(message = "证件号码不能为空，请检查zjhm参数", groups = {add.class, edit.class})
    private String zjhm;

    /**
     * 出生日期
     */
    @NotNull(message = "出生日期不能为空，请检查csrq参数", groups = {add.class, edit.class})
    private String csrq;

    /**
     * 户籍地
     */
    @NotBlank(message = "户籍地不能为空，请检查hjd参数", groups = {add.class, edit.class})
    private String hjd;

    /**
     * 户籍地详址
     */
    @NotBlank(message = "户籍地详址不能为空，请检查hjdxz参数", groups = {add.class, edit.class})
    private String hjdxz;

    /**
     * 现住地
     */
    @NotBlank(message = "现住地不能为空，请检查xzd参数", groups = {add.class, edit.class})
    private String xzd;

    /**
     * 现住地详址
     */
    @NotBlank(message = "现住地详址不能为空，请检查xzdxz参数", groups = {add.class, edit.class})
    private String xzdxz;

    /**
     * 生效判决机关
     */
    @NotBlank(message = "生效判决机关不能为空，请检查sxpjjg参数", groups = {add.class, edit.class})
    private String sxpjjg;

    /**
     * 判决文书文号
     */
    @NotBlank(message = "判决文书文号不能为空，请检查pjwswh参数", groups = {add.class, edit.class})
    private String pjwswh;

    /**
     * 判决日期
     */
    @NotNull(message = "判决日期不能为空，请检查pjrq参数", groups = {add.class, edit.class})
    private String pjrq;

    /**
     * 判决罪名
     */
    @NotBlank(message = "判决罪名不能为空，请检查pjzm参数", groups = {add.class, edit.class})
    private String pjzm;

    /**
     * 判决其他罪名
     */
    @NotBlank(message = "判决其他罪名不能为空，请检查pjqtzm参数", groups = {add.class, edit.class})
    private String pjqtzm;

    /**
     * 原判刑罚
     */
    @NotBlank(message = "原判刑罚不能为空，请检查ypxf参数", groups = {add.class, edit.class})
    private String ypxf;

    /**
     * 附加刑
     */
    @NotBlank(message = "附加刑不能为空，请检查fjx参数", groups = {add.class, edit.class})
    private String fjx;

    /**
     * 禁止令内容
     */
    @NotBlank(message = "禁止令内容不能为空，请检查jzlnr参数", groups = {add.class, edit.class})
    private String jzlnr;

    /**
     * 禁止期限起日
     */
    @NotNull(message = "禁止期限起日不能为空，请检查jzqxqr参数", groups = {add.class, edit.class})
    private String jzqxqr;

    /**
     * 禁止期限止日
     */
    @NotNull(message = "禁止期限止日不能为空，请检查jzqxzr参数", groups = {add.class, edit.class})
    private String jzqxzr;

    /**
     * 裁定假释法院
     */
    @NotBlank(message = "裁定假释法院不能为空，请检查cdjsfy参数", groups = {add.class, edit.class})
    private String cdjsfy;

    /**
     * 裁定假释法院案号
     */
    @NotBlank(message = "裁定假释法院案号不能为空，请检查cdjsfyah参数", groups = {add.class, edit.class})
    private String cdjsfyah;

    /**
     * 裁定假释日期
     */
    @NotNull(message = "裁定假释日期不能为空，请检查cdjsrq参数", groups = {add.class, edit.class})
    private String cdjsrq;

    /**
     * 裁定假释文书号
     */
    @NotBlank(message = "裁定假释文书号不能为空，请检查cdjswsh参数", groups = {add.class, edit.class})
    private String cdjswsh;

    /**
     * 矫正类别
     */
    @NotBlank(message = "矫正类别不能为空，请检查jzlb参数", groups = {add.class, edit.class})
    private String jzlb;

    /**
     * 决定机关
     */
    @NotBlank(message = "决定机关不能为空，请检查jdjg参数", groups = {add.class, edit.class})
    private String jdjg;

    /**
     * 司法所
     */
    @NotBlank(message = "司法所不能为空，请检查sfs参数", groups = {add.class, edit.class})
    private String sfs;

    /**
     * 社区矫正开始日期
     */
    @NotNull(message = "社区矫正开始日期不能为空，请检查sqjzksrq参数", groups = {add.class, edit.class})
    private String sqjzksrq;

    /**
     * 社区矫正结束日期
     */
    @NotNull(message = "社区矫正结束日期不能为空，请检查sqjzjsrq参数", groups = {add.class, edit.class})
    private String sqjzjsrq;

    /**
     * 矫正期限
     */
    @NotBlank(message = "矫正期限不能为空，请检查jzqx参数", groups = {add.class, edit.class})
    private String jzqx;

    /**
     * 社区矫正执行地
     */
    @NotBlank(message = "社区矫正执行地不能为空，请检查sqjzzxd参数", groups = {add.class, edit.class})
    private String sqjzzxd;

    /**
     * 建议书文号
     */
    @NotBlank(message = "建议书文号不能为空，请检查jyswh参数", groups = {add.class, edit.class})
    private String jyswh;

    /**
     * 提请日期
     */
    @NotNull(message = "提请日期不能为空，请检查tqrq参数", groups = {add.class, edit.class})
    private String tqrq;

    /**
     * 事由及依据
     */
    @NotBlank(message = "事由及依据不能为空，请检查syjyj参数", groups = {add.class, edit.class})
    private String syjyj;

    /**
     * 回执结果
     */
    @NotBlank(message = "回执结果不能为空，请检查hzjg参数", groups = {add.class, edit.class})
    private String hzjg;

    /**
     * 回执时间
     */
    @NotNull(message = "回执时间不能为空，请检查hzsj参数", groups = {add.class, edit.class})
    private String hzsj;

    /**
     * 回执说明
     */
    @NotBlank(message = "回执说明不能为空，请检查hzsm参数", groups = {add.class, edit.class})
    private String hzsm;

    /**
     * 提请逮捕决定
     */
    @NotBlank(message = "提请逮捕决定不能为空，请检查tqdbjd参数", groups = {add.class, edit.class})
    private String tqdbjd;

    /**
     * 反馈日期
     */
    @NotNull(message = "反馈日期不能为空，请检查fkrq参数", groups = {add.class, edit.class})
    private String fkrq;

    /**
     * 决定说明
     */
    @NotBlank(message = "决定说明不能为空，请检查jdsm参数", groups = {add.class, edit.class})
    private String jdsm;

    /**
     * 决定文书号
     */
    @NotBlank(message = "决定文书号不能为空，请检查jdwsh参数", groups = {add.class, edit.class})
    private String jdwsh;

}
