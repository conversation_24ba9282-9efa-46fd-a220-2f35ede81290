package com.concise.gen.documentserial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.documentserial.entity.DocumentSerial;
import com.concise.gen.documentserial.param.DocumentSerialParam;

import java.util.List;

/**
 * 文书号维护service接口
 *
 * <AUTHOR>
 * @date 2025-03-27 10:09:11
 */
public interface DocumentSerialService extends IService<DocumentSerial> {

    /**
     * 调查评估 矫调评 获取文书号
     * @param deptId 区县局机构id
     * @param type 文书类型，字典值：WSLX
     * @param sName 市、区简称，如湖州市南浔区---> 湖南
     * @return
     */
    String getSerial(String deptId, String type, String sName, String referId);

    /**
     *  文书号校验
     * @return
     */
    String checkSerialNum(DocumentSerialParam param);
}
