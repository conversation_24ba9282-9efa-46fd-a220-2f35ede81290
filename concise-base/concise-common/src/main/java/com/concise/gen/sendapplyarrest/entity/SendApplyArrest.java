package com.concise.gen.sendapplyarrest.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 发送提请逮捕
 *
 * <AUTHOR>
 * @date 2023-09-20 14:47:19
 */
@Data
@TableName("send_apply_arrest")
public class SendApplyArrest{

    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 矫正单位id
     */
    private String jzjgId;
    private String jzjgCode;
    private String jzjgPids;

    /**
     * 状态
     * 0待发送
     * 1待反馈回执
     * 2待作出决定
     * 3已完结
     */
    private String zt;
    /**
     * 文书材料
     */
    private String ws;

    /**
     * 协同受理法院
     */
    private String xtfy;
    private String xtfyName;

    /**
     * 协同检察院
     */
    private String xtjcy;
    private String xtjcyName;

    /**
     * 发送单位
     */
    private String fsdw;

    /**
     * 发送时间
     */
    private Date fssj;

    /**
     * 统一赋号
     */
    private String tyfh;

    /**
     * 社区矫正案件编号
     */
    private String sqjzajbh;

    /**
     * 罪犯编号
     */
    private String zfbh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    private String xb;

    /**
     * 证件类型
     */
    private String zjlx;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 出生日期
     */
    private Date csrq;

    /**
     * 户籍地
     */
    private String hjd;

    /**
     * 户籍地详址
     */
    private String hjdxz;

    /**
     * 现住地
     */
    private String xzd;

    /**
     * 现住地详址
     */
    private String xzdxz;

    /**
     * 生效判决机关
     */
    private String sxpjjg;

    /**
     * 判决文书文号
     */
    private String pjwswh;

    /**
     * 判决日期
     */
    private Date pjrq;

    /**
     * 判决罪名
     */
    private String pjzm;

    /**
     * 判决其他罪名
     */
    private String pjqtzm;

    /**
     * 原判刑罚
     */
    private String ypxf;

    /**
     * 附加刑
     */
    private String fjx;

    /**
     * 禁止令内容
     */
    private String jzlnr;

    /**
     * 禁止期限起日
     */
    private Date jzqxqr;

    /**
     * 禁止期限止日
     */
    private Date jzqxzr;

    /**
     * 裁定假释法院
     */
    private String cdjsfy;

    /**
     * 裁定假释法院案号
     */
    private String cdjsfyah;

    /**
     * 裁定假释日期
     */
    private Date cdjsrq;

    /**
     * 裁定假释文书号
     */
    private String cdjswsh;

    /**
     * 矫正类别
     */
    private String jzlb;

    /**
     * 决定机关
     */
    private String jdjg;

    /**
     * 司法所
     */
    private String sfs;

    /**
     * 社区矫正开始日期
     */
    private Date sqjzksrq;

    /**
     * 社区矫正结束日期
     */
    private Date sqjzjsrq;

    /**
     * 矫正期限
     */
    private String jzqx;

    /**
     * 社区矫正执行地
     */
    private String sqjzzxd;

    /**
     * 建议书文号
     */
    private String jyswh;

    /**
     * 提请日期
     */
    private Date tqrq;

    /**
     * 事由及依据
     */
    private String syjyj;

    /**
     * 回执结果
     */
    private String hzjg;

    /**
     * 回执时间
     */
    private Date hzsj;

    /**
     * 回执说明
     */
    private String hzsm;

    /**
     * 提请逮捕决定
     */
    private String tqdbjd;

    /**
     * 反馈日期
     */
    private Date fkrq;

    /**
     * 决定说明
     */
    private String jdsm;

    /**
     * 决定文书号
     */
    private String jdwsh;

}
