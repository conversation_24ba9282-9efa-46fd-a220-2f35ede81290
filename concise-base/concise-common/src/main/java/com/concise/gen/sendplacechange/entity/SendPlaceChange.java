package com.concise.gen.sendplacechange.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 发送变更执行地通知
 *
 * <AUTHOR>
 * @date 2023-09-13 14:39:13
 */
@Data
@TableName("send_place_change")
public class SendPlaceChange{

    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;


    private String ws;

    /**
     * 类别
     * 0 迁出
     * 1 迁入
     */
    private String type;

    /**
     * 状态
     * 0 待发送
     * 1 已发送
     */
    private String zt;

    /**
     * 矫正单位id
     */
    private String jzjgId;
    private String jzjgName;

    /**
     * 迁入单位id
     */
    private String qrdwId;
    private String qrdwName;

    /**
     * 社区矫正决定机关
     */
    private String sqjzjdjg;

    /**
     * 抄送公安
     */
    private String csga;

    /**
     * 抄送检察院
     */
    private String csjcy;
    /**
     * 监狱
     */
    private String csjy;
    /**
     * 看守所
     */
    private String cskss;

    /**
     * 发送单位
     */
    private String fsdw;

    /**
     * 发送时间
     */
    private Date fssj;

    /**
     * 统一赋号
     */
    private String tyfh;

    /**
     * 社区矫正案件编号
     */
    private String sqjzajbh;

    /**
     * 罪犯编号
     */
    private String zfbh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    private String xb;

    /**
     * 证件类型
     */
    private String zjlx;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 出生日期
     */
    private Date csrq;

    /**
     * 户籍地
     */
    private String hjd;

    /**
     * 户籍地详址
     */
    private String hjdxz;

    /**
     * 现住地
     */
    private String xzd;

    /**
     * 现住地详址
     */
    private String xzdxz;

    /**
     * 生效判决机关
     */
    private String sxpjjg;

    /**
     * 判决文书文号
     */
    private String pjwswh;

    /**
     * 判决日期
     */
    private Date pjrq;

    /**
     * 判决罪名
     */
    private String pjzm;

    /**
     * 判决其他罪名
     */
    private String pjqtzm;

    /**
     * 主刑
     */
    private String zx;

    /**
     * 附加刑
     */
    private String fjx;

    /**
     * 矫正类别
     */
    private String jzlb;

    /**
     * 决定机关
     */
    private String jdjg;

    /**
     * 司法所
     */
    private String sfs;

    /**
     * 社区矫正开始日期
     */
    private Date sqjzksrq;

    /**
     * 社区矫正结束日期
     */
    private Date sqjzjsrq;

    /**
     * 矫正期限
     */
    private String jzqx;

    /**
     * 社区矫正执行地
     */
    private String sqjzzxd;

    /**
     * 迁出地
     */
    private String qcd;

    /**
     * 迁入地
     */
    private String qrd;
    private String qrdCode;

    /**
     * 事由及依据
     */
    private String syjyj;

    /**
     * 申请日期
     */
    private Date sqrq;

    private String cbr;
    private String cbrdh;
    private String cbrbm;
    private String lxdh;

    private int deleted;
}
