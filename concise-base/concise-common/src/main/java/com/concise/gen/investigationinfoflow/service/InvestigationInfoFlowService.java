package com.concise.gen.investigationinfoflow.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.gen.investigation.param.InvestigationApprovalParam;
import com.concise.gen.investigation.param.InvestigationDeliberationParam;
import com.concise.gen.investigation.param.InvestigationGroupParam;
import com.concise.gen.investigation.param.InvestigationReviewParam;
import com.concise.gen.investigationinfoflow.entity.InvestigationInfoFlow;
import com.concise.gen.investigationinfoflow.param.InvestigationInfoFlowParam;

import java.util.List;
import java.util.Map;

/**
 * 调查评估-流程记录service接口
 *
 * <AUTHOR>
 * @date 2025-03-25 09:02:58
 */
public interface InvestigationInfoFlowService extends IService<InvestigationInfoFlow> {


    /**
     * 调查评估-流程记录列表
     *
     * <AUTHOR>
     * @date 2025-03-25 09:02:58
     */
    List<InvestigationInfoFlow> list(String investigationInfoId);

    /**
     * 添加调查评估-流程记录
     *
     * <AUTHOR>
     * @date 2025-03-25 09:02:58
     */
    void add(InvestigationInfoFlowParam investigationInfoFlowParam);

    /**
     * 开启流程时，初始化所有流程节点
     */
    void initFlow(String investigationInfoId);

    /**
     * 待调查退回时更新流程记录
     */
    void ddcBack(InvestigationGroupParam param);

    /**
     * 待初审/小组意见退回时更新流程记录
     */
    void xzyjBack(InvestigationDeliberationParam param);

    /**
     * 待初审/评议退回时更新流程记录
     */
    void pyBack(InvestigationReviewParam param);

    /**
     * 待审批退回时更新流程记录
     */
    void dspBack(InvestigationApprovalParam param);


    /**
     * 修改数据，并返回实际修改的行数
     */
    int uptBackNum(LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper);

    /**
     * 调查评估-流程记录列表
     *
     * <AUTHOR>
     * @date 2025-03-25 09:02:58
     */
    List<Map<String, Object>> listFlow(String investigationInfoId);

    /**
     * 标记暂存状态
     * @param investigationInfoId
     * @param stepCode
     */
    void draftTag(String investigationInfoId, String stepCode);
}
