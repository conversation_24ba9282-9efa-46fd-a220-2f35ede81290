package com.concise.gen.revocationparole.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.gen.revocationauditphase.entity.TaskInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 提请撤销假释
 *
 * <AUTHOR>
 * @date 2022-06-13 10:14:21
 */
@Data
@TableName("revocation_parole")
public class RevocationParole  {
    /** 拟呈送法院(新增阶段) */
    private String fyP1;
    private String jcyP1;
    /** 提请依据(新增阶段) */
    private String tqyjP1;
    /** 提请理由(新增阶段) */
    private String tqlyP1;
    /** 备注 */
    private String bzP1;
    /** 提交申请时间 */
    private Date addTime;
    private String addPsnName;
    private String addPsnId;
    private String processType;
    @TableField(exist = false)
    private String processNodes;
    /** 流程实例id */
    private String processInstanceId;
    @TableField(exist = false)
    private List<TaskInfo> taskInfoList;
    @TableField(exist = false)
    private JSONObject phase;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;


    /** 统一赋号 */
    private String tyfh;

    /** 社区矫正案件编号 */
    private String sqjzajbh;
    /** 社区矫正人员id */
    private String sqjzryId;

    /** 罪犯编号 */
    private String zfbh;

    /** 姓名 */
    private String xm;

    /** 性别 */
    private String xb;

    /** 证件号码 */
    private String zjhm;

    /** 出生日期 */
    private Date csrq;

    /** 国籍 */
    private String gj;

    /** 民族 */
    private String mz;

    /**  户籍地/户籍地址 */
    private String hjd;

    /**  户籍地详址/户籍地址明细 */
    private String hjdxz;

    /**  现住地/居住地地址 */
    private String xzd;

    /**  现住地详址/住地地址明细 */
    private String xzdxz;

    /**  生效判决机关 */
    private String sxpjjg;

    /**  判决文书文号 */
    private String pjwswh;

    /**  判决日期 */
    private Date pjrq;

    /**  判决罪名 */
    private String pjzm;

    /**  判决其他罪名 */
    private String pjqtzm;

    /** 是否数罪并罚 */
    private String sfszbf;

    /** 原判刑罚 */
    private String ypxf;

    /**  附加刑 */
    private String fjx;

    /**  禁止令内容 */
    private String jzlnr;

    /**  禁止期限起日 */
    private Date jzqxqr;

    /**  禁止期止限日 */
    private Date jzqxzr;

    /** 裁定假释法院 */
    private String cdjsfy;

    /** 裁定假释法院案号 */
    private String cdjsfyah;

    /** 裁定假释日期 */
    private Date cdjsrq;

    /** 裁定假释文书号 */
    private String cdjswsh;

    /** 矫正类别 */
    private String jzlb;

    /**  决定机关 */
    private String jdjg;

    /**  司法所 */
    private String sfs;

    /**  社区矫正开始日期 */
    private Date sqjzksrq;

    /**  社区矫正结束日期 */
    private Date sqjzjsrq;

    /**  矫正期限 */
    private String jzqx;

    /**  社区矫正执行地 */
    private String sqjzzxd;

    /** 矫正期间表现情况 */
    private String jzqjbxqk;

    /**  征求意见建议书文号 */
    private String zqyjjyswh;

    /** 征求意见日期 */
    private Date zqyjrq;


    /**  征求意见提请理由 */
    private String zqyjtqly;

    /**  征求意见提请依据 */
    private String zqyjtqyj;

    /** 检察院案件编号 */
    private String jcyajbh;

    /** 检察意见文书文号 */
    private String jcyjwswh;

    /** 撤销假释检察意见 */
    private String cxjsjcyj;

    /** 意见反馈时间 */
    private Date yjfksj;

    /** 检察意见备注 */
    private String jcyjbz;

    /** 提请日期 */
    private Date tqrq;

    /** 提请收监建议书文号 */
    private String jyswh;

    /** 提请收监理由 */
    private String tqly;

    /** 提请收监依据 */
    private String tqyj;

    /** 矫正单位 */
    private String jzdw;
    private String jzdwmc;
    private String jzdwId;
    private String jzdwPids;


    /** 征求检察院 */
    private String jcy;

    /** 征求检察院名称 */
    private String jcymc;

    /** 提请法院 */
    private String fy;

    /** 提请法院名称 */
    private String fymc;

    /** 提请状态(下一步节点值) */
    private String zt;


    private String files1;
    private String files2;
    private String files3;
    private String files4;
    private String files5;

    /**决定书文号**/
    private String jdswh;
    /**是否决定撤销缓刑**/
    private String sfjdcxjs;
    /**（不）撤销缓刑决定日期**/
    private Date cxjsjdrq;
    /**（不）撤销缓刑原因**/
    private String cxjsyy;
    /**（不）撤销缓刑决定机关**/
    private String cxjsjdjg;

}
