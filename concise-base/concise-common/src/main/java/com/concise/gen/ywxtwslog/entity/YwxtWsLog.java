package com.concise.gen.ywxtwslog.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * ws接口获取日志
 *
 * <AUTHOR>
 * @date 2023-05-11 14:49:29
 */
@Data
@TableName("ywxt_ws_log")
public class YwxtWsLog{

    private String sjrzbh;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    private String paramXml;
    private String taskId;
    /**
     * 接收后业务表对应主键
     */
    private String dataId;

    /**
     * 操作时间
     */
    private Date opTime;

    /**
     * 类别
     */
    private String typeName;

    /**
     * 矫正对象
     */
    private String correctionObject;

    /**
     * 解码后的xml
     */
    private String decodeXml;
    /**
     * 0 失败
     * 1 成功
     *
     * 2 接收单位异常
     * 3 字段校验未通过
     * 4 字段内容异常
     * */
    private int success;
    private String jsdw;
    private String jsdwmc;
    private String tsdw;
    private String tsdwmc;
    private String sfxx;

}
