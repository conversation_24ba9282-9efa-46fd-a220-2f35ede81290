package com.concise.gen.revocationprobation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.base.enums.OrgTypeEnum;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import com.concise.gen.dataCenter.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.dataCenter.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.gen.dataCenter.orgDc.entity.OrgDc;
import com.concise.gen.dataCenter.orgDc.service.OrgDcService;
import com.concise.gen.extorginfo.entity.ExtOrgInfo;
import com.concise.gen.extorginfo.service.ExtOrgInfoService;
import com.concise.gen.revocationauditphase.constant.RevocationPhaseVar;
import com.concise.gen.revocationauditphase.entity.RevocationAuditPhase;
import com.concise.gen.revocationauditphase.entity.TaskInfo;
import com.concise.gen.revocationauditphase.param.RevocationAuditPhaseParam;
import com.concise.gen.revocationauditphase.service.RevocationAuditPhaseService;
import com.concise.gen.revocationprobation.entity.RevocationProbation;
import com.concise.gen.revocationprobation.mapper.RevocationProbationMapper;
import com.concise.gen.revocationprobation.param.RevParam;
import com.concise.gen.revocationprobation.param.RevocationProbationParam;
import com.concise.gen.revocationprobation.service.RevocationProbationService;
import com.concise.gen.webservice.service.SendRevocationProbationService;
import com.concise.gen.webservice.utils.DictWdToHyUtils;
import org.activiti.engine.*;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 提请撤销缓刑service接口实现类
 *
 * <AUTHOR>
 * @date 2022-05-31 13:47:23
 */
@Service
public class RevocationProbationServiceImpl extends ServiceImpl<RevocationProbationMapper, RevocationProbation> implements RevocationProbationService {

    @Resource
    private CorrectionObjectInformationService objService;
    @Resource
    private SendRevocationProbationService sendService;
    @Resource
    private RevocationAuditPhaseService auditService;
    @Resource
    private OrgDcService orgDcService;
    @Resource
    private ExtOrgInfoService extOrgInfoService;
    @Resource
    private SysFileInfoService sysFileInfoService;


    @Resource
    private RuntimeService runtimeService;

    @Resource
    private TaskService taskService;

    @Resource
    private IdentityService identityService;

    @Resource
    private HistoryService historyService;

    @Resource
    private ProcessEngineConfiguration processEngineConfiguration;

    @Resource
    private RepositoryService repositoryService;

    @Override
    public PageResult<RevocationProbation> page(RevocationProbationParam param) {
        QueryWrapper<RevocationProbation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().and(i -> i.eq(RevocationProbation::getJzdwId, param.getJzdwId()).or().like(RevocationProbation::getJzdwPids, param.getJzdwId()));
        if (ObjectUtil.isNotNull(param)) {

            // 根据 统一赋号  查询
            if (ObjectUtil.isNotEmpty(param.getTyfh())) {
                queryWrapper.lambda().eq(RevocationProbation::getTyfh, param.getTyfh());
            }
            // 根据 姓名  查询
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(RevocationProbation::getXm, param.getXm());
            }
            // 根据征求建议日期 查询
            if (ObjectUtil.isNotEmpty(param.getZqyjrq())) {
                queryWrapper.apply("date_format (zqyjrq,'%Y-%m-%d') = '" + param.getZqyjrq() + "'");
            }
            // 根据 提请日期  查询
            if (ObjectUtil.isNotEmpty(param.getTqrq())) {
                queryWrapper.apply("date_format (tqrq,'%Y-%m-%d') = '" + param.getTqrq() + "'");
            }
            // 根据征求检察院名称 查询
            if (ObjectUtil.isNotEmpty(param.getJcymc())) {
                queryWrapper.lambda().like(RevocationProbation::getJcymc, param.getJcymc());
            }
            // 根据提请法院名称 查询
            if (ObjectUtil.isNotEmpty(param.getFymc())) {
                queryWrapper.lambda().like(RevocationProbation::getFymc, param.getFymc());
            }
            // 根据提请状态 查询
            if (ObjectUtil.isNotEmpty(param.getZt())) {
                queryWrapper.lambda().eq(RevocationProbation::getZt, param.getZt());
            }
        }
        queryWrapper.lambda().orderByDesc(RevocationProbation::getTqrq);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<RevocationProbation> list(RevocationProbationParam revocationProbationParam) {
        return this.list();
    }

    @Override
    public void add(RevocationProbationParam param) {
        RevocationProbation model = new RevocationProbation();
        BeanUtil.copyProperties(param, model);
        model.setId(IdUtil.fastSimpleUUID());
        model.setTyfh(model.getId());

        model.setAddTime(DateUtil.date());
        model.setAddPsnId(param.getProcessPsnId());
        model.setAddPsnName(param.getProcessPsnName());

        CorrectionObjectInformation obj = objService.getById(param.getSqjzryId());
        model.setSqjzajbh(obj.getId());
        model.setXm(obj.getXm());
        model.setXb(obj.getXb());
        model.setCsrq(obj.getCsrq());
        model.setZjhm(obj.getSfzh());
        model.setGj(obj.getGj());
        model.setXzd(obj.getGdjzdszs()+","+obj.getGdjzdszds()+","+obj.getGdjzdszxq()+","+obj.getGdjzd());
        model.setXzdxz(obj.getGdjzdmx());
        model.setHjd(obj.getHjszs()+","+obj.getHjszds()+","+obj.getHjszxq()+","+obj.getHjszd());
        model.setHjdxz(obj.getHjszdmx());
        model.setJzlb(DictWdToHyUtils.jzlb(obj.getJzlb()));
        model.setSfs(obj.getJzjgName());
        model.setSqjzksrq(obj.getSqjzksrq());
        model.setSqjzjsrq(obj.getSqjzjsrq());
        model.setJzqx(obj.getSqjzqx());
        OrgDc org = orgDcService.getById(obj.getJzjg());
        model.setSqjzzxd(org.getCode().substring(0,6));
        model.setJzdw(org.getCode());
        model.setJzdwId(org.getId());
        model.setJzdwmc(org.getName());
        model.setJzdwPids(org.getPids());
        model.setZt("sh");
        model.setFiles2(model.getFiles1());
        model.setFiles3(model.getFiles1());
        model.setFiles4(model.getFiles1());

        ExtOrgInfo fy = extOrgInfoService.lambdaQuery().eq(ExtOrgInfo::getOrgCode, model.getFyP1()).eq(ExtOrgInfo::getType, OrgTypeEnum.FA_YUAN.getCode()).one();
        ExtOrgInfo jcy = extOrgInfoService.lambdaQuery().eq(ExtOrgInfo::getOrgCode, model.getJcyP1()).eq(ExtOrgInfo::getType, OrgTypeEnum.JIAN_CHA_YUAN.getCode()).one();
        model.setProcessType(fy.getOrgLevel());

        model.setFy(model.getFyP1());
        model.setFymc(fy.getOrgName());
        model.setJcy(model.getJcyP1());
        model.setJcymc(jcy.getOrgName());
        model.setZqyjtqly(model.getTqlyP1());
        model.setZqyjtqyj(model.getTqyjP1());
        model.setTqly(model.getTqlyP1());
        model.setTqyj(model.getTqyjP1());
        this.save(model);
    }

    @Override
    public void audit(RevocationAuditPhaseParam param) {
        String id = param.getId();
        param.setContactId(id);
        RevocationProbation byId = this.getById(id);
        String processKey = RevocationPhaseVar.PROCESS_DEFINITION_KEY.getString(byId.getProcessType());

        if (ObjectUtil.isEmpty(byId.getProcessInstanceId())) {
            Task ex = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(id).singleResult();
            if (ex == null) {
                HashMap<String, Object> vars = new HashMap<>(3);
                vars.put("processPsnId", byId.getAddPsnId());
                vars.put("processPsnName", byId.getAddPsnName());
                ProcessInstance rs = runtimeService.startProcessInstanceByKey(processKey, id, vars);
                identityService.setAuthenticatedUserId(param.getProcessPsnId());
                Task addTask = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(id).singleResult();
                taskService.addComment(addTask.getId(), addTask.getProcessInstanceId(), getComment(param.getProcessPsnId(),param.getProcessPsnName(), "", ""));
                taskService.complete(addTask.getId(), vars);
                byId.setProcessInstanceId(rs.getProcessInstanceId());
            }
        }

        HashMap<String, Object> vars = new HashMap<>(4);
        vars.put("processPsnId", param.getProcessPsnId());
        vars.put("processPsnName", param.getProcessPsnName());
        vars.put("result", param.getCsjg());
        identityService.setAuthenticatedUserId(param.getProcessPsnId());
        Task task = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(id).singleResult();
        taskService.addComment(task.getId(), task.getProcessInstanceId(), getComment(param.getProcessPsnId(),param.getProcessPsnName(), param.getCsyj(), param.getCsjg()));
        taskService.complete(task.getId(), vars);

        param.setAuditPhase(task.getTaskDefinitionKey());
        auditService.add(param);

        //获取下一个流程节点
        Task next = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(id).singleResult();

        if (next == null) {
            byId.setZt("zz");
        }else {
            byId.setZt(next.getTaskDefinitionKey());
        }
        this.updateById(byId);

    }
    private String getComment(String userId, String userName, String remark, String approve) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userId", userId);
        jsonObject.put("userName", userName);
        jsonObject.put("approve", approve);
        jsonObject.put("remark", remark);
        return jsonObject.toJSONString();
    }

    @Override
    public void submit(RevocationProbationParam param) {
        RevocationProbation model = this.getById(param.getId());
        String processKey = RevocationPhaseVar.PROCESS_DEFINITION_KEY.getString(model.getProcessType());
        BeanUtil.copyProperties(param, model);
        if ("zqjcjy".equals(model.getZt())) {
            if (!sendService.request4401(model,sysFileInfoService.getDocListByIds(model.getFiles1()))) {
                return;
            }
        }
        if ("tqcx".equals(model.getZt())) {
            if (!sendService.request4403_1(model,sysFileInfoService.getDocListByIds(model.getFiles2()))) {
                return;
            }
            sendService.request4403_2(model,sysFileInfoService.getDocListByIds(model.getFiles2()));
        }
        this.updateById(model);

        HashMap<String, Object> vars = new HashMap<>(4);
        vars.put("processPsnId", param.getProcessPsnId());
        vars.put("processPsnName", param.getProcessPsnName());
        identityService.setAuthenticatedUserId(param.getProcessPsnId());
        Task task = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(param.getId()).singleResult();
        taskService.addComment(task.getId(), task.getProcessInstanceId(), getComment(param.getProcessPsnId(),param.getProcessPsnName(),"",""));
        taskService.complete(task.getId(), vars);

        //获取下一个流程节点
        Task next = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(model.getId()).singleResult();
        model.setZt(next.getTaskDefinitionKey());
        this.updateById(model);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(RevocationProbationParam revocationProbationParam) {
        RevocationProbation byId = this.getById(revocationProbationParam.getId());
        if (ObjectUtil.isNotEmpty(byId.getProcessInstanceId())) {
            runtimeService.deleteProcessInstance(byId.getProcessInstanceId(), "delete");
        }
        this.removeById(revocationProbationParam.getId());
    }

    @Override
    public void edit(RevocationProbationParam param) {
        RevocationProbation model = this.getById(param.getId());
        if (ObjectUtil.isNotEmpty(model.getProcessInstanceId())&&!"xz".equals(model.getZt())) {
            //未开始的流程 可以编辑 退回到第一步到流程可以编辑
            return;
        }
        if (!model.getSqjzryId().equals(param.getSqjzryId())) {
            CorrectionObjectInformation obj = objService.getById(param.getSqjzryId());
            BeanUtil.copyProperties(param, model);
            model.setXm(obj.getXm());
            model.setXb(obj.getXb());
            model.setCsrq(obj.getCsrq());
            model.setZjhm(obj.getSfzh());
            model.setGj(obj.getGj());
            model.setXzdxz(obj.getGdjzdmx());
            model.setHjdxz(obj.getHjszdmx());
            model.setJzlb(DictWdToHyUtils.jzlb(obj.getJzlb()));
            model.setSfs(obj.getJzjgName());
            model.setSqjzksrq(obj.getSqjzksrq());
            model.setSqjzjsrq(obj.getSqjzjsrq());
            model.setJzqx(obj.getSqjzqx());
            OrgDc org = orgDcService.getById(obj.getJzjg());
            model.setJzdw(org.getCode());
            model.setJzdwId(org.getId());
            model.setJzdwmc(org.getName());
            model.setJzdwPids(org.getPids());
        }else {
            BeanUtil.copyProperties(param, model);
        }

        ExtOrgInfo fy = extOrgInfoService.lambdaQuery().eq(ExtOrgInfo::getOrgCode, model.getFyP1()).eq(ExtOrgInfo::getType, OrgTypeEnum.FA_YUAN.getCode()).one();
        ExtOrgInfo jcy = extOrgInfoService.lambdaQuery().eq(ExtOrgInfo::getOrgCode, model.getJcyP1()).eq(ExtOrgInfo::getType, OrgTypeEnum.JIAN_CHA_YUAN.getCode()).one();
        model.setProcessType(fy.getOrgLevel());

        model.setFy(model.getFyP1());
        model.setFymc(fy.getOrgName());
        model.setJcy(model.getJcyP1());
        model.setJcymc(jcy.getOrgName());
        model.setZqyjtqly(model.getTqlyP1());
        model.setZqyjtqyj(model.getTqyjP1());
        model.setTqly(model.getTqlyP1());
        model.setTqyj(model.getTqyjP1());
        model.setAddTime(DateUtil.date());
        model.setAddPsnId(param.getProcessPsnId());
        model.setAddPsnName(param.getProcessPsnName());

        if ("xz".equals(model.getZt())) {
            //修改后重新提交
            String processKey = RevocationPhaseVar.PROCESS_DEFINITION_KEY.getString(model.getProcessType());

            HashMap<String, Object> vars = new HashMap<>(4);
            vars.put("processPsnId", param.getProcessPsnId());
            vars.put("processPsnName", param.getProcessPsnName());
            identityService.setAuthenticatedUserId(param.getProcessPsnId());
            Task task = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(model.getProcessInstanceId()).singleResult();
            taskService.addComment(task.getId(), task.getProcessInstanceId(), getComment(param.getProcessPsnId(),param.getProcessPsnName(), "重新提交", ""));
            taskService.complete(task.getId(), vars);
            model.setZt("sh");
        }

        this.updateById(model);
    }

    @Override
    public RevocationProbation detail(RevocationProbationParam param) {
        RevocationProbation detail = this.getById(param.getId());
        detail.setProcessNodes(RevocationPhaseVar.PROCESS_DEFINITION.getString(detail.getProcessType()));
        JSONObject phase = new JSONObject();
        auditService.lambdaQuery().eq(RevocationAuditPhase::getContactId, param.getId())
                .eq(RevocationAuditPhase::getDelFlag, "0")
                .list().forEach(audit -> phase.put(audit.getAuditPhase(),audit));
        detail.setPhase(phase);
        String processInstanceId = detail.getProcessInstanceId();
        if (ObjectUtil.isNotEmpty(processInstanceId)) {
            List<HistoricActivityInstance> history = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).activityType("userTask").orderByHistoricActivityInstanceStartTime().asc().list();

            List<TaskInfo> infos = new ArrayList<>();
            history.forEach(h->{
                TaskInfo info = new TaskInfo();
                info.setProcessInstanceId(h.getProcessInstanceId());
                info.setStartTime(DateUtil.formatDateTime(h.getStartTime()));
                if (h.getEndTime() != null) {
                    info.setEndTime(DateUtil.formatDateTime(h.getEndTime()));
                }
                info.setTaskName(h.getActivityName());
                List<Comment> comments = taskService.getTaskComments(h.getTaskId());
                if (comments.size() > 0) {
                    JSONObject msg = JSON.parseObject(comments.get(0).getFullMessage());
                    info.setAssignee(msg.getString("userName"));
                    info.setApprove(msg.getString("approve"));
                    info.setComment(msg.getString("remark"));
                }
                infos.add(info);
            });
            detail.setTaskInfoList(infos);
        }
        return detail;
    }

    @Override
    public void revocationProbation(RevParam param) {
        CorrectionObjectInformation obj = objService.getById(param.getPid());
        if (obj == null) {
            throw new RuntimeException("社区矫正对象标识错误!");
        }
        String fymc = extOrgInfoService.getExtOrgName(OrgTypeEnum.FA_YUAN.getCode(), param.getFy());
        if (ObjectUtil.isEmpty(fymc)) {
            throw new RuntimeException("提请法院错误!");
        }
        String jcymc = extOrgInfoService.getExtOrgName(OrgTypeEnum.JIAN_CHA_YUAN.getCode(), param.getJcy());
        if (ObjectUtil.isEmpty(jcymc)) {
            throw new RuntimeException("征求意见检察院错误!");
        }
        if (ObjectUtil.isEmpty(param.getWs())) {
            throw new RuntimeException("文书附件不能为空，请检查ws参数!");
        }
        StringBuilder fileIds = new StringBuilder();
        List<AcceptCorrectionDoc> list = new ArrayList<>();
        for (Object s : param.getWs()) {
            JSONObject s1 = (JSONObject) s;
            HttpResponse result2 = HttpRequest.get(s1.getString("url")).body("").execute();
            SysFileInfo sysFileInfo = sysFileInfoService.uploadFileExtOss(s1.getString("name"), result2.bodyBytes());
            fileIds.append(sysFileInfo.getId()).append(",");

            AcceptCorrectionDoc doc = new AcceptCorrectionDoc();
            doc.setWs(sysFileInfo.getFileOriginName());
            doc.setWsdm(sysFileInfo.getFileOriginName());
            doc.setUri(sysFileInfo.getExtFilePath());
            list.add(doc);
        }

        OrgDc org = orgDcService.getById(obj.getJzjg());
        RevocationProbation model = new RevocationProbation();

        model.setJzdwId(obj.getJzjg());
        model.setJzdw(org.getCode());
        model.setJzdwmc(org.getName());
        model.setJzdwPids(org.getPids());

        model.setTyfh(param.getTyfh());
        model.setSqjzajbh(obj.getId());
        model.setZfbh(obj.getId());
        model.setSqjzryId(obj.getId());
        model.setXm(obj.getXm());
        model.setXb(obj.getXb());
        model.setZjhm(obj.getSfzh());
        model.setCsrq(obj.getCsrq());
        model.setJzlb(DictWdToHyUtils.jzlb(obj.getJzlb()));
        //社区矫正信息决定机关
        model.setJdjg("");
        model.setSfs(obj.getJzjg());
        model.setSqjzksrq(obj.getSqjzksrq());
        model.setSqjzjsrq(obj.getSqjzjsrq());

        model.setJyswh(param.getJyswh());
        model.setTqrq(DateUtil.parse(param.getTqrq()));
        model.setTqly(param.getTqly());
        model.setTqyj(param.getTqyj());
        model.setFy(param.getFy());
        model.setFymc(fymc);
        model.setJcymc(jcymc);
        model.setJcy(param.getJcy());

        if ("4401".equals(param.getXtbh())) {
            model.setFiles1(fileIds.toString());
            this.save(model);
            System.out.println(sendService.request4401(model,list));
        }
        if ("4403".equals(param.getXtbh())) {
            model.setFiles2(fileIds.toString());
            this.updateById(model);
            System.out.println(sendService.request4403_2(model,list));
        }

    }

    @Override
    public void updateByTyfh(RevocationProbationParam param, String step) {
        RevocationProbation model = this.getById(param.getId());
        if (model == null) {
            return;
        }
        String processKey = RevocationPhaseVar.PROCESS_DEFINITION_KEY.getString(model.getProcessType());
        HashMap<String, Object> vars = new HashMap<>(4);
        vars.put("processPsnId", "111");
        vars.put("processPsnName", "一体化");
        identityService.setAuthenticatedUserId("111");
        Task task = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(param.getId()).singleResult();
        if ("XTBH4402".equals(step)) {
            model.setJcyjwswh("检察意见文书文号");
            model.setCxhxjcyj("撤销缓刑检察建议");
            model.setJcyjbz("检查意见备注");
            model.setYjfksj(DateUtil.date());
            taskService.addComment(task.getId(), task.getProcessInstanceId(), getComment(model.getJcy(),model.getJcymc(),model.getCxhxjcyj(),""));
        }
        if ("XTBH4408_1".equals(step)) {
            model.setJdswh("决定书文号");
            model.setSfjdcxhx("1");
            model.setCxhxjdrq(DateUtil.date());
            model.setCxhxyy("撤销缓刑原因");
            model.setCxhxjdjg(model.getFy());
            taskService.addComment(task.getId(), task.getProcessInstanceId(), getComment(model.getFy(),model.getFymc(),model.getSfjdcxhx(),""));
        }
        taskService.complete(task.getId(), vars);


        //获取下一个流程节点
        Task next = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(model.getId()).singleResult();
        if (next == null) {
            model.setZt("ywc");
        }else {
            model.setZt(next.getTaskDefinitionKey());
        }
        this.updateById(model);
    }
}
