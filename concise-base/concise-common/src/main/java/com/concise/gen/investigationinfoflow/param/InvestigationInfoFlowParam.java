package com.concise.gen.investigationinfoflow.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
* 调查评估-流程记录参数类
 *
 * <AUTHOR>
 * @date 2025-03-25 09:02:58
*/
@Data
public class InvestigationInfoFlowParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 调查评估主键
     */
    @NotBlank(message = "调查评估主键不能为空，请检查investigationInfoId参数", groups = {add.class, edit.class})
    private String investigationInfoId;

    /**
     * 外层节点code, 1: 接收委托建档 2: 调查评估 3: 评估审批阶段 4: 反馈结果阶段
     */
    private Integer nodeLevel;

    /**
     * 类型：0：退回 1：通过 2：流程终止 9: 未开始
     */
    private Integer type;

    /**
     * 流程节点(调查评估状态的字典值:PGZT)
     */
    private String stepCode;

    /**
     * 流程节点名称
     */
    private String stepName;

    /**
     * 机构id
     */
    private String deptId;

    /**
     * 机构名称
     */
    private String deptName;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 意见/原因
     */
    private String msg;

    /**
     * 委托时间
     */
    private Date wtTime;

    /**
     * 公告/委托单位id
     */
    private String gwDeptId;

    /**
     * 公告/委托单位名称
     */
    private String gwDeptName;

    /**
     * 审批时间
     */
    private Date spTime;

    /**  公是否暂存, 0: 否 1：是 */
    private Integer draft;

    /**  表单信息 */
    private String formVal;

}
