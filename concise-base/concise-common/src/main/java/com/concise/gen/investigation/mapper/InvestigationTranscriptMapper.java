package com.concise.gen.investigation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.concise.gen.investigation.entity.InvestigationTranscript;
import com.concise.gen.investigation.vo.DeviceInvestigationTranscriptDTO;
import com.concise.gen.investigation.vo.DeviceInvestigationTranscriptVo;
import org.apache.ibatis.annotations.Param;

/**
 * 调查评估_笔录
 *
 * <AUTHOR>
 * @date 2025-03-24 14:59:13
 */
public interface InvestigationTranscriptMapper extends BaseMapper<InvestigationTranscript> {


    Page<DeviceInvestigationTranscriptDTO> selectPage(Page page, @Param("param") DeviceInvestigationTranscriptVo deviceInvestigationTranscriptVo);
}
