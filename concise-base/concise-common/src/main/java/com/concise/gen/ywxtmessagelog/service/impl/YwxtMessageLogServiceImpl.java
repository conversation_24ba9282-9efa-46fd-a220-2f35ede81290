package com.concise.gen.ywxtmessagelog.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ywxtmessagelog.entity.YwxtMessageLog;
import com.concise.gen.ywxtmessagelog.mapper.YwxtMessageLogMapper;
import com.concise.gen.ywxtmessagelog.param.YwxtMessageLogParam;
import com.concise.gen.ywxtmessagelog.service.YwxtMessageLogService;
import com.concise.gen.ywxtsendlog.entity.YwxtSendLog;
import com.concise.gen.ywxtsendlog.service.YwxtSendLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 一体化消息日志service接口实现类
 *
 * <AUTHOR>
 * @date 2024-10-11 17:07:24
 */
@Service
public class YwxtMessageLogServiceImpl extends ServiceImpl<YwxtMessageLogMapper, YwxtMessageLog> implements YwxtMessageLogService {

    @Resource
    private YwxtSendLogService ywxtSendLogService;
    @Override
    public PageResult<YwxtMessageLog> page(YwxtMessageLogParam param) {
        QueryWrapper<YwxtMessageLog> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {
            // 根据服务编号 查询
            if (ObjectUtil.isNotEmpty(param.getServerNum())) {
                queryWrapper.lambda().eq(YwxtMessageLog::getServerNum, param.getServerNum());
            }
            if (ObjectUtil.isNotEmpty(param.getXxlx())) {
                queryWrapper.lambda().eq(YwxtMessageLog::getXxlx, param.getXxlx());
            }
            // 根据发送单位 查询
            if (ObjectUtil.isNotEmpty(param.getSendOrgCode())) {
                queryWrapper.lambda().eq(YwxtMessageLog::getSendOrgCode, param.getSendOrgCode());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(param.getReceiveOrgCode())) {
                queryWrapper.lambda().and(i -> i.eq(YwxtMessageLog::getReceiveOrgId, param.getReceiveOrgCode()).or().like(YwxtMessageLog::getReceiveOrgPids, param.getReceiveOrgCode()));
            }
            if (ObjectUtil.isNotEmpty(param.getCorrectionObject())) {
                queryWrapper.lambda().like(YwxtMessageLog::getCorrectionObject, param.getCorrectionObject().trim());
            }
            // 根据关联记录id 查询
            if (ObjectUtil.isNotEmpty(param.getDataId())) {
                queryWrapper.lambda().eq(YwxtMessageLog::getDataId, param.getDataId());
            }
        }
        queryWrapper.lambda().eq(YwxtMessageLog::getDelFlag, 0).orderByDesc(YwxtMessageLog::getOpTime);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public JSONArray detailByDataId(String dataId) {
        JSONArray array = new JSONArray();
        List<YwxtSendLog> list = ywxtSendLogService.lambdaQuery().eq(YwxtSendLog::getDataId, dataId).list();
        for (YwxtSendLog sendLog : list) {
            List<YwxtMessageLog> msgList = this.lambdaQuery()
                    .eq(YwxtMessageLog::getTaskId, sendLog.getTaskId())
                    .eq(YwxtMessageLog::getDelFlag, 0)
                    .orderByAsc(YwxtMessageLog::getSendOrgCode)
                    .orderByAsc(YwxtMessageLog::getXxlx)
                    .list();
            JSONObject obj = new JSONObject();
            obj.put("type",sendLog.getTypeName());
            obj.put("msgList",msgList);
            array.add(obj);
        }
        return array;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(YwxtMessageLogParam ywxtMessageLogParam) {
        this.lambdaUpdate().set(YwxtMessageLog::getDelFlag, 1).eq(YwxtMessageLog::getId, ywxtMessageLogParam.getId()).update();
    }
}
