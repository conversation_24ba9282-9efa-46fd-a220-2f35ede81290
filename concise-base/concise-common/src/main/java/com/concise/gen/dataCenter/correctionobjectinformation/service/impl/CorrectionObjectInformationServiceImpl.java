package com.concise.gen.dataCenter.correctionobjectinformation.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.dataCenter.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.dataCenter.correctionobjectinformation.mapper.CorrectionObjectInformationMapper;
import com.concise.gen.dataCenter.correctionobjectinformation.param.CorrectionObjectInformationParam;
import com.concise.gen.dataCenter.correctionobjectinformation.service.CorrectionObjectInformationService;
import org.springframework.stereotype.Service;

/**
 * 矫正对象信息表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-06-07 14:59:06
 */
@Service
@DS("dataCenter")
public class CorrectionObjectInformationServiceImpl extends ServiceImpl<CorrectionObjectInformationMapper, CorrectionObjectInformation> implements CorrectionObjectInformationService {

    @Override
    public PageResult<CorrectionObjectInformation> page(CorrectionObjectInformationParam param) {
        QueryWrapper<CorrectionObjectInformation> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {
            if (ObjectUtil.isNotEmpty(param.getOrgSet()) && param.getOrgSet().size() < 1000) {
                queryWrapper.lambda().in(CorrectionObjectInformation::getJzjg, param.getOrgSet());
            }
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(CorrectionObjectInformation::getXm, param.getXm());
            }
            // 根据社区矫正人员编号 查询
            if (ObjectUtil.isNotEmpty(param.getSqjzrybh())) {
                queryWrapper.lambda().eq(CorrectionObjectInformation::getSqjzrybh, param.getSqjzrybh());
            }
            // 根据是否调查评估 查询
            if (ObjectUtil.isNotEmpty(param.getSfdcpg())) {
                queryWrapper.lambda().eq(CorrectionObjectInformation::getSfdcpg, param.getSfdcpg());
            }
            // 根据矫正类别 查询
            if (ObjectUtil.isNotEmpty(param.getJzlb())) {
                queryWrapper.lambda().eq(CorrectionObjectInformation::getJzlb, param.getJzlb());
            }
            if (ObjectUtil.isNotEmpty(param.getZhuangtai())) {
                queryWrapper.lambda().eq(CorrectionObjectInformation::getZhuangtai, param.getZhuangtai());
            }
        }
        queryWrapper.lambda().orderByDesc(CorrectionObjectInformation::getSqjzksrq);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

}
