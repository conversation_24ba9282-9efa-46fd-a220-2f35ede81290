package com.concise.gen.notice.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.PoiUtil;
import com.concise.gen.notice.entity.YwxtNoticeUser;
import com.concise.gen.notice.mapper.YwxtNoticeUserMapper;
import com.concise.gen.notice.param.YwxtNoticeUserParam;
import com.concise.gen.notice.service.YwxtNoticeUserService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 协同信息通知_用户表service接口实现类
 *
 * <AUTHOR>
 * @date 2024-03-28 13:41:14
 */
@Service
public class YwxtNoticeUserServiceImpl extends ServiceImpl<YwxtNoticeUserMapper, YwxtNoticeUser> implements YwxtNoticeUserService {

    @Override
    public PageResult<YwxtNoticeUser> page(YwxtNoticeUserParam param) {
        QueryWrapper<YwxtNoticeUser> queryWrapper = buildQueryWrapper(param);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    private QueryWrapper<YwxtNoticeUser> buildQueryWrapper(YwxtNoticeUserParam param) {
        QueryWrapper<YwxtNoticeUser> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据ywxt_notice.id 查询
            if (ObjectUtil.isNotEmpty(param.getNoticeId())) {
                queryWrapper.lambda().eq(YwxtNoticeUser::getNoticeId, param.getNoticeId());
            }
            // 姓名 查询
            if (ObjectUtil.isNotEmpty(param.getUserName())) {
                queryWrapper.lambda().like(YwxtNoticeUser::getUserName, param.getUserName());
            }
            //所属单位
            if (ObjectUtil.isNotEmpty(param.getOrgIds())) {
                queryWrapper.lambda().like(YwxtNoticeUser::getOrgIds, param.getOrgIds());
            }
            // 通知事项 查询
            if (ObjectUtil.isNotEmpty(param.getNoticeType())) {
                queryWrapper.lambda().like(YwxtNoticeUser::getNoticeType, param.getNoticeType());
            }

            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper.apply("date_format (send_time,'%Y-%m-%d') >= date_format('" + param.getSearchBeginTime() + "','%Y-%m-%d')")
                        .apply("date_format (send_time,'%Y-%m-%d') <= date_format('" + param.getSearchEndTime() + "','%Y-%m-%d')");
            }
        }
        queryWrapper.lambda().orderByDesc(YwxtNoticeUser::getSendTime);
        return queryWrapper;
    }

    @Override
    public void export(YwxtNoticeUserParam param) {
        QueryWrapper<YwxtNoticeUser> queryWrapper = buildQueryWrapper(param);
        List<YwxtNoticeUser> list = this.list(queryWrapper);
        PoiUtil.exportExcelWithStream("YwxtNoticeUser.xls", YwxtNoticeUser.class, list);
    }

}
