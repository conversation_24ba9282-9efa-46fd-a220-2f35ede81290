package com.concise.gen.paperquestionbank.param;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.paperquestionitem.entity.PaperQuestionItem;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
* 题库参数类
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:58
*/
@Data
public class PaperQuestionBankParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, detail.class})
    private String id;

    /**
     * 问题名称
     */
    @NotBlank(message = "问题名称不能为空，请检查topicName参数", groups = {add.class, edit.class})
    private String topicName;

    /**
     * 指标
     */
    @NotBlank(message = "指标不能为空，请检查indexName参数", groups = {add.class, edit.class})
    private String indexName;

    /**
     * 总分
     */
    private Integer topicScore;

    /**
     * 备注
     */
    private String remark;

    /**
     * 指标选项
     */
    private String items;

    /**
     * 删除状态， 0：未删除  1：已删除
     */
    private Integer delFlag;

    /**
     * ids
     */
    private List<String> idList;
    /**
     * 指标选项集合
     */
    private List<PaperQuestionItem> itemList;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateUser;
}
