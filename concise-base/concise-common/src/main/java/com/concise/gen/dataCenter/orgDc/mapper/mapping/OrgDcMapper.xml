<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.dataCenter.orgDc.mapper.OrgDcMapper">

    <select id="getZwddIds" resultType="com.concise.gen.dataCenter.orgDc.vo.CcgfUser">
        SELECT a.id,a.realname,b.dep_id
        FROM ccgf0.sys_user AS a
                 INNER JOIN ccgf0.sys_user_depart AS b ON a.id = b.user_id AND b.dep_id = #{deptId}
        WHERE a.del_flag = 0
          AND a.id like 'GE_%'
          AND a.extend01 = 'businessReminding'
    </select>
</mapper>
