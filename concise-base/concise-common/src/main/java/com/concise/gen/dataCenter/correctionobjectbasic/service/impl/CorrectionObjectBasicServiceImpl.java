package com.concise.gen.dataCenter.correctionobjectbasic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.dataCenter.correctionobjectbasic.entity.CorrectionObjectBasic;
import com.concise.gen.dataCenter.correctionobjectbasic.enums.CorrectionObjectBasicExceptionEnum;
import com.concise.gen.dataCenter.correctionobjectbasic.mapper.CorrectionObjectBasicMapper;
import com.concise.gen.dataCenter.correctionobjectbasic.param.CorrectionObjectBasicParam;
import com.concise.gen.dataCenter.correctionobjectbasic.service.CorrectionObjectBasicService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * 社区矫正对象信息-基本信息service接口实现类
 *
 * <AUTHOR>
 * @date 2021-08-27 11:35:18
 */
@Service
@DS("dataCenter")
public class CorrectionObjectBasicServiceImpl extends ServiceImpl<CorrectionObjectBasicMapper, CorrectionObjectBasic> implements CorrectionObjectBasicService {

    @Override
    public PageResult<CorrectionObjectBasic> page(CorrectionObjectBasicParam correctionObjectBasicParam, Set<String> orgs, boolean b) {
        QueryWrapper<CorrectionObjectBasic> queryWrapper = new QueryWrapper<>();
        // 非管理员
        int i = 0;
        if(b) {
            queryWrapper.in("jzjg", orgs);
            i=1;
        }
        if (ObjectUtil.isNotNull(correctionObjectBasicParam)) {

            // 根据社区矫正人员编号 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getSqjzrybh())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getSqjzrybh, correctionObjectBasicParam.getSqjzrybh());
            }
            // 根据矫正机构 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getJzjg())) {
                if(i==0) {
                    queryWrapper.in("jzjg", orgs);
                }
            }
            // 根据矫正机构中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getJzjgName())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getJzjgName, correctionObjectBasicParam.getJzjgName());
            }
            // 根据是否调查评估 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getSfdcpg())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getSfdcpg, correctionObjectBasicParam.getSfdcpg());
            }
            // 根据矫正类别 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getJzlb())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getJzlb, correctionObjectBasicParam.getJzlb());
            }
            // 根据矫正类别中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getJzlbName())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getJzlbName, correctionObjectBasicParam.getJzlbName());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getXm())) {
//                queryWrapper.lambda().eq(CorrectionObjectBasic::getXm, correctionObjectBasicParam.getXm());
                queryWrapper.like("xm",correctionObjectBasicParam.getXm());
            }
            // 根据曾用名 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getCym())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getCym, correctionObjectBasicParam.getCym());
            }
            // 根据性别 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getXb())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getXb, correctionObjectBasicParam.getXb());
            }
            // 根据性别中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getXbName())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getXbName, correctionObjectBasicParam.getXbName());
            }
            // 根据民族 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getMz())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getMz, correctionObjectBasicParam.getMz());
            }
            // 根据民族中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getMzName())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getMzName, correctionObjectBasicParam.getMzName());
            }
            // 根据身份证号 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getSfzh())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getSfzh, correctionObjectBasicParam.getSfzh());
            }
            // 根据出生日期 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getCsrq())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getCsrq, correctionObjectBasicParam.getCsrq());
            }
            // 根据个人联系电话 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getGrlxdh())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getGrlxdh, correctionObjectBasicParam.getGrlxdh());
            }
            // 根据定位号码 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getDwhm())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getDwhm, correctionObjectBasicParam.getDwhm());
            }
            // 根据社区矫正开始日期 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getSqjzksrq())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getSqjzksrq, correctionObjectBasicParam.getSqjzksrq());
            }
            // 根据社区矫正结束日期 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getSqjzjsrq())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getSqjzjsrq, correctionObjectBasicParam.getSqjzjsrq());
            }
            // 根据社区矫正期限 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getSqjzqx())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getSqjzqx, correctionObjectBasicParam.getSqjzqx());
            }
            // 根据是否采用电子定位管理(1实施定位、2免除定位、3解除定位） 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getSfcydzdwgl())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getSfcydzdwgl, correctionObjectBasicParam.getSfcydzdwgl());
            }
            // 根据电子定位方式 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getDzdwfs())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getDzdwfs, correctionObjectBasicParam.getDzdwfs());
            }
            // 根据电子定位方式中文值 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getDzdwfsName())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getDzdwfsName, correctionObjectBasicParam.getDzdwfsName());
            }
            // 根据矫正状态(1:期满解矫,2:收监执行,200在册,3:死亡,4:托管,5:其他(余罪),6:重新犯罪,7:矫正中止后矫正解除,8:特赦解矫) 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getStatus())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getStatus, correctionObjectBasicParam.getStatus());
            }
            // 根据解矫类型 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getRelieveType())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getRelieveType, correctionObjectBasicParam.getRelieveType());
            }
            // 根据运营商类型（1：移动，2：联通，3：电信） 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getOperatorType())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getOperatorType, correctionObjectBasicParam.getOperatorType());
            }
            // 根据是否开通基站电子定位管理(0:否,1:是,2:退订,3:开通失败) 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getIsElectronicPositioning())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getIsElectronicPositioning, correctionObjectBasicParam.getIsElectronicPositioning());
            }
            // 根据是否开通基站电子定位管理(0:否,1:是,2:退订,3:开通失败) 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getElectronicPositioning())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getElectronicPositioning, correctionObjectBasicParam.getElectronicPositioning());
            }
            // 根据手环设备编号 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getEquipmentNumber())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getEquipmentNumber, correctionObjectBasicParam.getEquipmentNumber());
            }
            // 根据是否采用腕带定位管理(0:否,1:是) 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getIsEwPositioning())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getIsEwPositioning, correctionObjectBasicParam.getIsEwPositioning());
            }
            // 根据是否重点人员(0:否,1:是) 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getIsKeyPersonnel())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getIsKeyPersonnel, correctionObjectBasicParam.getIsKeyPersonnel());
            }
            // 根据处理等级(字典值：1:严管,2:普管) 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getJzjb())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getJzjb, correctionObjectBasicParam.getJzjb());
            }
            // 根据处理等级(字典值：1:严管,2:普管) 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getJzjbName())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getJzjbName, correctionObjectBasicParam.getJzjbName());
            }
            // 根据请假标识（是_1,否_0） 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getIsLeave())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getIsLeave, correctionObjectBasicParam.getIsLeave());
            }
            // 根据版本 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getVersion())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getVersion, correctionObjectBasicParam.getVersion());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getCreateBy())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getCreateBy, correctionObjectBasicParam.getCreateBy());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getUpdateBy())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getUpdateBy, correctionObjectBasicParam.getUpdateBy());
            }
            // 根据是否删除（0：未删除，1删除） 查询
            if (ObjectUtil.isNotEmpty(correctionObjectBasicParam.getDelFlag())) {
                queryWrapper.lambda().eq(CorrectionObjectBasic::getDelFlag, correctionObjectBasicParam.getDelFlag());
            }
        }
        queryWrapper.orderByDesc("create_time");
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionObjectBasic> list(CorrectionObjectBasicParam correctionObjectBasicParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionObjectBasicParam correctionObjectBasicParam) {
        CorrectionObjectBasic correctionObjectBasic = new CorrectionObjectBasic();
        BeanUtil.copyProperties(correctionObjectBasicParam, correctionObjectBasic);
        this.save(correctionObjectBasic);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionObjectBasicParam correctionObjectBasicParam) {
        this.removeById(correctionObjectBasicParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionObjectBasicParam correctionObjectBasicParam) {
        CorrectionObjectBasic correctionObjectBasic = this.queryCorrectionObjectBasic(correctionObjectBasicParam);
        BeanUtil.copyProperties(correctionObjectBasicParam, correctionObjectBasic);
        this.updateById(correctionObjectBasic);
    }

    @Override
    public CorrectionObjectBasic detail(CorrectionObjectBasicParam correctionObjectBasicParam) {
        return this.queryCorrectionObjectBasic(correctionObjectBasicParam);
    }

    /**
     * 获取社区矫正对象信息-基本信息
     *
     * <AUTHOR>
     * @date 2021-08-27 11:35:18
     */
    private CorrectionObjectBasic queryCorrectionObjectBasic(CorrectionObjectBasicParam correctionObjectBasicParam) {
        CorrectionObjectBasic correctionObjectBasic = this.getById(correctionObjectBasicParam.getId());
        if (ObjectUtil.isNull(correctionObjectBasic)) {
            throw new ServiceException(CorrectionObjectBasicExceptionEnum.NOT_EXIST);
        }
        return correctionObjectBasic;
    }
}
