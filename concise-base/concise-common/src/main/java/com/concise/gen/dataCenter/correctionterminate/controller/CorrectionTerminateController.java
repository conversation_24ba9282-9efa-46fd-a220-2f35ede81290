package com.concise.gen.dataCenter.correctionterminate. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.dataCenter.correctionterminate. param.CorrectionTerminateParam;
import com.concise.gen.dataCenter.correctionterminate. service.CorrectionTerminateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 矫正对象解矫列表控制器
 *
 * <AUTHOR>
 * @date 2022-06-17 15:09:52
 */
@Api(tags = "矫正对象解矫列表")
@RestController
public class CorrectionTerminateController {

    @Resource
    private CorrectionTerminateService correctionTerminateService;

    /**
     * 查询矫正对象解矫列表
     *
     * <AUTHOR>
     * @date 2022-06-17 15:09:52
     */
    @Permission
    @GetMapping("/correctionTerminate/page")
    @ApiOperation("矫正对象解矫列表_分页查询")
    @BusinessLog(title = "矫正对象解矫列表_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(CorrectionTerminateParam correctionTerminateParam) {
        return new SuccessResponseData(correctionTerminateService.page(correctionTerminateParam));
    }

    /**
     * 添加矫正对象解矫列表
     *
     * <AUTHOR>
     * @date 2022-06-17 15:09:52
     */
    @Permission
    @PostMapping("/correctionTerminate/add")
    @ApiOperation("矫正对象解矫列表_增加")
    @BusinessLog(title = "矫正对象解矫列表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(CorrectionTerminateParam.add.class) CorrectionTerminateParam correctionTerminateParam) {
        correctionTerminateService.add(correctionTerminateParam);
        return new SuccessResponseData();
    }

    /**
     * 删除矫正对象解矫列表
     *
     * <AUTHOR>
     * @date 2022-06-17 15:09:52
     */
    @Permission
    @PostMapping("/correctionTerminate/delete")
    @ApiOperation("矫正对象解矫列表_删除")
    @BusinessLog(title = "矫正对象解矫列表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(CorrectionTerminateParam.delete.class) CorrectionTerminateParam correctionTerminateParam) {
        correctionTerminateService.delete(correctionTerminateParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑矫正对象解矫列表
     *
     * <AUTHOR>
     * @date 2022-06-17 15:09:52
     */
    @Permission
    @PostMapping("/correctionTerminate/edit")
    @ApiOperation("矫正对象解矫列表_编辑")
    @BusinessLog(title = "矫正对象解矫列表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(CorrectionTerminateParam.edit.class) CorrectionTerminateParam correctionTerminateParam) {
        correctionTerminateService.edit(correctionTerminateParam);
        return new SuccessResponseData();
    }

    /**
     * 查看矫正对象解矫列表
     *
     * <AUTHOR>
     * @date 2022-06-17 15:09:52
     */
    @Permission
    @GetMapping("/correctionTerminate/detail")
    @ApiOperation("矫正对象解矫列表_查看")
    @BusinessLog(title = "矫正对象解矫列表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(CorrectionTerminateParam.detail.class) CorrectionTerminateParam correctionTerminateParam) {
        return new SuccessResponseData(correctionTerminateService.detail(correctionTerminateParam));
    }

    /**
     * 矫正对象解矫列表列表
     *
     * <AUTHOR>
     * @date 2022-06-17 15:09:52
     */
    @Permission
    @GetMapping("/correctionTerminate/list")
    @ApiOperation("矫正对象解矫列表_列表")
    @BusinessLog(title = "矫正对象解矫列表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(CorrectionTerminateParam correctionTerminateParam) {
        return new SuccessResponseData(correctionTerminateService.list(correctionTerminateParam));
    }

    @Permission
    @PostMapping("/correctionTerminate/xt")
    public ResponseData addToXt(CorrectionTerminateParam correctionTerminateParam) {
        correctionTerminateService.addToXt(correctionTerminateParam);
        return new SuccessResponseData();
    }

}
