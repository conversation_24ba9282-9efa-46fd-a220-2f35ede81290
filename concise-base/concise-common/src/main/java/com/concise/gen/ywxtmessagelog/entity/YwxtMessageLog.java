package com.concise.gen.ywxtmessagelog.entity;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 一体化消息接收日志
 *
 * <AUTHOR>
 * @date 2024-10-11 17:07:24
 */
@Data
@TableName("ywxt_message_log")
public class YwxtMessageLog{

    /**  主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**  操作时间 */
    private Date opTime;

    /**  服务编号 */
    private String serverNum;

    /**  发送单位 */
    private String sendOrgCode;
    private String sendOrgName;
    private String sendOrgType;
    /**  接收单位 */
    private String receiveOrgCode;
    private String receiveOrgName;
    private String receiveOrgId;
    private String receiveOrgPids;

    /**  消息类型 */
    private String xxlx;

    /**   */
    private String taskId;

    /**  解码后的xml */
    private String decodeXml;
    private String xxnr;

    /**  关联记录id */
    private String dataId;
    private String correctionObject;
    private String deptName;
    private Boolean success;
    private Integer delFlag;

    public YwxtMessageLog(){
    }

    public YwxtMessageLog(String serverNum,
                          String xxlx,
                          String sendOrgCode,
                          String sendOrgName,
                          String sendOrgType,
                          String receiveOrgCode,
                          String receiveOrgName,
                          String receiveOrgId,
                          String receiveOrgPids,
                          String taskId,
                          String decodeXml,
                          String xxnr,
                          Boolean success,
                          String dataId,
                          String correctionObject,
                          String deptName) {
        this.opTime = DateUtil.date();
        this.serverNum = serverNum;
        this.sendOrgCode = sendOrgCode;
        this.sendOrgName = sendOrgName;
        this.sendOrgType = sendOrgType;
        this.receiveOrgCode = receiveOrgCode;
        this.receiveOrgName = receiveOrgName;
        this.receiveOrgId = receiveOrgId;
        this.receiveOrgPids = receiveOrgPids;
        this.xxlx = xxlx;
        this.taskId = taskId;
        this.decodeXml = decodeXml;
        this.xxnr = xxnr;
        this.success = success;
        this.dataId = dataId;
        this.correctionObject = correctionObject;
        this.deptName = deptName;
    }

}
