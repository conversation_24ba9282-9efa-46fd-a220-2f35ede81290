package com.concise.gen.dataCenter.correctplacechange.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 居住地变更_数据中心
 *
 * <AUTHOR>
 * @date 2023-09-13 15:07:59
 */
@Data
@TableName("correction_placechange")
public class PlaceChangeDc{

    /**
     * 唯一标识
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 社区矫正人员标识
     */
    private String pid;

    /**
     * 社区矫正人员姓名
     */
    private String pname;

    /**
     * 申请时间
     */
    private Date sqsj;

    /**
     * 迁入地所在省（区、市）
     */
    private String qrdszs;

    /**
     * 迁入地所在省（区、市）中文值
     */
    private String qrdszsName;

    /**
     * 迁入地所在地（市、州）
     */
    private String qrdszd;

    /**
     * 迁入地所在地（市、州）中文值
     */
    private String qrdszdName;

    /**
     * 迁入地所在县（市、区）
     */
    private String qrdszx;

    /**
     * 迁入地所在县（市、区）中文值
     */
    private String qrdszxName;

    /**
     * 迁入地（乡镇、街道）
     */
    private String qrdxz;

    /**
     * 迁入地（乡镇、街道）中文值
     */
    private String qrdxzName;

    /**
     * 迁入地明细
     */
    private String qrdmx;

    /**
     * 拟接收矫正单位
     */
    private String njsjzdwid;

    /**
     * 拟接收矫正单位中文值
     */
    private String njsjzdwName;

    /**
     * 变更理由
     */
    private String jzdbgsy;

    /**
     * 流程状态
     */
    private String flowstatusname;
    /**
     * 最终处理结果
     * */
    private String zzcljgName;

    /**
     * 最后修改时间
     */
    private Date lastModifiedTime;

    /**
     * 迁居前的机构id
     */
    private String jiedaoId;

    /**
     * 迁居前的机构名称
     */
    private String jiedaoName;

}
