package com.concise.gen.revocationprobation.param;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class RevParam {
    private String id;
    /**统一赋号**/
    @NotBlank(message = "统一赋号不能为空，请检查tyfh参数", groups = {api.class})
    private String tyfh;
    /**协同节点编号**/
    @NotBlank(message = "协同节点编号不能为空，请检查xtbh参数", groups = {api.class})
    private String xtbh;
    /**社区矫正对象标识**/
    @NotBlank(message = "社区矫正对象标识不能为空，请检查pid参数", groups = {api.class})
    private String pid;
    /**姓名**/
    private String xm;
    /**撤销假释建议书文号**/
    @NotBlank(message = "撤销假释建议书文号不能为空，请检查jyswh参数", groups = {api.class})
    private String jyswh;
    /**提请日期**/
    @NotBlank(message = "提请日期不能为空，请检查tqrq参数", groups = {api.class})
    private String tqrq;
    /**提请理由**/
    @NotBlank(message = "提请理由不能为空，请检查tqly参数", groups = {api.class})
    private String tqly;
    /**提请依据**/
    @NotBlank(message = "提请依据不能为空，请检查tqyj参数", groups = {api.class})
    private String tqyj;
    /**征求意见检察院**/
    @NotBlank(message = "征求意见检察院不能为空，请检查jcy参数", groups = {api.class})
    private String jcy;
    /**提请法院**/
    @NotBlank(message = "提请法院不能为空，请检查fy参数", groups = {api.class})
    private String fy;
    /**文书附件**/
    private JSONArray ws;

    public @interface api { }
}
