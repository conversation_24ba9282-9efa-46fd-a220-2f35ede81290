package com.concise.gen.revocationauditphase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.revocationauditphase.entity.RevocationAuditPhase;
import com.concise.gen.revocationauditphase.enums.RevocationAuditPhaseExceptionEnum;
import com.concise.gen.revocationauditphase.mapper.RevocationAuditPhaseMapper;
import com.concise.gen.revocationauditphase.param.RevocationAuditPhaseParam;
import com.concise.gen.revocationauditphase.service.RevocationAuditPhaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 提请撤销审核阶段表service接口实现类
 *
 * <AUTHOR>
 * @date 2024-02-29 14:41:31
 */
@Service
public class RevocationAuditPhaseServiceImpl extends ServiceImpl<RevocationAuditPhaseMapper, RevocationAuditPhase> implements RevocationAuditPhaseService {

    @Override
    public PageResult<RevocationAuditPhase> page(RevocationAuditPhaseParam revocationAuditPhaseParam) {
        QueryWrapper<RevocationAuditPhase> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(revocationAuditPhaseParam)) {

            // 根据阶段 查询
            if (ObjectUtil.isNotEmpty(revocationAuditPhaseParam.getAuditPhase())) {
                queryWrapper.lambda().eq(RevocationAuditPhase::getAuditPhase, revocationAuditPhaseParam.getAuditPhase());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(revocationAuditPhaseParam.getContactId())) {
                queryWrapper.lambda().eq(RevocationAuditPhase::getContactId, revocationAuditPhaseParam.getContactId());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<RevocationAuditPhase> list(RevocationAuditPhaseParam revocationAuditPhaseParam) {
        return this.list();
    }

    @Override
    public void add(RevocationAuditPhaseParam param) {
        this.lambdaUpdate().set(RevocationAuditPhase::getDelFlag, "1")
                .eq(RevocationAuditPhase::getContactId, param.getContactId())
                .eq(RevocationAuditPhase::getAuditPhase, param.getAuditPhase()).update();

        param.setId(IdUtil.fastSimpleUUID());
        RevocationAuditPhase revocationAuditPhase = new RevocationAuditPhase();
        BeanUtil.copyProperties(param, revocationAuditPhase);
        this.save(revocationAuditPhase);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(RevocationAuditPhaseParam revocationAuditPhaseParam) {
        this.removeById(revocationAuditPhaseParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(RevocationAuditPhaseParam revocationAuditPhaseParam) {
        RevocationAuditPhase revocationAuditPhase = this.queryRevocationAuditPhase(revocationAuditPhaseParam);
        BeanUtil.copyProperties(revocationAuditPhaseParam, revocationAuditPhase);
        this.updateById(revocationAuditPhase);
    }

    @Override
    public RevocationAuditPhase detail(RevocationAuditPhaseParam revocationAuditPhaseParam) {
        return this.queryRevocationAuditPhase(revocationAuditPhaseParam);
    }

    /**
     * 获取提请撤销审核阶段表
     *
     * <AUTHOR>
     * @date 2024-02-29 14:41:31
     */
    private RevocationAuditPhase queryRevocationAuditPhase(RevocationAuditPhaseParam revocationAuditPhaseParam) {
        RevocationAuditPhase revocationAuditPhase = this.getById(revocationAuditPhaseParam.getId());
        if (ObjectUtil.isNull(revocationAuditPhase)) {
            throw new ServiceException(RevocationAuditPhaseExceptionEnum.NOT_EXIST);
        }
        return revocationAuditPhase;
    }
}
