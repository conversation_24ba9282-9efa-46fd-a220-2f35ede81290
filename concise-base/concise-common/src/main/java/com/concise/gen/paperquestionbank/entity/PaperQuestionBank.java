package com.concise.gen.paperquestionbank.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.concise.gen.paperquestionitem.entity.PaperQuestionItem;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 题库
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:58
 */
@Data
@TableName("paper_question_bank")
public class PaperQuestionBank {

    /**  主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**  问题名称 */
    @Excel(name = "问题", width = 40)
    private String topicName;

    /**  指标 */
    @Excel(name = "指标", width = 50)
    private String indexName;

    /**  指标选项 */
    @Excel(name = "指标选项", width = 80)
    private String items;

    /**  总分 */
    @Excel(name = "总分", width = 20)
    private Integer topicScore;

    /**  备注 */
    @Excel(name = "备注", width = 40)
    private String remark;

    /**  删除状态， 0：未删除  1：已删除 */
    private Integer delFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;

    /**
     * 指标选项集合
     */
    @TableField(exist = false)
    private List<PaperQuestionItem> itemList;
}
