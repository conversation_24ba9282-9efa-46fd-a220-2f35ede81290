package com.concise.gen.extorginfo. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.extorginfo. param.ExtOrgInfoParam;
import com.concise.gen.extorginfo. service.ExtOrgInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 外部单位信息控制器
 *
 * <AUTHOR>
 * @date 2022-06-01 10:56:17
 */
@Api(tags = "外部单位信息")
@RestController
public class ExtOrgInfoController {

    @Resource
    private ExtOrgInfoService extOrgInfoService;

    /**
     * 查询外部单位信息
     *
     * <AUTHOR>
     * @date 2022-06-01 10:56:17
     */
    @Permission
    @GetMapping("/extOrgInfo/page")
    @ApiOperation("外部单位信息_分页查询")
    public ResponseData page(ExtOrgInfoParam extOrgInfoParam) {
        return new SuccessResponseData(extOrgInfoService.page(extOrgInfoParam));
    }

    /**
     * 添加外部单位信息
     *
     * <AUTHOR>
     * @date 2022-06-01 10:56:17
     */
    @Permission
    @PostMapping("/extOrgInfo/add")
    @ApiOperation("外部单位信息_增加")
    @BusinessLog(title = "外部单位信息_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(ExtOrgInfoParam.add.class) ExtOrgInfoParam extOrgInfoParam) {
        extOrgInfoService.add(extOrgInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 删除外部单位信息
     *
     * <AUTHOR>
     * @date 2022-06-01 10:56:17
     */
    @Permission
    @PostMapping("/extOrgInfo/delete")
    @ApiOperation("外部单位信息_删除")
    @BusinessLog(title = "外部单位信息_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(ExtOrgInfoParam.delete.class) ExtOrgInfoParam extOrgInfoParam) {
        extOrgInfoService.delete(extOrgInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑外部单位信息
     *
     * <AUTHOR>
     * @date 2022-06-01 10:56:17
     */
    @Permission
    @PostMapping("/extOrgInfo/edit")
    @ApiOperation("外部单位信息_编辑")
    @BusinessLog(title = "外部单位信息_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(ExtOrgInfoParam.edit.class) ExtOrgInfoParam extOrgInfoParam) {
        extOrgInfoService.edit(extOrgInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 查看外部单位信息
     *
     * <AUTHOR>
     * @date 2022-06-01 10:56:17
     */
    @Permission
    @GetMapping("/extOrgInfo/detail")
    @ApiOperation("外部单位信息_查看")
    public ResponseData detail(@Validated(ExtOrgInfoParam.detail.class) ExtOrgInfoParam extOrgInfoParam) {
        return new SuccessResponseData(extOrgInfoService.detail(extOrgInfoParam));
    }

    /**
     * 外部单位信息列表
     *
     * <AUTHOR>
     * @date 2022-06-01 10:56:17
     */
    @Permission
    @GetMapping("/extOrgInfo/list")
    @ApiOperation("外部单位信息_列表")
    public ResponseData list(ExtOrgInfoParam extOrgInfoParam) {
        return new SuccessResponseData(extOrgInfoService.list(extOrgInfoParam));
    }

}
