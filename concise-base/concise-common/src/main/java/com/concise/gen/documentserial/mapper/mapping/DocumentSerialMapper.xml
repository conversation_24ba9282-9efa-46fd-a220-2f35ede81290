<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.documentserial.mapper.DocumentSerialMapper">

    <select id="getMaxSerialNumber" resultType="java.lang.Integer">
        select max(serial_number) from document_serial where type = #{type} and current_year = #{year} and dept_id = #{deptId}
    </select>
</mapper>
