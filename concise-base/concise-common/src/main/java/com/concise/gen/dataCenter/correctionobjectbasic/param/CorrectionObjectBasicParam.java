package com.concise.gen.dataCenter.correctionobjectbasic.param;

import com.concise.common.pojo.base.param.BaseParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
* 社区矫正对象信息-基本信息参数类
 *
 * <AUTHOR>
 * @date 2021-08-27 11:35:18
*/
@Data
public class CorrectionObjectBasicParam extends BaseParam {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    @ApiModelProperty(value = "ID")
    private String id;

    /**
     * 社区矫正人员编号
     */
    @NotBlank(message = "社区矫正人员编号不能为空，请检查sqjzrybh参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "社区矫正人员编号")
    private String sqjzrybh;

    /**
     * 矫正机构
     */
    @NotBlank(message = "矫正机构不能为空，请检查jzjg参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "矫正机构")
    private String jzjg;

    /**
     * 矫正机构中文值
     */
    @NotBlank(message = "矫正机构中文值不能为空，请检查jzjgName参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "矫正机构中文值")
    private String jzjgName;

    /**
     * 是否调查评估
     */
    @NotBlank(message = "是否调查评估不能为空，请检查sfdcpg参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "是否调查评估")
    private String sfdcpg;

    /**
     * 矫正类别
     */
    @NotBlank(message = "矫正类别不能为空，请检查jzlb参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "矫正类别")
    private String jzlb;

    /**
     * 矫正类别中文值
     */
    @NotBlank(message = "矫正类别中文值不能为空，请检查jzlbName参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "矫正类别中文值")
    private String jzlbName;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "姓名")
    private String xm;

    /**
     * 曾用名
     */
    @NotBlank(message = "曾用名不能为空，请检查cym参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "曾用名")
    private String cym;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空，请检查xb参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "性别")
    private String xb;

    /**
     * 性别中文值
     */
    @NotBlank(message = "性别中文值不能为空，请检查xbName参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "性别中文值")
    private String xbName;

    /**
     * 民族
     */
    @NotBlank(message = "民族不能为空，请检查mz参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "民族")
    private String mz;

    /**
     * 民族中文值
     */
    @NotBlank(message = "民族中文值不能为空，请检查mzName参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "民族中文值")
    private String mzName;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空，请检查sfzh参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "身份证号")
    private String sfzh;

    /**
     * 出生日期
     */
    @NotNull(message = "出生日期不能为空，请检查csrq参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "出生日期")
    private String csrq;

    /**
     * 个人联系电话
     */
    @NotBlank(message = "个人联系电话不能为空，请检查grlxdh参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "个人联系电话")
    private String grlxdh;

    /**
     * 定位号码
     */
    @NotBlank(message = "定位号码不能为空，请检查dwhm参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "定位号码")
    private String dwhm;

    /**
     * 社区矫正开始日期
     */
    @NotNull(message = "社区矫正开始日期不能为空，请检查sqjzksrq参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "社区矫正开始日期")
    private String sqjzksrq;

    /**
     * 社区矫正结束日期
     */
    @NotNull(message = "社区矫正结束日期不能为空，请检查sqjzjsrq参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "社区矫正结束日期")
    private String sqjzjsrq;

    /**
     * 社区矫正期限
     */
    @NotBlank(message = "社区矫正期限不能为空，请检查sqjzqx参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "社区矫正期限")
    private String sqjzqx;

    /**
     * 是否采用电子定位管理(1实施定位、2免除定位、3解除定位）
     */
    @NotBlank(message = "是否采用电子定位管理(1实施定位、2免除定位、3解除定位）不能为空，请检查sfcydzdwgl参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "是否采用电子定位管理(1实施定位、2免除定位、3解除定位）")
    private String sfcydzdwgl;

    /**
     * 电子定位方式
     */
    @NotBlank(message = "电子定位方式不能为空，请检查dzdwfs参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "电子定位方式")
    private String dzdwfs;

    /**
     * 电子定位方式中文值
     */
    @NotBlank(message = "电子定位方式中文值不能为空，请检查dzdwfsName参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "电子定位方式中文值")
    private String dzdwfsName;

    /**
     * 矫正状态(1:期满解矫,2:收监执行,200在册,3:死亡,4:托管,5:其他(余罪),6:重新犯罪,7:矫正中止后矫正解除,8:特赦解矫)
     */
    @NotNull(message = "矫正状态(1:期满解矫,2:收监执行,200在册,3:死亡,4:托管,5:其他(余罪),6:重新犯罪,7:矫正中止后矫正解除,8:特赦解矫)不能为空，请检查status参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "矫正状态(1:期满解矫,2:收监执行,200在册,3:死亡,4:托管,5:其他(余罪),6:重新犯罪,7:矫正中止后矫正解除,8:特赦解矫)")
    private Integer status;

    /**
     * 解矫类型
     */
    @NotBlank(message = "解矫类型不能为空，请检查relieveType参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "解矫类型")
    private String relieveType;

    /**
     * 运营商类型（1：移动，2：联通，3：电信）
     */
    @NotNull(message = "运营商类型（1：移动，2：联通，3：电信）不能为空，请检查operatorType参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "运营商类型（1：移动，2：联通，3：电信）")
    private Integer operatorType;

    /**
     * 是否开通基站电子定位管理(0:否,1:是,2:退订,3:开通失败)
     */
    @NotNull(message = "是否开通基站电子定位管理(0:否,1:是,2:退订,3:开通失败)不能为空，请检查isElectronicPositioning参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "是否开通基站电子定位管理(0:否,1:是,2:退订,3:开通失败)")
    private Integer isElectronicPositioning;

    /**
     * 是否开通基站电子定位管理(0:否,1:是,2:退订,3:开通失败)
     */
    @NotBlank(message = "是否开通基站电子定位管理(0:否,1:是,2:退订,3:开通失败)不能为空，请检查electronicPositioning参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "是否开通基站电子定位管理(0:否,1:是,2:退订,3:开通失败)")
    private String electronicPositioning;

    /**
     * 手环设备编号
     */
    @NotBlank(message = "手环设备编号不能为空，请检查equipmentNumber参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "手环设备编号")
    private String equipmentNumber;

    /**
     * 是否采用腕带定位管理(0:否,1:是)
     */
    @NotNull(message = "是否采用腕带定位管理(0:否,1:是)不能为空，请检查isEwPositioning参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "是否采用腕带定位管理(0:否,1:是)")
    private Integer isEwPositioning;

    /**
     * 是否重点人员(0:否,1:是)
     */
    @NotNull(message = "是否重点人员(0:否,1:是)不能为空，请检查isKeyPersonnel参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "是否重点人员(0:否,1:是)")
    private Integer isKeyPersonnel;

    /**
     * 处理等级(字典值：1:严管,2:普管)
     */
    @NotBlank(message = "处理等级(字典值：1:严管,2:普管)不能为空，请检查jzjb参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "处理等级(字典值：1:严管,2:普管)")
    private String jzjb;

    /**
     * 处理等级(字典值：1:严管,2:普管)
     */
    @NotBlank(message = "处理等级(字典值：1:严管,2:普管)不能为空，请检查jzjbName参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "处理等级(字典值：1:严管,2:普管)")
    private String jzjbName;

    /**
     * 请假标识（是_1,否_0）
     */
    @NotNull(message = "请假标识（是_1,否_0）不能为空，请检查isLeave参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "请假标识（是_1,否_0）")
    private Integer isLeave;

    /**
     * 版本
     */
    @NotNull(message = "版本不能为空，请检查version参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "版本")
    private Long version;

    /**
     *
     */
    @NotBlank(message = "不能为空，请检查createBy参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "")
    private String createBy;

    /**
     *
     */
    @NotBlank(message = "不能为空，请检查updateBy参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "")
    private String updateBy;

    /**
     * 是否删除（0：未删除，1删除）
     */
    @NotNull(message = "是否删除（0：未删除，1删除）不能为空，请检查delFlag参数", groups = {add.class, edit.class})
    @ApiModelProperty(value = "是否删除（0：未删除，1删除）")
    private Integer delFlag;

    public CorrectionObjectBasicParam(@NotBlank(message = "矫正类别不能为空，请检查jzlb参数", groups = {add.class, edit.class}) String jzlb, @NotBlank(message = "矫正类别中文值不能为空，请检查jzlbName参数", groups = {add.class, edit.class}) String jzlbName) {
        this.jzlb = jzlb;
        this.jzlbName = jzlbName;
    }
}
