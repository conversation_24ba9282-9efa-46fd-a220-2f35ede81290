package com.concise.gen.dataCenter.correctionobjectinformation.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.dataCenter.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.dataCenter.correctionobjectinformation.param.CorrectionObjectInformationParam;

/**
 * 矫正对象信息表service接口
 *
 * <AUTHOR>
 * @date 2022-06-07 14:59:06
 */
@DS("dataCenter")
public interface CorrectionObjectInformationService extends IService<CorrectionObjectInformation> {

    /**
     * 查询矫正对象信息表
     *
     * <AUTHOR>
     * @date 2022-06-07 14:59:06
     */
    PageResult<CorrectionObjectInformation> page(CorrectionObjectInformationParam correctionObjectInformationParam);


}
