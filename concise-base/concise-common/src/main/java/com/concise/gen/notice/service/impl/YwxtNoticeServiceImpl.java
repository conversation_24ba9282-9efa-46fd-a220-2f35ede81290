package com.concise.gen.notice.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.log.Log;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.consts.SymbolConstant;
import com.concise.gen.dataCenter.orgDc.entity.OrgDc;
import com.concise.gen.dataCenter.orgDc.service.OrgDcService;
import com.concise.gen.dataCenter.orgDc.vo.CcgfUser;
import com.concise.gen.notice.entity.YwxtNotice;
import com.concise.gen.notice.entity.YwxtNoticeUser;
import com.concise.gen.notice.enums.YwxtNoticeTypeEnum;
import com.concise.gen.notice.mapper.YwxtNoticeMapper;
import com.concise.gen.notice.service.YwxtNoticeService;
import com.concise.gen.notice.service.YwxtNoticeUserService;
import com.concise.gen.utils.ZwddUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 协同信息通知表service接口实现类
 *
 * <AUTHOR>
 * @date 2023-12-08 11:38:39
 */
@Service
public class YwxtNoticeServiceImpl extends ServiceImpl<YwxtNoticeMapper, YwxtNotice> implements YwxtNoticeService {

    @Value("${zwdd.url}")
    private String url;
    @Value("${zwdd.appKey}")
    private String appKey;
    @Value("${zwdd.appSecret}")
    private String appSecret;
    @Value("${zwdd.send}")
    private boolean send;

    @Resource
    private OrgDcService orgDcService;
    @Resource
    private YwxtNoticeUserService ywxtNoticeUserService;

    private static final Log log = Log.get();

    @Override
    public List<YwxtNotice> getAllTypeLatestNoticeList(String id) {
        return this.baseMapper.getAllTypeLatestNoticeList(id);
    }

    @Override
    public List<YwxtNotice> getLastNoticeList(String userId, int size) {
        DateTime offset = DateUtil.offset(DateUtil.date(), DateField.HOUR,-24);
        List<YwxtNotice> lastNoticeList = this.baseMapper.getLastNoticeList(userId, size);
        for (YwxtNotice notice : lastNoticeList) {
            YwxtNoticeTypeEnum type = YwxtNoticeTypeEnum.getEnumByCode(notice.getNoticeType());
            Object[] split = notice.getNoticeParam().split(SymbolConstant.COMMA);
            notice.setMsg(String.format(type.getTemplate(), split));
            notice.setIsNew(DateUtil.compare(notice.getNoticeTime(),offset)>0);
        }
        return lastNoticeList;
    }

    @Override
    public List<YwxtNotice> getLastNoticeListByOrgId(String orgId, int size) {
        List<YwxtNotice> lastNoticeList = this.baseMapper.getLastNoticeListByOrgId(orgId, size);
        DateTime offset = DateUtil.offset(DateUtil.date(), DateField.HOUR,-24);
        for (YwxtNotice notice : lastNoticeList) {
            try {
                YwxtNoticeTypeEnum type = YwxtNoticeTypeEnum.getEnumByCode(notice.getNoticeType());
                Object[] split = notice.getNoticeParam().split(SymbolConstant.COMMA);
                notice.setMsg(String.format(type.getTemplate(), split));
                notice.setIsNew(DateUtil.compare(notice.getNoticeTime(),offset)>0);
            }catch (Exception e){
                log.error("getLastNoticeListByOrgId err:{}", notice.getId());
            }
        }
        return lastNoticeList;
    }

    @Override
    public String buildNotice(YwxtNoticeTypeEnum type, String param, String zwddParam) {
        YwxtNotice ywxtNotice = new YwxtNotice();
        ywxtNotice.setId(IdUtil.fastSimpleUUID());
        ywxtNotice.setPrefix(type.getPrefix());
        ywxtNotice.setNoticeType(type.getType());
        ywxtNotice.setNoticeParam(param);
        ywxtNotice.setNoticeZwddParam(zwddParam);
        ywxtNotice.setSendZwddMsg(type.getSendZwddMsg());
        ywxtNotice.setNoticeTime(DateUtil.date());
        this.save(ywxtNotice);
        return ywxtNotice.getId();
    }

    @Override
    public void buildNoticeByOrgIdList(YwxtNoticeTypeEnum type, String param, String zwddParam, List<String> orgIdList) {
        String noticeId = buildNotice(type, param, zwddParam);
        if (ObjectUtil.isNotEmpty(noticeId)) {
            this.baseMapper.insertNoticeOrgIdList(noticeId,type.getType(), orgIdList);
            if (type.getSendZwddMsg() && send) {
                Object[] paramList = zwddParam.split(SymbolConstant.COMMA);
                String msg = "";
                try {
                    msg = String.format(type.getTemplateZwdd(), paramList);
                }catch (Exception e){
                    log.error("buildNotice参数错误 err:{}", noticeId);
                    return;
                }
                List<String> userIdList = new ArrayList<>();
                for (String orgId : orgIdList) {
                    //发送人员为本级及所属部门的联系人
                    String finalMsg = msg;
                    orgDcService.lambdaQuery()
                            .eq(OrgDc::getId, orgId)
                            .or(i ->i.eq(OrgDc::getPid, orgId).eq(OrgDc::getType, "sp"))
                            .list()
                            .forEach(org -> {
                                List<CcgfUser> userList = orgDcService.getZwddUserList(org.getId());
                                for (CcgfUser user : userList) {
                                    userIdList.add(user.getId());
                                    YwxtNoticeUser noticeUser = new YwxtNoticeUser();
                                    noticeUser.setNoticeId(noticeId);
                                    noticeUser.setUserId(user.getId());
                                    noticeUser.setUserName(user.getRealname());
                                    noticeUser.setOrgName(ObjectUtil.isNotEmpty(org.getFullName())?org.getFullName():org.getName());
                                    noticeUser.setOrgIds(orgDcService.getIds(org));
                                    noticeUser.setNoticeType(type.getType());
                                    noticeUser.setNoticeTypeName(type.getPrefix());
                                    noticeUser.setMsg(finalMsg);
                                    ywxtNoticeUserService.save(noticeUser);
                                }
                            });
                }
                if (userIdList.size()==0) {
                    log.error("buildNotice提醒用户列表为空 type:{},param:{},org:{}", type.getType(),param,orgIdList.get(0));
                    return;
                }
                boolean sent = ZwddUtils.sendWorkNotificationNormal(String.join(",", userIdList), msg, url, appKey, appSecret);
                if (sent) {
                    ywxtNoticeUserService.lambdaUpdate()
                            .set(YwxtNoticeUser::getSend, true)
                            .set(YwxtNoticeUser::getSendTime, DateUtil.date())
                            .eq(YwxtNoticeUser::getNoticeId, noticeId)
                            .update();
                }
            }
        }else {
            log.error("buildNoticeByOrgId构建失败 type:{},param:{},org:{}", type.getType(),param,orgIdList.get(0));
        }
    }

    @Override
    public void buildNoticeByOrgId(YwxtNoticeTypeEnum type, String param, String zwddParam, String... orgIds) {
        buildNoticeByOrgIdList(type,param,zwddParam,Arrays.asList(orgIds));
    }
}
