package com.concise.gen.notice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 协同信息通知表
 *
 * <AUTHOR>
 * @date 2023-12-08 11:38:39
 */
@Data
@TableName("ywxt_notice")
public class YwxtNotice{

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    @TableField(exist = false)
    private String noticeUserId;
    @TableField(exist = false)
    private String msg;
    @TableField(exist = false)
    private Boolean isNew;

    /**
     * 协同通知前缀
     */
    private String prefix;

    /**
     * 通知类型,关联消息模版id
     */
    private String noticeType;

    /**
     * 填充参数 [,]分隔
     */
    private String noticeParam;
    private String noticeZwddParam;

    /**
     * 发布时间
     */
    private Date noticeTime;

    /**
     * 是否发送浙政钉通知
     */
    private Boolean sendZwddMsg;

}
