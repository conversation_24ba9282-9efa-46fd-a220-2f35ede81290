package com.concise.gen.placechange.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
* 跨省执行地变更参数类
 *
 * <AUTHOR>
 * @date 2025-04-01 11:09:06
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class PlacechangeTransProvParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**  变更类型
     * 0 迁出
     * 1 迁入
     * */
    private String transType;

    /**
     * 统一赋号
     */
    private String uniformCode;

    /**
     * 流程状态
     */
    private String processStatus;

    /**
     * 迁出结果
     */
    private String receiveStatus;

    /**
     * 矫正单位
     */
    private String deptCode;

    /**
     * 矫正单位
     */
    private String deptId;

    /**
     * 矫正单位
     */
    private String deptPids;

    /**
     * 矫正单位
     */
    private String deptName;

    /**
     * 矫正单位
     */
    private String extDeptCode;

    /**
     * 矫正单位
     */
    private String extDeptName;


    /**
     * 社区矫正对象姓名
     */
    private String correctionObjName;

    /**
     * 社区矫正对象
     */
    private String correctionObjId;

    /**
     * 社区矫正对象编号
     */
    private String correctionObjCode;

    /**
     * 证件类型
     * 字典 zjlx
     */
    private String certType;

    /**
     * 证件号码
     */
    private String certNum;

    /**
     * 民族
     * 字典	mz
     */
    private String nation;

    /**
     * 国籍
     * 字典 	guoji
     */
    private String nationality;

    /**
     * 文化程度
     * 字典 	whcd
     */
    private String educationalBackground;

    /**
     * 婚姻状况
     * 字典 hyzk
     */
    private String maritalStatus;

    /**
     * 政治面貌
     * 字典 	zzmm
     */
    private String politicalStatus;

    /**
     * 捕前职业
     * 字典 	SQJZ_BQZY
     */
    private String job;

    /**
     * 就业就学情况
     * 字典 jyjxqk
     */
    private String employment;

    /**
     * 社区矫正类别
     * 字典	SQJZ_JZLB_NEW
     */
    private String correctionType;

    /**
     * 住所地
     */
    private String residenceCode;

    /**
     * 住所地详细地址
     */
    private String residence;

    /**
     * 户籍所在地
     */
    private String registeredAddressCode;

    /**
     * 户籍所在地明细
     */
    private String registeredAddress;

    /**
     * 矫正开始日期
     */
    private String correctionStart;

    /**
     * 矫正结束日期
     */
    private String correctionEnd;

    /**
     * 矫正期限
     */
    private String correctionDuration;

    /**
     * 决定机关
     */
    private String decisionDept;

    /**
     * 迁出单位联系人
     */
    private String deptContactPsn;

    /**
     * 迁出单位联系电话
     */
    private String deptContactTel;

    /**
     * 申请日期
     */
    private String applicationDate;

    /**
     * 迁入地
     */
    private String destinationCode;

    /**
     * 迁入地详细地址
     */
    private String destination;

    /**
     * 变更理由
     */
    private String changeReason;

    /**
     * 接收结果
     */
    private String opinionResult;

    /**
     * 情况说明
     */
    private String opinionRemark;

    /**
     * 意见反馈日期
     */
    private String opinionDate;

    /**
     * 入矫报到情况
     */
    private String registrationResult;

    /**
     * 入矫日期
     */
    private String correctionStartAt;

    /**
     * 入矫报到备注
     */
    private String registrationRemark;

    /**
     * 移送日期
     */
    private String transDate;


    /**
     * 附件上传
     * */
    private String files;
    private String sync;
    private String receiveDept;
    private String sendDept;
    private String taskId1;
    private String taskId2;
    private String taskId3;
    private String taskId4;
    /**承办信息*/
    private String processPsn;
    private String processDept;
    private String processDeptTel;

    /**外部承办信息*/
    private String extProcessPsn;
    private String extProcessDept;
    private String extProcessDeptTel;
}
