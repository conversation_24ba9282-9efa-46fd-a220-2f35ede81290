package com.concise.gen.notice.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.gen.notice.entity.YwxtNotice;
import com.concise.gen.notice.enums.YwxtNoticeTypeEnum;

import java.util.List;

/**
 * 协同信息通知表service接口
 *
 * <AUTHOR>
 * @date 2023-12-08 11:38:39
 */
public interface YwxtNoticeService extends IService<YwxtNotice> {

    /**
     * 获取所有类型最新一条通知
     * @param id 用户id
     * @return YwxtNotice
     */
    List<YwxtNotice> getAllTypeLatestNoticeList(String id);
    /**
     * 获取最新通知
     * @param userId 用户id
     * @param size 数量
     * @return YwxtNotice
     */
    List<YwxtNotice> getLastNoticeList(String userId,int size);

    /**
     * 获取最新通知
     * @param orgId 机构id集合
     * @param size 数量
     * @return YwxtNotice
     */
    List<YwxtNotice> getLastNoticeListByOrgId(String orgId, int size);

    /**
     * 构建通知
     * @param type YwxtNoticeTypeEnum
     * @param param 字符串 [,]分隔
     * @param zwddParam zwddParam 字符串 [,]分隔
     * @return YwxtNotice.id
     */
    String buildNotice(YwxtNoticeTypeEnum type, String param, String zwddParam);

    /**
     * 构建通知(本级机构所有通知人)
     * @param type YwxtNoticeTypeEnum
     * @param param 字符串 [,]分隔
     * @param zwddParam zwddParam 字符串 [,]分隔
     * @param orgIdList 机构id
     */
    void buildNoticeByOrgIdList(YwxtNoticeTypeEnum type, String param, String zwddParam, List<String> orgIdList);

    /**
     * 构建通知(本级机构所有通知人)
     * @param type YwxtNoticeTypeEnum
     * @param param 字符串 [,]分隔
     * @param zwddParam zwddParam 字符串 [,]分隔
     * @param orgIds 机构id
     */
    void buildNoticeByOrgId(YwxtNoticeTypeEnum type, String param, String zwddParam, String... orgIds);

}
