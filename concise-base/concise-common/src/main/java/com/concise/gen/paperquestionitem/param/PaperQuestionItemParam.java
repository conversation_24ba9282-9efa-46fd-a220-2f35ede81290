package com.concise.gen.paperquestionitem.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 题库-指标选项参数类
 *
 * <AUTHOR>
 * @date 2025-03-20 15:03:00
*/
@Data
public class PaperQuestionItemParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 题库主键
     */
    @NotBlank(message = "题库主键不能为空，请检查paperQuestionBankId参数", groups = {add.class, edit.class})
    private String paperQuestionBankId;

    /**
     * 指标选项内容
     */
    @NotBlank(message = "指标选项内容不能为空，请检查content参数", groups = {add.class, edit.class})
    private String content;

    /**
     * 分数
     */
    @NotNull(message = "分数不能为空，请检查itemScore参数", groups = {add.class, edit.class})
    private Integer itemScore;

    /**
     * 排序号
     */
    @NotNull(message = "排序号不能为空，请检查serialNumber参数", groups = {add.class, edit.class})
    private Integer serialNumber;

}
