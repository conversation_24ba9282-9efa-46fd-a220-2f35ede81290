package com.concise.gen.dataCenter.orgDc. controller;

import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.dataCenter.orgDc.param.OrgDcParam;
import com.concise.gen.dataCenter.orgDc.service.OrgDcService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 数据中心机构表控制器
 *
 * <AUTHOR>
 * @date 2023-11-23 16:15:33
 */
@Api(tags = "数据中心机构表")
@RestController
public class OrgDcController {

    @Resource
    private OrgDcService orgDcService;

    /**
     * 查询数据中心机构表
     *
     * <AUTHOR>
     * @date 2023-11-23 16:15:33
     */
    @GetMapping("/orgDc/page")
    public ResponseData page(OrgDcParam orgDcParam) {
        return new SuccessResponseData(orgDcService.page(orgDcParam));
    }

}
