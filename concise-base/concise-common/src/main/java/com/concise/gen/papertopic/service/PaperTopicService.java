package com.concise.gen.papertopic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.papertopic.entity.PaperTopic;
import com.concise.gen.papertopic.param.PaperTopicParam;
import java.util.List;

/**
 * 量卷维护-题目service接口
 *
 * <AUTHOR>
 * @date 2025-03-20 15:03:03
 */
public interface PaperTopicService extends IService<PaperTopic> {

    /**
     * 查询量卷维护-题目
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:03
     */
    PageResult<PaperTopic> page(PaperTopicParam paperTopicParam);

    /**
     * 量卷维护-题目列表
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:03
     */
    List<PaperTopic> list(PaperTopicParam paperTopicParam);

    /**
     * 添加量卷维护-题目
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:03
     */
    void add(PaperTopicParam paperTopicParam);

    /**
     * 删除量卷维护-题目
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:03
     */
    void delete(PaperTopicParam paperTopicParam);

    /**
     * 编辑量卷维护-题目
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:03
     */
    void edit(PaperTopicParam paperTopicParam);

    /**
     * 查看量卷维护-题目
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:03
     */
     PaperTopic detail(PaperTopicParam paperTopicParam);

     /**
      * 批量遍历题目里面topicScore为空的题目，选项如果有分数，则取最高分，更新topicScore
      */
     void batchUpdateTopicScore();
}
