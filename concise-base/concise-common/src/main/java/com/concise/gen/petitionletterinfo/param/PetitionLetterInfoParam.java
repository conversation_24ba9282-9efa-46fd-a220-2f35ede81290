package com.concise.gen.petitionletterinfo.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
* 信访信息参数类
 *
 * <AUTHOR>
 * @date 2024-04-02 14:11:20
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class PetitionLetterInfoParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 信访件编号
     */
    private String letterNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 矫正单位
     */
    private String deptName;
    private String deptId;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件类型
     */
    private String idTypeName;

    /**
     * 证件号码
     */
    private String idNumber;

    /**
     * 信访时间
     */
    private String letterTime;

    /**
     * 信访类型
     */
    private String letterType;

    /**
     * 信访类型
     */
    private String letterTypeName;

    /**
     * 信访渠道
     */
    private String letterChannel;

    /**
     * 信访渠道
     */
    private String letterChannelName;

    /**
     * 是否异地信访
     */
    private String isOffsite;
    private String zhuangtai;

}
