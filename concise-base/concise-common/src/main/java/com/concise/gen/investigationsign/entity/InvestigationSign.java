package com.concise.gen.investigationsign.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.pojo.base.entity.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 调查评估_手签
 *
 * <AUTHOR>
 * @date 2025-04-21 20:30:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("investigation_sign")
public class InvestigationSign extends BaseEntity {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 调查评估id
     */
    private String pid;

    /**
     * 用户id
     */
    private String receiveUserId;

    /**
     * 用户类型（1-浙政钉用户；2-浙里办用户）
     */
    private Integer receiveUserType;

    /**
     * 用户姓名
     */
    private String receiveUserName;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 文书id
     */
    private String paperId;

    /**
     * 文书标题
     */
    private String title;

    /**
     * 文书类型
     */
    private String paperType;

    /**
     * 类型： 1：表示手动上传的
     */
    private String type;

    /**
     * 文书类型_中文值
     */
    private String paperTypeName;

    /**
     * 发送单位id
     */
    private String sendOrgId;

    /**
     * 发送单位
     */
    private String sendOrgName;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 状态（0-待签；1-已签；2-退回）
     */
    private String status;

    /**
     * 状态字典值
     */
    @TableField(exist = false)
    private String statusName;

    public String getStatusName() {
        if (status != null) {
            switch (status) {
                case "0":
                    statusName = "待签名";
                    break;
                case "1":
                    statusName = "已签名";
                    break;
                case "2":
                    statusName = "退回";
                    break;
                default:
                    statusName = null;
                    break;
            }
        }
        return statusName;
    }

    /**
     * 签名时间
     */
    private Date signTime;

    /**
     * 签名顺序
     */
    private Integer sort;

    /**
     * 签名图片base64
     */
    private String signFileBase64;

    /**
     * 捺印图片base64
     */
    private String chopFileBase64;

    /**
     * 通知信息
     */
    private String noticeInfo;

    //笔录id
    private String blId;
}
