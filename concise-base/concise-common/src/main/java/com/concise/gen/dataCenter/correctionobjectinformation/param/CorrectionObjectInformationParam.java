package com.concise.gen.dataCenter.correctionobjectinformation.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
* 矫正对象信息表参数类
 *
 * <AUTHOR>
 * @date 2022-07-25 16:08:07
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class CorrectionObjectInformationParam extends BaseParam {


    private Set<String> orgSet;
    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 社区矫正人员编号
     */
    @NotBlank(message = "社区矫正人员编号不能为空，请检查sqjzrybh参数", groups = {add.class, edit.class})
    private String sqjzrybh;

    /**
     * 是否调查评估
     */
    @NotBlank(message = "是否调查评估不能为空，请检查sfdcpg参数", groups = {add.class, edit.class})
    private String sfdcpg;

    /**
     * 矫正类别
     */
    @NotBlank(message = "矫正类别不能为空，请检查jzlb参数", groups = {add.class, edit.class})
    private String jzlb;

    /**
     * 矫正类别中文值
     */
    @NotBlank(message = "矫正类别中文值不能为空，请检查jzlbName参数", groups = {add.class, edit.class})
    private String jzlbName;

    /**
     * 是否成年
     */
    @NotBlank(message = "是否成年不能为空，请检查sfcn参数", groups = {add.class, edit.class})
    private String sfcn;

    /**
     * 是否成年中文值
     */
    @NotBlank(message = "是否成年中文值不能为空，请检查sfcnName参数", groups = {add.class, edit.class})
    private String sfcnName;

    /**
     * 未成年
     */
    @NotBlank(message = "未成年不能为空，请检查wcn参数", groups = {add.class, edit.class})
    private String wcn;

    /**
     * 未成年中文值
     */
    @NotBlank(message = "未成年中文值不能为空，请检查wcnName参数", groups = {add.class, edit.class})
    private String wcnName;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 曾用名
     */
    @NotBlank(message = "曾用名不能为空，请检查cym参数", groups = {add.class, edit.class})
    private String cym;

    /**
     *  性别
     */
    @NotBlank(message = " 性别不能为空，请检查xb参数", groups = {add.class, edit.class})
    private String xb;

    /**
     * 性别中文值
     */
    @NotBlank(message = "性别中文值不能为空，请检查xbName参数", groups = {add.class, edit.class})
    private String xbName;

    /**
     *  民族
     */
    @NotBlank(message = " 民族不能为空，请检查mz参数", groups = {add.class, edit.class})
    private String mz;

    /**
     * 民族中文值
     */
    @NotBlank(message = "民族中文值不能为空，请检查mzName参数", groups = {add.class, edit.class})
    private String mzName;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空，请检查sfzh参数", groups = {add.class, edit.class})
    private String sfzh;

    /**
     * 出生日期
     */
    @NotNull(message = "出生日期不能为空，请检查csrq参数", groups = {add.class, edit.class})
    private String csrq;

    /**
     * 有无港澳台身份证
     */
    @NotBlank(message = "有无港澳台身份证不能为空，请检查ywgatsfz参数", groups = {add.class, edit.class})
    private String ywgatsfz;

    /**
     * 港澳台身份证类型
     */
    @NotBlank(message = "港澳台身份证类型不能为空，请检查gatsfzlx参数", groups = {add.class, edit.class})
    private String gatsfzlx;

    /**
     * 港澳台身份证类型中文值
     */
    @NotBlank(message = "港澳台身份证类型中文值不能为空，请检查gatsfzlxName参数", groups = {add.class, edit.class})
    private String gatsfzlxName;

    /**
     * 港澳台身份证号码
     */
    @NotBlank(message = "港澳台身份证号码不能为空，请检查gatsfzhm参数", groups = {add.class, edit.class})
    private String gatsfzhm;

    /**
     * 有无护照
     */
    @NotBlank(message = "有无护照不能为空，请检查ywhz参数", groups = {add.class, edit.class})
    private String ywhz;

    /**
     * 护照号码
     */
    @NotBlank(message = "护照号码不能为空，请检查hzhm参数", groups = {add.class, edit.class})
    private String hzhm;

    /**
     * 护照保存状态
     */
    @NotBlank(message = "护照保存状态不能为空，请检查hzbczt参数", groups = {add.class, edit.class})
    private String hzbczt;

    /**
     * 护照保存状态中文值
     */
    @NotBlank(message = "护照保存状态中文值不能为空，请检查hzbcztName参数", groups = {add.class, edit.class})
    private String hzbcztName;

    /**
     * 有无港澳台通行证
     */
    @NotBlank(message = "有无港澳台通行证不能为空，请检查ywgattxz参数", groups = {add.class, edit.class})
    private String ywgattxz;

    /**
     * 港澳台通行证类型
     */
    @NotBlank(message = "港澳台通行证类型不能为空，请检查gattxzlx参数", groups = {add.class, edit.class})
    private String gattxzlx;

    /**
     * 港澳台通行证类型中文值
     */
    @NotBlank(message = "港澳台通行证类型中文值不能为空，请检查gattxzlxName参数", groups = {add.class, edit.class})
    private String gattxzlxName;

    /**
     * 港澳台通行证号码
     */
    @NotBlank(message = "港澳台通行证号码不能为空，请检查gattxzhm参数", groups = {add.class, edit.class})
    private String gattxzhm;

    /**
     * 港澳台通行证保存状态
     */
    @NotBlank(message = "港澳台通行证保存状态不能为空，请检查gattxzbczt参数", groups = {add.class, edit.class})
    private String gattxzbczt;

    /**
     * 港澳台通行证保存状态中文值
     */
    @NotBlank(message = "港澳台通行证保存状态中文值不能为空，请检查gattxzbcztName参数", groups = {add.class, edit.class})
    private String gattxzbcztName;

    /**
     * 有无港澳居民往来内地通行证
     */
    @NotBlank(message = "有无港澳居民往来内地通行证不能为空，请检查ywgajmwlndtxz参数", groups = {add.class, edit.class})
    private String ywgajmwlndtxz;

    /**
     * 港澳居往来内地通行证号码
     */
    @NotBlank(message = "港澳居往来内地通行证号码不能为空，请检查gajmwlndtxz参数", groups = {add.class, edit.class})
    private String gajmwlndtxz;

    /**
     * 港澳居民往来内地通行证保存状态
     */
    @NotBlank(message = "港澳居民往来内地通行证保存状态不能为空，请检查gajmwlndtxzbczt参数", groups = {add.class, edit.class})
    private String gajmwlndtxzbczt;

    /**
     * 港澳居民往来内地通行证保存状态中文值
     */
    @NotBlank(message = "港澳居民往来内地通行证保存状态中文值不能为空，请检查gajmwlndtxzbcztName参数", groups = {add.class, edit.class})
    private String gajmwlndtxzbcztName;

    /**
     * 有无台胞证
     */
    @NotBlank(message = "有无台胞证不能为空，请检查ywtbz参数", groups = {add.class, edit.class})
    private String ywtbz;

    /**
     * 台胞证号码
     */
    @NotBlank(message = "台胞证号码不能为空，请检查tbzhm参数", groups = {add.class, edit.class})
    private String tbzhm;

    /**
     * 台胞证保存状态
     */
    @NotBlank(message = "台胞证保存状态不能为空，请检查tbzbczt参数", groups = {add.class, edit.class})
    private String tbzbczt;

    /**
     * 台胞证保存状态中文值
     */
    @NotBlank(message = "台胞证保存状态中文值不能为空，请检查tbzbcztName参数", groups = {add.class, edit.class})
    private String tbzbcztName;

    /**
     * 暂予监外执行人员身体状况
     */
    @NotBlank(message = "暂予监外执行人员身体状况不能为空，请检查zyjwzxrystzk参数", groups = {add.class, edit.class})
    private String zyjwzxrystzk;

    /**
     * 暂予监外执行人员身体状况中文值
     */
    @NotBlank(message = "暂予监外执行人员身体状况中文值不能为空，请检查zyjwzxrystzkName参数", groups = {add.class, edit.class})
    private String zyjwzxrystzkName;

    /**
     * 最后就诊医院
     */
    @NotBlank(message = "最后就诊医院不能为空，请检查zhjzyy参数", groups = {add.class, edit.class})
    private String zhjzyy;

    /**
     * 是否有精神病
     */
    @NotBlank(message = "是否有精神病不能为空，请检查sfyjsb参数", groups = {add.class, edit.class})
    private String sfyjsb;

    /**
     * 鉴定机构
     */
    @NotBlank(message = "鉴定机构不能为空，请检查jdjg参数", groups = {add.class, edit.class})
    private String jdjg;

    /**
     * 是否有传染病
     */
    @NotBlank(message = "是否有传染病不能为空，请检查sfycrb参数", groups = {add.class, edit.class})
    private String sfycrb;

    /**
     * 具体传染病
     */
    @NotBlank(message = "具体传染病不能为空，请检查jtcrb参数", groups = {add.class, edit.class})
    private String jtcrb;

    /**
     * 具体传染病中文值
     */
    @NotBlank(message = "具体传染病中文值不能为空，请检查jtcrbName参数", groups = {add.class, edit.class})
    private String jtcrbName;

    /**
     * 文化程度
     */
    @NotBlank(message = "文化程度不能为空，请检查whcd参数", groups = {add.class, edit.class})
    private String whcd;

    /**
     * 文化程度中文值
     */
    @NotBlank(message = "文化程度中文值不能为空，请检查whcdName参数", groups = {add.class, edit.class})
    private String whcdName;

    /**
     * 婚姻状况
     */
    @NotBlank(message = "婚姻状况不能为空，请检查hyzk参数", groups = {add.class, edit.class})
    private String hyzk;

    /**
     * 婚姻状况中文值
     */
    @NotBlank(message = "婚姻状况中文值不能为空，请检查hyzkName参数", groups = {add.class, edit.class})
    private String hyzkName;

    /**
     * 捕前职业
     */
    @NotBlank(message = "捕前职业不能为空，请检查pqzy参数", groups = {add.class, edit.class})
    private String pqzy;

    /**
     * 捕前职业中文值
     */
    @NotBlank(message = "捕前职业中文值不能为空，请检查pqzyName参数", groups = {add.class, edit.class})
    private String pqzyName;

    /**
     * 就业就学情况
     */
    @NotBlank(message = "就业就学情况不能为空，请检查jyjxqk参数", groups = {add.class, edit.class})
    private String jyjxqk;

    /**
     * 就业就学情况中文值
     */
    @NotBlank(message = "就业就学情况中文值不能为空，请检查jyjxqkName参数", groups = {add.class, edit.class})
    private String jyjxqkName;

    /**
     * 现政治面貌
     */
    @NotBlank(message = "现政治面貌不能为空，请检查xzzmn参数", groups = {add.class, edit.class})
    private String xzzmn;

    /**
     * 现政治面貌中文值
     */
    @NotBlank(message = "现政治面貌中文值不能为空，请检查xzzmnName参数", groups = {add.class, edit.class})
    private String xzzmnName;

    /**
     * 原政治面貌
     */
    @NotBlank(message = "原政治面貌不能为空，请检查yzzmm参数", groups = {add.class, edit.class})
    private String yzzmm;

    /**
     * 原政治面貌中文值
     */
    @NotBlank(message = "原政治面貌中文值不能为空，请检查yzzmmName参数", groups = {add.class, edit.class})
    private String yzzmmName;

    /**
     * 原工作单位
     */
    @NotBlank(message = "原工作单位不能为空，请检查ygzdw参数", groups = {add.class, edit.class})
    private String ygzdw;

    /**
     * 现工作单位中文值
     */
    @NotBlank(message = "现工作单位中文值不能为空，请检查xgzdwName参数", groups = {add.class, edit.class})
    private String xgzdwName;

    /**
     * 单位联系电话
     */
    @NotBlank(message = "单位联系电话不能为空，请检查dwlxdh参数", groups = {add.class, edit.class})
    private String dwlxdh;

    /**
     * 个人联系电话
     */
    @NotBlank(message = "个人联系电话不能为空，请检查grlxdh参数", groups = {add.class, edit.class})
    private String grlxdh;

    /**
     * 国籍
     */
    @NotBlank(message = "国籍不能为空，请检查gj参数", groups = {add.class, edit.class})
    private String gj;

    /**
     * 国籍中文值
     */
    @NotBlank(message = "国籍中文值不能为空，请检查gjName参数", groups = {add.class, edit.class})
    private String gjName;

    /**
     * 有无家庭成员及主要社会关系
     */
    @NotNull(message = "有无家庭成员及主要社会关系不能为空，请检查yxjtcyjzyshgx参数", groups = {add.class, edit.class})
    private String yxjtcyjzyshgx;

    /**
     * 户籍地是否与居住地相同
     */
    @NotBlank(message = "户籍地是否与居住地相同不能为空，请检查hjdsfyjzdxt参数", groups = {add.class, edit.class})
    private String hjdsfyjzdxt;

    /**
     * 固定居住地所在省（区、市）
     */
    @NotBlank(message = "固定居住地所在省（区、市）不能为空，请检查gdjzdszs参数", groups = {add.class, edit.class})
    private String gdjzdszs;

    /**
     * 固定居住地所在省（区、市）中文值
     */
    @NotBlank(message = "固定居住地所在省（区、市）中文值不能为空，请检查gdjzdszsName参数", groups = {add.class, edit.class})
    private String gdjzdszsName;

    /**
     * 固定居住地所在地（市、州）
     */
    @NotBlank(message = "固定居住地所在地（市、州）不能为空，请检查gdjzdszds参数", groups = {add.class, edit.class})
    private String gdjzdszds;

    /**
     * 固定居住地所在地（市、州）中文值
     */
    @NotBlank(message = "固定居住地所在地（市、州）中文值不能为空，请检查gdjzdszdsName参数", groups = {add.class, edit.class})
    private String gdjzdszdsName;

    /**
     * 固定居住地所在县（市、区）
     */
    @NotBlank(message = "固定居住地所在县（市、区）不能为空，请检查gdjzdszxq参数", groups = {add.class, edit.class})
    private String gdjzdszxq;

    /**
     * 固定居住地所在县（市、区）中文值
     */
    @NotBlank(message = "固定居住地所在县（市、区）中文值不能为空，请检查gdjzdszxqName参数", groups = {add.class, edit.class})
    private String gdjzdszxqName;

    /**
     * 固定居住地（乡镇、街道）
     */
    @NotBlank(message = "固定居住地（乡镇、街道）不能为空，请检查gdjzd参数", groups = {add.class, edit.class})
    private String gdjzd;

    /**
     * 固定居住地（乡镇、街道）中文值
     */
    @NotBlank(message = "固定居住地（乡镇、街道）中文值不能为空，请检查gdjzdName参数", groups = {add.class, edit.class})
    private String gdjzdName;

    /**
     * 固定居住地明细
     */
    @NotNull(message = "固定居住地明细不能为空，请检查gdjzdmx参数", groups = {add.class, edit.class})
    private String gdjzdmx;

    /**
     * 户籍所在省（区、市）
     */
    @NotBlank(message = "户籍所在省（区、市）不能为空，请检查hjszs参数", groups = {add.class, edit.class})
    private String hjszs;

    /**
     * 户籍所在省（区、市）中文值
     */
    @NotBlank(message = "户籍所在省（区、市）中文值不能为空，请检查hjszsName参数", groups = {add.class, edit.class})
    private String hjszsName;

    /**
     * 户籍所在地（市、州）
     */
    @NotBlank(message = "户籍所在地（市、州）不能为空，请检查hjszds参数", groups = {add.class, edit.class})
    private String hjszds;

    /**
     * 户籍所在地（市、州）中文值
     */
    @NotBlank(message = "户籍所在地（市、州）中文值不能为空，请检查hjszdsName参数", groups = {add.class, edit.class})
    private String hjszdsName;

    /**
     * 户籍所在县（市、区）
     */
    @NotBlank(message = "户籍所在县（市、区）不能为空，请检查hjszxq参数", groups = {add.class, edit.class})
    private String hjszxq;

    /**
     * 户籍所在县（市、区）中文值
     */
    @NotBlank(message = "户籍所在县（市、区）中文值不能为空，请检查hjszxqName参数", groups = {add.class, edit.class})
    private String hjszxqName;

    /**
     * 户籍所在地（乡镇、街道）
     */
    @NotBlank(message = "户籍所在地（乡镇、街道）不能为空，请检查hjszd参数", groups = {add.class, edit.class})
    private String hjszd;

    /**
     * 户籍所在地（乡镇、街道）中文值
     */
    @NotBlank(message = "户籍所在地（乡镇、街道）中文值不能为空，请检查hjszdName参数", groups = {add.class, edit.class})
    private String hjszdName;

    /**
     * 户籍所在地明细
     */
    @NotBlank(message = "户籍所在地明细不能为空，请检查hjszdmx参数", groups = {add.class, edit.class})
    private String hjszdmx;

    /**
     * 是否三无人员
     */
    @NotBlank(message = "是否三无人员不能为空，请检查sfswry参数", groups = {add.class, edit.class})
    private String sfswry;

    /**
     * 矫正机构
     */
    @NotBlank(message = "矫正机构不能为空，请检查jzjg参数", groups = {add.class, edit.class})
    private String jzjg;

    /**
     * 矫正机构中文值
     */
    @NotBlank(message = "矫正机构中文值不能为空，请检查jzjgName参数", groups = {add.class, edit.class})
    private String jzjgName;

    /**
     * 是否有前科
     */
    @NotBlank(message = "是否有前科不能为空，请检查sfyqk参数", groups = {add.class, edit.class})
    private String sfyqk;

    /**
     * 是否累犯
     */
    @NotBlank(message = "是否累犯不能为空，请检查sflf参数", groups = {add.class, edit.class})
    private String sflf;

    /**
     * 前科类型
     */
    @NotBlank(message = "前科类型不能为空，请检查qklx参数", groups = {add.class, edit.class})
    private String qklx;

    /**
     * 前科类型中文值
     */
    @NotBlank(message = "前科类型中文值不能为空，请检查qklxName参数", groups = {add.class, edit.class})
    private String qklxName;

    /**
     * 主要犯罪事实
     */
    @NotNull(message = "主要犯罪事实不能为空，请检查zyfzss参数", groups = {add.class, edit.class})
    private String zyfzss;

    /**
     * 社区矫正期限
     */
    @NotBlank(message = "社区矫正期限不能为空，请检查sqjzqx参数", groups = {add.class, edit.class})
    private String sqjzqx;

    /**
     * 社区矫正开始日期
     */
    @NotNull(message = "社区矫正开始日期不能为空，请检查sqjzksrq参数", groups = {add.class, edit.class})
    private String sqjzksrq;

    /**
     * 社区矫正结束日期
     */
    @NotNull(message = "社区矫正结束日期不能为空，请检查sqjzjsrq参数", groups = {add.class, edit.class})
    private String sqjzjsrq;

    /**
     * 犯罪类型
     */
    @NotNull(message = "犯罪类型不能为空，请检查fzlx参数", groups = {add.class, edit.class})
    private String fzlx;

    /**
     * 犯罪类型中文值
     */
    @NotNull(message = "犯罪类型中文值不能为空，请检查fzlxName参数", groups = {add.class, edit.class})
    private String fzlxName;

    /**
     * 具体罪名
     */
    @NotNull(message = "具体罪名不能为空，请检查jtzm参数", groups = {add.class, edit.class})
    private String jtzm;

    /**
     * 具体罪名中文值
     */
    @NotNull(message = "具体罪名中文值不能为空，请检查jtzmName参数", groups = {add.class, edit.class})
    private String jtzmName;

    /**
     * 是否“五独”
     */
    @NotBlank(message = "是否“五独”不能为空，请检查sfwd参数", groups = {add.class, edit.class})
    private String sfwd;

    /**
     * 是否“五涉”
     */
    @NotBlank(message = "是否“五涉”不能为空，请检查sfws参数", groups = {add.class, edit.class})
    private String sfws;

    /**
     * 是否有“四史”
     */
    @NotBlank(message = "是否有“四史”不能为空，请检查sfyss参数", groups = {add.class, edit.class})
    private String sfyss;

    /**
     * 是否被宣告禁止令
     */
    @NotBlank(message = "是否被宣告禁止令不能为空，请检查sfbxgjzl参数", groups = {add.class, edit.class})
    private String sfbxgjzl;

    /**
     * 社区矫正人员接收日期
     */
    @NotNull(message = "社区矫正人员接收日期不能为空，请检查sqjzryjsrq参数", groups = {add.class, edit.class})
    private String sqjzryjsrq;

    /**
     * 社区矫正人员接收方式
     */
    @NotBlank(message = "社区矫正人员接收方式不能为空，请检查sqjzryjsfs参数", groups = {add.class, edit.class})
    private String sqjzryjsfs;

    /**
     * 社区矫正人员接收方式中文
     */
    @NotBlank(message = "社区矫正人员接收方式中文不能为空，请检查sqjzryjsfsName参数", groups = {add.class, edit.class})
    private String sqjzryjsfsName;

    /**
     * 报到情况
     */
    @NotBlank(message = "报到情况不能为空，请检查bdqk参数", groups = {add.class, edit.class})
    private String bdqk;

    /**
     * 报到情况中文值
     */
    @NotBlank(message = "报到情况中文值不能为空，请检查bdqkName参数", groups = {add.class, edit.class})
    private String bdqkName;

    /**
     * 未按时报到情况说明
     */
    @NotNull(message = "未按时报到情况说明不能为空，请检查wasbdqksm参数", groups = {add.class, edit.class})
    private String wasbdqksm;

    /**
     * 是否建立矫正小组
     */
    @NotBlank(message = "是否建立矫正小组不能为空，请检查sfjljzxz参数", groups = {add.class, edit.class})
    private String sfjljzxz;

    /**
     * 是否采用电子定位管理
     */
    @NotBlank(message = "是否采用电子定位管理不能为空，请检查sfcydzdwgl参数", groups = {add.class, edit.class})
    private String sfcydzdwgl;

    /**
     * 电子定位方式
     */
    @NotBlank(message = "电子定位方式不能为空，请检查dzdwfs参数", groups = {add.class, edit.class})
    private String dzdwfs;

    /**
     * 电子定位方式中文值
     */
    @NotBlank(message = "电子定位方式中文值不能为空，请检查dzdwfsName参数", groups = {add.class, edit.class})
    private String dzdwfsName;

    /**
     * 定位号码
     */
    @NotBlank(message = "定位号码不能为空，请检查dwhm参数", groups = {add.class, edit.class})
    private String dwhm;

    /**
     * 备注
     */
    @NotNull(message = "备注不能为空，请检查bz参数", groups = {add.class, edit.class})
    private String bz;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空，请检查zhuangtai参数", groups = {add.class, edit.class})
    private String zhuangtai;

    /**
     * 入矫日期
     */
    @NotNull(message = "入矫日期不能为空，请检查rujiaoriqi参数", groups = {add.class, edit.class})
    private String rujiaoriqi;

    /**
     * 最后修改时间
     */
    @NotNull(message = "最后修改时间不能为空，请检查lastModifiedTime参数", groups = {add.class, edit.class})
    private String lastModifiedTime;

    /**
     * 矫正级别
     */
    @NotBlank(message = "矫正级别不能为空，请检查jzjb参数", groups = {add.class, edit.class})
    private String jzjb;

    /**
     * 矫正级别中文值
     */
    @NotBlank(message = "矫正级别中文值不能为空，请检查jzjbName参数", groups = {add.class, edit.class})
    private String jzjbName;

    /**
     * 调查评估意见
     */
    @NotNull(message = "调查评估意见不能为空，请检查dcpgyj参数", groups = {add.class, edit.class})
    private String dcpgyj;

    /**
     * 评估意见采信
     */
    @NotNull(message = "评估意见采信不能为空，请检查pgyjcx参数", groups = {add.class, edit.class})
    private String pgyjcx;

    /**
     * 电子定位监控
     */
    @NotBlank(message = "电子定位监控不能为空，请检查dzdwjk参数", groups = {add.class, edit.class})
    private String dzdwjk;

    /**
     * 定位手环设备号
     */
    @NotBlank(message = "定位手环设备号不能为空，请检查dwshsbh参数", groups = {add.class, edit.class})
    private String dwshsbh;

    /**
     * 是否居住地变更迁入
     */
    @NotBlank(message = "是否居住地变更迁入不能为空，请检查sfbgqr参数", groups = {add.class, edit.class})
    private String sfbgqr;

    /**
     * 是否重点对象
     */
    @NotBlank(message = "是否重点对象不能为空，请检查zddx参数", groups = {add.class, edit.class})
    private String zddx;

    /**
     * 是否正在被刑拘拘留
     */
    @NotBlank(message = "是否正在被刑拘拘留不能为空，请检查sfxj参数", groups = {add.class, edit.class})
    private String sfxj;

    /**
     * 社区矫正决定机关
     */
    @NotBlank(message = "社区矫正决定机关不能为空，请检查jdjglx参数", groups = {add.class, edit.class})
    private String jdjglx;

    /**
     * 社区矫正决定机关名称
     */
    @NotBlank(message = "社区矫正决定机关名称不能为空，请检查jdjgmc参数", groups = {add.class, edit.class})
    private String jdjgmc;

    /**
     * 执行通知书文号
     */
    @NotBlank(message = "执行通知书文号不能为空，请检查zxtzswh参数", groups = {add.class, edit.class})
    private String zxtzswh;

    /**
     * 执行通知书日期
     */
    @NotNull(message = "执行通知书日期不能为空，请检查zxtzsrq参数", groups = {add.class, edit.class})
    private String zxtzsrq;

    /**
     * 交付执行日期
     */
    @NotNull(message = "交付执行日期不能为空，请检查jfzxrq参数", groups = {add.class, edit.class})
    private String jfzxrq;

    /**
     * 罪名
     */
    @NotBlank(message = "罪名不能为空，请检查zm参数", groups = {add.class, edit.class})
    private String zm;

    /**
     * 缓刑考验期
     */
    @NotBlank(message = "缓刑考验期不能为空，请检查hxkyqx参数", groups = {add.class, edit.class})
    private String hxkyqx;

    /**
     * 是否数罪并罚
     */
    @NotBlank(message = "是否数罪并罚不能为空，请检查sfszbf参数", groups = {add.class, edit.class})
    private String sfszbf;

    /**
     * 原判判决书字号
     */
    @NotBlank(message = "原判判决书字号不能为空，请检查pjszh参数", groups = {add.class, edit.class})
    private String pjszh;

    /**
     * 原判刑期开始时间
     */
    @NotNull(message = "原判刑期开始时间不能为空，请检查ypxqkssj参数", groups = {add.class, edit.class})
    private String ypxqkssj;

    /**
     * 原判刑期结束时间
     */
    @NotNull(message = "原判刑期结束时间不能为空，请检查ypxqjssj参数", groups = {add.class, edit.class})
    private String ypxqjssj;

    /**
     * 文书生效日期
     */
    @NotNull(message = "文书生效日期不能为空，请检查wssxrq参数", groups = {add.class, edit.class})
    private String wssxrq;

    /**
     * 有期徒刑期限
     */
    @NotBlank(message = "有期徒刑期限不能为空，请检查yqtxqx参数", groups = {add.class, edit.class})
    private String yqtxqx;

    /**
     * 罚金
     */
    @NotBlank(message = "罚金不能为空，请检查fj参数", groups = {add.class, edit.class})
    private String fj;

    /**
     * 抄报
     */
    @NotBlank(message = "抄报不能为空，请检查chaobao参数", groups = {add.class, edit.class})
    private String chaobao;

    /**
     * 是否共同犯罪
     */
    @NotBlank(message = "是否共同犯罪不能为空，请检查sfgtfz参数", groups = {add.class, edit.class})
    private String sfgtfz;

}
