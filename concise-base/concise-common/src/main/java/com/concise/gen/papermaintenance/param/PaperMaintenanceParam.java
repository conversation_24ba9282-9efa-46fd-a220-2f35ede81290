package com.concise.gen.papermaintenance.param;

import java.util.List;
import java.util.Set;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.papertopic.param.PaperTopicParam;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* 量卷维护参数类
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:55
*/
@Data
public class PaperMaintenanceParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 量卷名称
     */
    // @NotBlank(message = "量卷名称不能为空，请检查title参数", groups = {add.class, edit.class})
    private String title;

    /**
     * 量卷类型，字典值：LJLX
     */
    // @NotBlank(message = "量卷类型，字典值：LJLX不能为空，请检查paperType参数", groups = {add.class, edit.class})
    private String paperType;

    /**
     * 笔录类型，字典值：BLLX
     */
    private String blType;

    /**
     * 笔录类型名称
     */
    private String blTypeName;

    /**
     * 状态，0：启用 1：禁用
     */
    private Integer status;

    /**
     * 使用单位
     */
    // @NotBlank(message = "使用单位不能为空，请检查jzjg参数", groups = {add.class, edit.class})
    private String jzjg;

    /**
     * 使用单位名称
     */
    private String jzjgName;

    /**
     * 分数，该分数及上结论是适宜社区矫正、以下是不适宜社区矫正
     */
    private Integer score;

    /**
     * 删除状态， 0：未删除  1：已删除
     */
    private Integer delFlag;

    /**
     * ids
     */
    private List<String> idList;

    private Set<String> jzjglist;

    /**
     * 题目列表
     */
    @ApiModelProperty(value = "题目列表")
    private List<PaperTopicParam> topicList;
}
