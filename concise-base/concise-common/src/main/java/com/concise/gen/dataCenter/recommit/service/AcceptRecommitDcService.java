package com.concise.gen.dataCenter.recommit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.gen.acceptrecommit.entity.AcceptRecommit;
import com.concise.gen.dataCenter.recommit.entity.AcceptRecommitDc;

/**
 * 公安再犯罪协同接收表service接口
 *
 * <AUTHOR>
 * @date 2023-08-02 11:42:25
 */
public interface AcceptRecommitDcService extends IService<AcceptRecommitDc> {
    /**
     * 同步至数据中心
     * @param model AcceptRecommit
     */
    void sync(AcceptRecommit model);
}
