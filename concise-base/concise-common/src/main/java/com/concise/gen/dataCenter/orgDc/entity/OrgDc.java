package com.concise.gen.dataCenter.orgDc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 数据中心机构表
 *
 * <AUTHOR>
 * @date 2023-11-23 16:15:33
 */
@Data
@TableName("sys_org_qn")
public class OrgDc{

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 父id
     */
    private String pid;

    /**
     * 父ids
     */
    private String pids;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 排序
     */
    private Integer sort;

    /**
     *
     */
    private String type;

    /**
     * 描述
     */
    private String remark;

    /**
     * 状态（字典 0正常 1停用 2删除）
     */
    private Integer status;

    /**
     * 机构完整名称
     */
    private String fullName;

    /**
     * 1省级，2市级，3区县级 4所街道
     */
    private Integer level;

    /**
     *
     */
    private Integer adcode;
    private String lxr;
    private String lxdh;

}
