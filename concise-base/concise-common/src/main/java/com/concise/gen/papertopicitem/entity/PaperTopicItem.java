package com.concise.gen.papertopicitem.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;

import java.util.Date;

/**
 * 量卷维护-题目-指标选项
 *
 * <AUTHOR>
 * @date 2025-03-20 15:03:05
 */
@Data
@TableName("paper_topic_item")
public class PaperTopicItem {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 量卷-题目主键
     */
    private String paperTopicId;

    /**
     * 指标选项内容
     */
    private String content;

    /**
     * 分数
     */
    private Integer itemScore;

    /**
     * 排序号
     */
    private Integer serialNumber;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;
}
