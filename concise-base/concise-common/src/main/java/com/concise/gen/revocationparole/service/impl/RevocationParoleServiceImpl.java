package com.concise.gen.revocationparole.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.base.enums.OrgTypeEnum;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.dataCenter.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.dataCenter.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.gen.dataCenter.orgDc.entity.OrgDc;
import com.concise.gen.dataCenter.orgDc.service.OrgDcService;
import com.concise.gen.extorginfo.entity.ExtOrgInfo;
import com.concise.gen.extorginfo.service.ExtOrgInfoService;
import com.concise.gen.revocationauditphase.constant.RevocationPhaseVar;
import com.concise.gen.revocationauditphase.entity.RevocationAuditPhase;
import com.concise.gen.revocationauditphase.entity.TaskInfo;
import com.concise.gen.revocationauditphase.param.RevocationAuditPhaseParam;
import com.concise.gen.revocationauditphase.service.RevocationAuditPhaseService;
import com.concise.gen.revocationparole.entity.RevocationParole;
import com.concise.gen.revocationparole.mapper.RevocationParoleMapper;
import com.concise.gen.revocationparole.param.RevocationParoleParam;
import com.concise.gen.revocationparole.service.RevocationParoleService;
import com.concise.gen.revocationprobation.param.RevParam;
import com.concise.gen.webservice.service.SendRevocationParoleService;
import com.concise.gen.webservice.utils.DictWdToHyUtils;
import org.activiti.engine.HistoryService;
import org.activiti.engine.IdentityService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 提请撤销假释service接口实现类
 *
 * <AUTHOR>
 * @date 2022-06-13 10:14:21
 */
@Service
public class RevocationParoleServiceImpl extends ServiceImpl<RevocationParoleMapper, RevocationParole> implements RevocationParoleService {

    @Resource
    private RevocationAuditPhaseService auditService;
    @Resource
    private CorrectionObjectInformationService objService;
    @Resource
    private SendRevocationParoleService sendService;
    @Resource
    private OrgDcService orgDcService;
    @Resource
    private ExtOrgInfoService extOrgInfoService;
    @Resource
    private SysFileInfoService sysFileInfoService;
    @Resource
    private RuntimeService runtimeService;
    @Resource
    private TaskService taskService;
    @Resource
    private IdentityService identityService;
    @Resource
    private HistoryService historyService;

    @Override
    public PageResult<RevocationParole> page(RevocationParoleParam param) {
        QueryWrapper<RevocationParole> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().and(i -> i.eq(RevocationParole::getJzdwId, param.getJzdwId()).or().like(RevocationParole::getJzdwPids, param.getJzdwId()));
        if (ObjectUtil.isNotNull(param)) {

            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(RevocationParole::getXm, param.getXm());
            }
            // 根据征求意见日期 查询
            if (ObjectUtil.isNotEmpty(param.getZqyjrq())) {
                queryWrapper.apply("date_format (zqyjrq,'%Y-%m-%d') = '" + param.getZqyjrq() + "'");
            }
            // 根据提请日期 查询
            if (ObjectUtil.isNotEmpty(param.getTqrq())) {
                queryWrapper.apply("date_format (tqrq,'%Y-%m-%d') = '" + param.getTqrq() + "'");
            }
            // 根据提请法院名称 查询
            if (ObjectUtil.isNotEmpty(param.getFymc())) {
                queryWrapper.lambda().like(RevocationParole::getFymc, param.getFymc());
            }
            // 根据提请状态 查询
            if (ObjectUtil.isNotEmpty(param.getZt())) {
                queryWrapper.lambda().eq(RevocationParole::getZt, param.getZt());
            }
        }
        queryWrapper.lambda().orderByDesc(RevocationParole::getTqrq);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<RevocationParole> list(RevocationParoleParam revocationParoleParam) {
        return this.list();
    }

    @Override
    public void add(RevocationParoleParam param) {
        RevocationParole model = new RevocationParole();
        BeanUtil.copyProperties(param, model);
        model.setId(IdUtil.fastSimpleUUID());
        model.setTyfh(model.getId());

        model.setAddTime(DateUtil.date());
        model.setAddPsnId(param.getProcessPsnId());
        model.setAddPsnName(param.getProcessPsnName());

        CorrectionObjectInformation obj = objService.getById(param.getSqjzryId());
        model.setSqjzajbh(obj.getId());
        model.setXm(obj.getXm());
        model.setXb(obj.getXb());
        model.setCsrq(obj.getCsrq());
        model.setZjhm(obj.getSfzh());
        model.setGj(obj.getGj());
        model.setXzd(obj.getGdjzdszs()+","+obj.getGdjzdszds()+","+obj.getGdjzdszxq()+","+obj.getGdjzd());
        model.setXzdxz(obj.getGdjzdmx());
        model.setHjd(obj.getHjszs()+","+obj.getHjszds()+","+obj.getHjszxq()+","+obj.getHjszd());
        model.setHjdxz(obj.getHjszdmx());
        model.setJzlb(DictWdToHyUtils.jzlb(obj.getJzlb()));
        model.setSfs(obj.getJzjgName());
        model.setSqjzksrq(obj.getSqjzksrq());
        model.setSqjzjsrq(obj.getSqjzjsrq());
        model.setJzqx(obj.getSqjzqx());
        OrgDc org = orgDcService.getById(obj.getJzjg());
        model.setJzdw(org.getCode());
        model.setJzdwId(org.getId());
        model.setJzdwmc(org.getName());
        model.setJzdwPids(org.getPids());
        model.setZt("sh");
        model.setFiles2(model.getFiles1());
        model.setFiles3(model.getFiles1());
        model.setFiles4(model.getFiles1());

        ExtOrgInfo fy = extOrgInfoService.lambdaQuery().eq(ExtOrgInfo::getOrgCode, model.getFyP1()).eq(ExtOrgInfo::getType, OrgTypeEnum.FA_YUAN.getCode()).one();
        ExtOrgInfo jcy = extOrgInfoService.lambdaQuery().eq(ExtOrgInfo::getOrgCode, model.getJcyP1()).eq(ExtOrgInfo::getType, OrgTypeEnum.JIAN_CHA_YUAN.getCode()).one();
        model.setProcessType(fy.getOrgLevel());

        model.setFy(model.getFyP1());
        model.setFymc(fy.getOrgName());
        model.setJcy(model.getJcyP1());
        model.setJcymc(jcy.getOrgName());
        model.setZqyjtqly(model.getTqlyP1());
        model.setZqyjtqyj(model.getTqyjP1());
        model.setTqly(model.getTqlyP1());
        model.setTqyj(model.getTqyjP1());

        this.save(model);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(RevocationParoleParam revocationParoleParam) {
        RevocationParole byId = this.getById(revocationParoleParam.getId());
        if (ObjectUtil.isNotEmpty(byId.getProcessInstanceId())) {
            runtimeService.deleteProcessInstance(byId.getProcessInstanceId(), "delete");
        }
        this.removeById(revocationParoleParam.getId());
    }

    @Override
    public void edit(RevocationParoleParam param) {
        RevocationParole model = this.getById(param.getId());
        if (ObjectUtil.isNotEmpty(model.getProcessInstanceId())) {
            return;
        }
        if (!model.getSqjzryId().equals(param.getSqjzryId())) {
            CorrectionObjectInformation obj = objService.getById(param.getSqjzryId());
            BeanUtil.copyProperties(param, model);
            model.setXm(obj.getXm());
            model.setXb(obj.getXb());
            model.setCsrq(obj.getCsrq());
            model.setZjhm(obj.getSfzh());
            model.setGj(obj.getGj());
            model.setXzdxz(obj.getGdjzdmx());
            model.setHjdxz(obj.getHjszdmx());
            model.setJzlb(DictWdToHyUtils.jzlb(obj.getJzlb()));
            model.setSfs(obj.getJzjgName());
            model.setSqjzksrq(obj.getSqjzksrq());
            model.setSqjzjsrq(obj.getSqjzjsrq());
            model.setJzqx(obj.getSqjzqx());
            OrgDc org = orgDcService.getById(obj.getJzjg());
            model.setJzdw(org.getCode());
            model.setJzdwId(org.getId());
            model.setJzdwmc(org.getName());
            model.setJzdwPids(org.getPids());
        }else {
            BeanUtil.copyProperties(param, model);
        }

        ExtOrgInfo fy = extOrgInfoService.lambdaQuery().eq(ExtOrgInfo::getOrgCode, model.getFyP1()).eq(ExtOrgInfo::getType, OrgTypeEnum.FA_YUAN.getCode()).one();
        ExtOrgInfo jcy = extOrgInfoService.lambdaQuery().eq(ExtOrgInfo::getOrgCode, model.getJcyP1()).eq(ExtOrgInfo::getType, OrgTypeEnum.JIAN_CHA_YUAN.getCode()).one();
        model.setProcessType(fy.getOrgLevel());

        model.setFy(model.getFyP1());
        model.setFymc(fy.getOrgName());
        model.setJcy(model.getJcyP1());
        model.setJcymc(jcy.getOrgName());
        model.setZqyjtqly(model.getTqlyP1());
        model.setZqyjtqyj(model.getTqyjP1());
        model.setTqly(model.getTqlyP1());
        model.setTqyj(model.getTqyjP1());
        model.setAddTime(DateUtil.date());
        model.setAddPsnId(param.getProcessPsnId());
        model.setAddPsnName(param.getProcessPsnName());

        this.updateById(model);
    }

    @Override
    public void audit(RevocationAuditPhaseParam param) {
        String id = param.getId();
        param.setContactId(id);
        RevocationParole byId = this.getById(id);
        String processKey = RevocationPhaseVar.PROCESS_DEFINITION_KEY.getString(byId.getProcessType());
        if (ObjectUtil.isEmpty(byId.getProcessInstanceId())) {
            Task ex = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(id).singleResult();
            if (ex == null) {
                HashMap<String, Object> vars = new HashMap<>(3);
                vars.put("processPsnId", byId.getAddPsnId());
                vars.put("processPsnName", byId.getAddPsnName());
                ProcessInstance rs = runtimeService.startProcessInstanceByKey(processKey, id, vars);
                identityService.setAuthenticatedUserId(param.getProcessPsnId());
                Task addTask = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(id).singleResult();
                taskService.addComment(addTask.getId(), addTask.getProcessInstanceId(), getComment(param.getProcessPsnId(),param.getProcessPsnName(), "", ""));
                taskService.complete(addTask.getId(), vars);
                byId.setProcessInstanceId(rs.getProcessInstanceId());
            }
        }

        HashMap<String, Object> vars = new HashMap<>(4);
        vars.put("processPsnId", param.getProcessPsnId());
        vars.put("processPsnName", param.getProcessPsnName());
        vars.put("result", param.getCsjg());
        identityService.setAuthenticatedUserId(param.getProcessPsnId());
        Task task = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(id).singleResult();
        taskService.addComment(task.getId(), task.getProcessInstanceId(), getComment(param.getProcessPsnId(),param.getProcessPsnName(), param.getCsyj(), param.getCsjg()));
        taskService.complete(task.getId(), vars);

        param.setAuditPhase(task.getTaskDefinitionKey());
        auditService.add(param);

        //获取下一个流程节点
        Task next = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(id).singleResult();

        if (next == null) {
            byId.setZt("zz");
        }else {
            byId.setZt(next.getTaskDefinitionKey());
        }
        this.updateById(byId);
    }

    @Override
    public void submit(RevocationParoleParam param) {
        RevocationParole model = this.getById(param.getId());
        String processKey = RevocationPhaseVar.PROCESS_DEFINITION_KEY.getString(model.getProcessType());
        BeanUtil.copyProperties(param, model);
        if ("zqjcjy".equals(model.getZt())) {
            boolean b = sendService.request4405(model, sysFileInfoService.getDocListByIds(model.getFiles1()));
            if (!b){
                return;
            }
        }
        if ("tqcx".equals(model.getZt())) {
            boolean b = sendService.request4407_2(model, sysFileInfoService.getDocListByIds(model.getFiles2()));
            if (!b){
                return;
            }
        }
        this.updateById(model);

        HashMap<String, Object> vars = new HashMap<>(4);
        vars.put("processPsnId", param.getProcessPsnId());
        vars.put("processPsnName", param.getProcessPsnName());
        identityService.setAuthenticatedUserId(param.getProcessPsnId());
        Task task = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(param.getId()).singleResult();
        taskService.addComment(task.getId(), task.getProcessInstanceId(), getComment(param.getProcessPsnId(),param.getProcessPsnName(),"",""));
        taskService.complete(task.getId(), vars);

        //获取下一个流程节点
        Task next = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(model.getId()).singleResult();
        model.setZt(next.getTaskDefinitionKey());
        this.updateById(model);
    }

    @Override
    public void updateByTyfh(RevocationParoleParam param, String step) {
        RevocationParole model = this.getById(param.getId());
        if (model == null) {
            return;
        }
        String processKey = RevocationPhaseVar.PROCESS_DEFINITION_KEY.getString(model.getProcessType());
        HashMap<String, Object> vars = new HashMap<>(4);
        vars.put("processPsnId", "111");
        vars.put("processPsnName", "一体化");
        identityService.setAuthenticatedUserId("111");
        Task task = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(param.getId()).singleResult();
        if ("XTBH4406".equals(step)) {
            model.setJcyjwswh("检察意见文书文号");
            model.setCxjsjcyj("撤销假释检察建议");
            model.setJcyjbz("检查意见备注");
            model.setYjfksj(DateUtil.date());
            taskService.addComment(task.getId(), task.getProcessInstanceId(), getComment(model.getJcy(),model.getJcymc(),model.getCxjsjcyj(),""));
        }
        if ("XTBH4410_1".equals(step)) {
            model.setJdswh("决定书文号");
            model.setSfjdcxjs("1");
            model.setCxjsjdrq(DateUtil.date());
            model.setCxjsyy("撤销假释原因");
            model.setCxjsjdjg(model.getFy());
            taskService.addComment(task.getId(), task.getProcessInstanceId(), getComment(model.getFy(),model.getFymc(),model.getSfjdcxjs(),""));
        }
        taskService.complete(task.getId(), vars);


        //获取下一个流程节点
        Task next = taskService.createTaskQuery().processDefinitionKey(processKey).processInstanceBusinessKey(model.getId()).singleResult();
        if (next == null) {
            model.setZt("ywc");
        }else {
            model.setZt(next.getTaskDefinitionKey());
        }
        this.updateById(model);
    }

    private String getComment(String userId, String userName, String remark, String approve) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userId", userId);
        jsonObject.put("userName", userName);
        jsonObject.put("approve", approve);
        jsonObject.put("remark", remark);
        return jsonObject.toJSONString();
    }

    @Override
    public RevocationParole detail(RevocationParoleParam param) {
        RevocationParole detail = this.getById(param.getId());
        detail.setProcessNodes(RevocationPhaseVar.PROCESS_DEFINITION.getString(detail.getProcessType()));
        JSONObject phase = new JSONObject();
        auditService.lambdaQuery().eq(RevocationAuditPhase::getContactId, param.getId())
                .eq(RevocationAuditPhase::getDelFlag, "0")
                .list().forEach(audit -> phase.put(audit.getAuditPhase(),audit));
        detail.setPhase(phase);
        String processInstanceId = detail.getProcessInstanceId();
        if (ObjectUtil.isNotEmpty(processInstanceId)) {
            List<HistoricActivityInstance> history = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).activityType("userTask").orderByHistoricActivityInstanceStartTime().asc().list();

            List<TaskInfo> infos = new ArrayList<>();
            history.forEach(h->{
                TaskInfo info = new TaskInfo();
                info.setProcessInstanceId(h.getProcessInstanceId());
                info.setStartTime(DateUtil.formatDateTime(h.getStartTime()));
                if (h.getEndTime() != null) {
                    info.setEndTime(DateUtil.formatDateTime(h.getEndTime()));
                }
                info.setTaskName(h.getActivityName());
                List<Comment> comments = taskService.getTaskComments(h.getTaskId());
                if (comments.size() > 0) {
                    JSONObject msg = JSON.parseObject(comments.get(0).getFullMessage());
                    info.setAssignee(msg.getString("userName"));
                    info.setApprove(msg.getString("approve"));
                    info.setComment(msg.getString("remark"));
                }
                infos.add(info);
            });
            detail.setTaskInfoList(infos);
        }
        return detail;
    }

    @Override
    public void revocationParole(RevParam param) {

        CorrectionObjectInformation obj = objService.getById(param.getPid());
        if (obj == null) {
            throw new RuntimeException("社区矫正对象标识错误!");
        }
        RevocationParole model = new RevocationParole();
        model.setTyfh(param.getTyfh());
        model.setSqjzajbh(obj.getId());
        model.setZfbh(obj.getId());
        model.setSqjzryId(obj.getId());
        model.setXm(obj.getXm());
        model.setXb(obj.getXb());
        model.setZjhm(obj.getSfzh());
        model.setCsrq(obj.getCsrq());
        model.setJzlb(DictWdToHyUtils.jzlb(obj.getJzlb()));
        //社区矫正信息决定机关
        model.setJdjg("");
        model.setSfs(obj.getJzjg());
        model.setSqjzksrq(obj.getSqjzksrq());
        model.setSqjzjsrq(obj.getSqjzjsrq());

        model.setJyswh(param.getJyswh());
        model.setTqrq(DateUtil.parse(param.getTqrq()));
        model.setTqly(param.getTqly());
        model.setTqyj(param.getTqyj());
        model.setFy(param.getFy());
        model.setFymc(extOrgInfoService.getExtOrgName(OrgTypeEnum.FA_YUAN.getCode(), param.getFy()));
        model.setJcy(param.getJcy());
        model.setJcymc(extOrgInfoService.getExtOrgName(OrgTypeEnum.JIAN_CHA_YUAN.getCode(), param.getJcy()));

        if ("4405".equals(param.getXtbh())) {
            this.save(model);
        }
        if ("4407".equals(param.getXtbh())) {
            this.updateById(model);
        }
    }
}
