package com.concise.gen.investigationinfoflow.util;

import com.concise.gen.investigationinfoflow.entity.InvestigationInfoFlow;

import java.util.*;
import java.util.stream.Collectors;

public class InvestigationFlowUtils {

    /**
     * 将流程列表按照连续的相同 nodeLevel 分组
     * @param flows 原始流程列表
     * @return 分组后的流程列表，每个分组包含 nodeLevel 和 childNode
     */
    public static List<Map<String, Object>> groupFlowsByNodeLevel(List<InvestigationInfoFlow> flows) {
        List<Map<String, Object>> result = new ArrayList<>();

        if (flows == null || flows.isEmpty()) {
            return result;
        }

        // 当前分组的 nodeLevel
        Integer currentLevel = null;
        // 当前分组的流程列表
        List<InvestigationInfoFlow> currentGroup = new ArrayList<>();

        for (InvestigationInfoFlow flow : flows) {
            if (currentLevel == null) {
                // 第一个元素，初始化分组
                currentLevel = flow.getNodeLevel();
                currentGroup.add(flow);
            } else if (flow.getNodeLevel().equals(currentLevel)) {
                // 当前流程的 nodeLevel 与当前分组相同，加入当前分组
                currentGroup.add(flow);
            } else {
                // nodeLevel 变化，将当前分组加入结果，并创建新分组
                addGroupToResult(result, currentLevel, currentGroup);
                currentLevel = flow.getNodeLevel();
                currentGroup = new ArrayList<>();
                currentGroup.add(flow);
            }
        }

        // 添加最后一个分组
        if (!currentGroup.isEmpty()) {
            addGroupToResult(result, currentLevel, currentGroup);
        }

        return result;
    }

    /**
     * 将分组添加到结果列表中
     */
    private static void addGroupToResult(List<Map<String, Object>> result,
                                         Integer level,
                                         List<InvestigationInfoFlow> group) {
        Map<String, Object> groupMap = new LinkedHashMap<>();
        groupMap.put("nodeLevel", level);
        groupMap.put("childNode", new ArrayList<>(group));
        result.add(groupMap);
    }

    /**
     * 将流程列表按照相同 nodeLevel 分组
     * @param flowList 原始流程列表
     * @return 分组后的流程列表，每个分组包含 nodeLevel 和 childNode
     */
    public static List<Map<String, Object>> convertToGroupedList(List<InvestigationInfoFlow> flowList) {
        // 按nodeLevel分组
        Map<Integer, List<InvestigationInfoFlow>> groupedByLevel = flowList.stream()
                .collect(Collectors.groupingBy(InvestigationInfoFlow::getNodeLevel));

        // 创建结果列表
        List<Map<String, Object>> result = new ArrayList<>();

        // 遍历分组后的数据
        groupedByLevel.forEach((nodeLevel, flows) -> {
            Map<String, Object> levelMap = new HashMap<>();
            levelMap.put("nodeLevel", nodeLevel);
            levelMap.put("childNode", flows);
            result.add(levelMap);
        });

        // 按nodeLevel排序
        result.sort((a, b) -> {
            int levelA = (int) a.get("nodeLevel");
            int levelB = (int) b.get("nodeLevel");
            return Integer.compare(levelA, levelB);
        });

        return result;
    }
}
