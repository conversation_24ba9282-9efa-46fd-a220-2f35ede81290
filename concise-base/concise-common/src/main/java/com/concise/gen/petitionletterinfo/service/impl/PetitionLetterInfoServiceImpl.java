package com.concise.gen.petitionletterinfo.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.PoiUtil;
import com.concise.gen.petitionletterinfo.entity.PetitionLetterInfo;
import com.concise.gen.petitionletterinfo.mapper.PetitionLetterInfoMapper;
import com.concise.gen.petitionletterinfo.param.PetitionLetterInfoParam;
import com.concise.gen.petitionletterinfo.service.PetitionLetterInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 信访信息service接口实现类
 *
 * <AUTHOR>
 * @date 2024-04-02 14:11:20
 */
@Service
public class PetitionLetterInfoServiceImpl extends ServiceImpl<PetitionLetterInfoMapper, PetitionLetterInfo> implements PetitionLetterInfoService {

    @Override
    public PageResult<PetitionLetterInfo> page(PetitionLetterInfoParam param) {
        QueryWrapper<PetitionLetterInfo> queryWrapper = buildQueryWrapper(param);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    private QueryWrapper<PetitionLetterInfo> buildQueryWrapper(PetitionLetterInfoParam param) {
        QueryWrapper<PetitionLetterInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(PetitionLetterInfo::getDeptIds, param.getDeptId());
        if (ObjectUtil.isNotNull(param)) {

            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(param.getName())) {
                queryWrapper.lambda().like(PetitionLetterInfo::getName, param.getName());
            }
            if (ObjectUtil.isNotEmpty(param.getZhuangtai())) {
                if ("200".equalsIgnoreCase(param.getZhuangtai())) {
                    queryWrapper.lambda().eq(PetitionLetterInfo::getZhuangtai, "200");
                }else {
                    queryWrapper.lambda().ne(PetitionLetterInfo::getZhuangtai, "200");
                }
            }
            if (ObjectUtil.isNotEmpty(param.getLetterType())) {
                queryWrapper.lambda().eq(PetitionLetterInfo::getLetterType, param.getLetterType());
            }
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper.apply("date_format (update_time,'%Y-%m-%d') >= date_format('" + param.getSearchBeginTime() + "','%Y-%m-%d')")
                        .apply("date_format (update_time,'%Y-%m-%d') <= date_format('" + param.getSearchEndTime() + "','%Y-%m-%d')");
            }
        }
        queryWrapper.lambda().orderByDesc(PetitionLetterInfo::getUpdateTime);
        return queryWrapper;
    }

    @Override
    public List<PetitionLetterInfo> list(PetitionLetterInfoParam param) {
        return this.list(buildQueryWrapper(param));
    }

    @Override
    public void export(PetitionLetterInfoParam param) {
        List<PetitionLetterInfo> list = this.list(buildQueryWrapper(param));
        PoiUtil.exportExcelWithStream("信访信息.xls", PetitionLetterInfo.class, list);
    }

    @Override
    public void saveOrUpdateByLetterNo(PetitionLetterInfo model) {
        model.setUpdateTime(DateUtil.date());
        List<PetitionLetterInfo> list = this.lambdaQuery().eq(PetitionLetterInfo::getLetterNo, model.getLetterNo()).list();
        if (ObjectUtil.isNotEmpty(list)) {
            model.setId(list.get(0).getId());
            this.updateById(model);
        } else {
            this.save(model);
        }
    }
}
