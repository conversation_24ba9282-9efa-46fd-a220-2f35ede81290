package com.concise.gen.outofcirclewarning.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.outofcirclewarning.entity.OutOfCircleWarning;
import com.concise.gen.outofcirclewarning.param.OutOfCircleWarningParam;
import java.util.List;

/**
 * 出圈预警信息service接口
 *
 * <AUTHOR>
 * @date 2023-07-21 17:39:55
 */
public interface OutOfCircleWarningService extends IService<OutOfCircleWarning> {

    /**
     * 查询出圈预警信息
     *
     * <AUTHOR>
     * @date 2023-07-21 17:39:55
     */
    PageResult<OutOfCircleWarning> page(OutOfCircleWarningParam outOfCircleWarningParam);

    /**
     * 出圈预警信息列表
     *
     * <AUTHOR>
     * @date 2023-07-21 17:39:55
     */
    List<OutOfCircleWarning> list(OutOfCircleWarningParam outOfCircleWarningParam);

    /**
     * 添加出圈预警信息
     *
     * <AUTHOR>
     * @date 2023-07-21 17:39:55
     */
    void add(OutOfCircleWarningParam outOfCircleWarningParam);

    /**
     * 删除出圈预警信息
     *
     * <AUTHOR>
     * @date 2023-07-21 17:39:55
     */
    void delete(OutOfCircleWarningParam outOfCircleWarningParam);

    /**
     * 编辑出圈预警信息
     *
     * <AUTHOR>
     * @date 2023-07-21 17:39:55
     */
    void edit(OutOfCircleWarningParam outOfCircleWarningParam);

    /**
     * 查看出圈预警信息
     *
     * <AUTHOR>
     * @date 2023-07-21 17:39:55
     */
     OutOfCircleWarning detail(OutOfCircleWarningParam outOfCircleWarningParam);


    /**
     * 导出
     * @param outOfCircleWarningParam outOfCircleWarningParam
     */
    void export(OutOfCircleWarningParam outOfCircleWarningParam);
}
