package com.concise.gen.papertopicitem.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.papertopicitem.entity.PaperTopicItem;
import com.concise.gen.papertopicitem.param.PaperTopicItemParam;
import java.util.List;

/**
 * 量卷维护-题目-指标选项service接口
 *
 * <AUTHOR>
 * @date 2025-03-20 15:03:05
 */
public interface PaperTopicItemService extends IService<PaperTopicItem> {

    /**
     * 查询量卷维护-题目-指标选项
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:05
     */
    PageResult<PaperTopicItem> page(PaperTopicItemParam paperTopicItemParam);

    /**
     * 量卷维护-题目-指标选项列表
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:05
     */
    List<PaperTopicItem> list(PaperTopicItemParam paperTopicItemParam);

    /**
     * 添加量卷维护-题目-指标选项
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:05
     */
    void add(PaperTopicItemParam paperTopicItemParam);

    /**
     * 删除量卷维护-题目-指标选项
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:05
     */
    void delete(PaperTopicItemParam paperTopicItemParam);

    /**
     * 编辑量卷维护-题目-指标选项
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:05
     */
    void edit(PaperTopicItemParam paperTopicItemParam);

    /**
     * 查看量卷维护-题目-指标选项
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:05
     */
     PaperTopicItem detail(PaperTopicItemParam paperTopicItemParam);
}
