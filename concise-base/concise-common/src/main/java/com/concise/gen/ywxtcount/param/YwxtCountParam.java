package com.concise.gen.ywxtcount.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 协同信息统计表参数类
 *
 * <AUTHOR>
 * @date 2024-01-23 14:08:03
*/
@Data
public class YwxtCountParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 数据类型
     */
    @NotBlank(message = "数据类型不能为空，请检查sjlx参数", groups = {add.class, edit.class})
    private String sjlx;

    /**
     * yyyy-MM-dd
     */
    @NotBlank(message = "yyyy-MM-dd不能为空，请检查jhrq参数", groups = {add.class, edit.class})
    private String jhrq;

    /**
     * 省/市/区/司法所
     */
    @NotBlank(message = "省/市/区/司法所不能为空，请检查deptId参数", groups = {add.class, edit.class})
    private String deptId;

    /**
     * 同步数量
     */
    @NotNull(message = "同步数量不能为空，请检查tbsl参数", groups = {add.class, edit.class})
    private Integer tbsl;

}
