package com.concise.gen.paperquestionitem.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;

/**
 * 题库-指标选项
 *
 * <AUTHOR>
 * @date 2025-03-20 15:03:00
 */
@Data
@TableName("paper_question_item")
public class PaperQuestionItem {

    /**  主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**  题库主键 */
    private String paperQuestionBankId;

    /**  指标选项内容 */
    private String content;

    /**  分数 */
    private Integer itemScore;

    /**  排序号 */
    private Integer serialNumber;

}
