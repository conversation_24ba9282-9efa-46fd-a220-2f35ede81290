package com.concise.gen.dataCenter.correctionterminate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.log.Log;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.dataCenter.correctionterminate.entity.CorrectionTerminate;
import com.concise.gen.dataCenter.correctionterminate.enums.CorrectionTerminateExceptionEnum;
import com.concise.gen.dataCenter.correctionterminate.mapper.CorrectionTerminateMapper;
import com.concise.gen.dataCenter.correctionterminate.param.CorrectionTerminateParam;
import com.concise.gen.dataCenter.correctionterminate.service.CorrectionTerminateService;
import com.concise.gen.correctionterminatext.service.CorrectionTerminateXtService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 矫正对象解矫列表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-06-17 15:09:52
 */
@Service
@DS("dataCenter")
public class CorrectionTerminateServiceImpl extends ServiceImpl<CorrectionTerminateMapper, CorrectionTerminate> implements CorrectionTerminateService {

    private static final Log log = Log.get();
    @Resource
    private CorrectionTerminateXtService correctionTerminateXtService;

    @Override
    public PageResult<CorrectionTerminate> page(CorrectionTerminateParam correctionTerminateParam) {
        QueryWrapper<CorrectionTerminate> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(correctionTerminateParam)) {

            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(correctionTerminateParam.getXm())) {
                queryWrapper.lambda().eq(CorrectionTerminate::getXm, correctionTerminateParam.getXm());
            }
            // 根据状态 查询
            if (ObjectUtil.isNotEmpty(correctionTerminateParam.getZhuangtai())) {
                queryWrapper.lambda().eq(CorrectionTerminate::getZhuangtai, correctionTerminateParam.getZhuangtai());
            }
            // 根据状态中文值 查询
            if (ObjectUtil.isNotEmpty(correctionTerminateParam.getZhuangtaiName())) {
                queryWrapper.lambda().eq(CorrectionTerminate::getZhuangtaiName, correctionTerminateParam.getZhuangtaiName());
            }
            // 根据终止日期 查询
            if (ObjectUtil.isNotEmpty(correctionTerminateParam.getZhongzhiriqi())) {
                queryWrapper.lambda().eq(CorrectionTerminate::getZhongzhiriqi, correctionTerminateParam.getZhongzhiriqi());
            }
            // 根据最后修改时间 查询
            if (ObjectUtil.isNotEmpty(correctionTerminateParam.getLastModifiedTime())) {
                queryWrapper.lambda().eq(CorrectionTerminate::getLastModifiedTime, correctionTerminateParam.getLastModifiedTime());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionTerminate> list(CorrectionTerminateParam correctionTerminateParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionTerminateParam correctionTerminateParam) {
        CorrectionTerminate correctionTerminate = new CorrectionTerminate();
        BeanUtil.copyProperties(correctionTerminateParam, correctionTerminate);
        this.save(correctionTerminate);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionTerminateParam correctionTerminateParam) {
        this.removeById(correctionTerminateParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionTerminateParam correctionTerminateParam) {
        CorrectionTerminate correctionTerminate = this.queryCorrectionTerminate(correctionTerminateParam);
        BeanUtil.copyProperties(correctionTerminateParam, correctionTerminate);
        this.updateById(correctionTerminate);
    }

    @Override
    public CorrectionTerminate detail(CorrectionTerminateParam correctionTerminateParam) {
        return this.queryCorrectionTerminate(correctionTerminateParam);
    }

    @Override
    public void addToXt(CorrectionTerminateParam correctionTerminateParam) {
        QueryWrapper<CorrectionTerminate> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(correctionTerminateParam.getZzrqs())) {
            queryWrapper.lambda().ge(CorrectionTerminate::getZhongzhiriqi, correctionTerminateParam.getZzrqs());
        }
        if (ObjectUtil.isNotEmpty(correctionTerminateParam.getZzrqe())) {
            queryWrapper.lambda().le(CorrectionTerminate::getZhongzhiriqi, correctionTerminateParam.getZzrqe());
        }
        queryWrapper.lambda().isNotNull(CorrectionTerminate::getZhongzhiriqi);
        Page<CorrectionTerminate> page = PageFactory.defaultPage();
        page.setSize(100);
        PageResult<CorrectionTerminate> pageResult = new PageResult<>(this.page(page, queryWrapper));
        while (pageResult.getTotalPage() >= pageResult.getPageNo()) {
            log.info(">>> addToXtPage:{} start",page.getCurrent());
            this.addCorrectionTerminateXt(pageResult.getRows());
            log.info(">>> addToXtPage:{} end",page.getCurrent());
            page.setCurrent(pageResult.getPageNo()+1);
            pageResult = new PageResult<>(this.page(page, queryWrapper));
        }
    }

    /**
     * 获取矫正对象解矫列表
     *
     * <AUTHOR>
     * @date 2022-06-17 15:09:52
     */
    private CorrectionTerminate queryCorrectionTerminate(CorrectionTerminateParam correctionTerminateParam) {
        CorrectionTerminate correctionTerminate = this.getById(correctionTerminateParam.getId());
        if (ObjectUtil.isNull(correctionTerminate)) {
            throw new ServiceException(CorrectionTerminateExceptionEnum.NOT_EXIST);
        }
        return correctionTerminate;
    }

    private void addCorrectionTerminateXt(List<CorrectionTerminate> correctionTerminates){
        correctionTerminates.forEach(correctionTerminate -> {
            log.info(">>> addToXt  name :{}",correctionTerminate.getXm());
            correctionTerminateXtService.add(correctionTerminate);
        });
    }
}
