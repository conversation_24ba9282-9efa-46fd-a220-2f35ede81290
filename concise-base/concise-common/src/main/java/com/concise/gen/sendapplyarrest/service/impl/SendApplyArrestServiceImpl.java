package com.concise.gen.sendapplyarrest.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptcorrectiondoc.service.AcceptCorrectionDocService;
import com.concise.gen.dataCenter.arrest.entity.ArrestDc;
import com.concise.gen.dataCenter.arrest.service.ArrestDcService;
import com.concise.gen.dataCenter.correctionban.entity.CorrectionBanDc;
import com.concise.gen.dataCenter.correctionban.service.CorrectionBanDcService;
import com.concise.gen.dataCenter.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.dataCenter.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.gen.extorginfo.service.ExtOrgInfoService;
import com.concise.gen.notice.enums.YwxtNoticeTypeEnum;
import com.concise.gen.notice.service.YwxtNoticeService;
import com.concise.gen.sendapplyarrest.entity.SendApplyArrest;
import com.concise.gen.sendapplyarrest.enums.SendApplyArrestExceptionEnum;
import com.concise.gen.sendapplyarrest.mapper.SendApplyArrestMapper;
import com.concise.gen.sendapplyarrest.param.SendApplyArrestParam;
import com.concise.gen.sendapplyarrest.service.SendApplyArrestService;
import com.concise.gen.sysdistinfo.service.SysDistinfoService;
import com.concise.gen.sysorg.entity.OrgCommon;
import com.concise.gen.sysorg.service.OrgCommonService;
import com.concise.gen.webservice.service.YwxtSendService;
import com.concise.gen.webservice.utils.DictWdToHyUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发送提请逮捕service接口实现类
 *
 * <AUTHOR>
 * @date 2023-09-20 14:47:19
 */
@Service
public class SendApplyArrestServiceImpl extends ServiceImpl<SendApplyArrestMapper, SendApplyArrest> implements SendApplyArrestService {

    @Resource
    private ArrestDcService arrestDcService;
    @Resource
    private CorrectionObjectInformationService objInfoDcService;
    @Resource
    private CorrectionBanDcService correctionBanDcService;
    @Resource
    private SysDistinfoService distInfoService;
    @Resource
    private ExtOrgInfoService extOrgInfoService;
    @Resource
    private AcceptCorrectionDocService acceptCorrectionDocService;
    @Resource
    private OrgCommonService orgCommonService;
    @Resource
    private YwxtSendService sendService;
    @Resource
    private YwxtNoticeService ywxtNoticeService;
    @Override
    public PageResult<SendApplyArrest> page(SendApplyArrestParam param) {
        QueryWrapper<SendApplyArrest> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据状态 查询
            if (ObjectUtil.isNotEmpty(param.getZt())) {
                queryWrapper.lambda().eq(SendApplyArrest::getZt, param.getZt());
            }
            // 根据矫正单位id 查询
            if (ObjectUtil.isNotEmpty(param.getJzjgId())) {
                queryWrapper.lambda().and(i -> i.eq(SendApplyArrest::getJzjgId, param.getJzjgId()).or().like(SendApplyArrest::getJzjgPids, param.getJzjgId()));
            }
            // 发送时间
            if (ObjectUtil.isNotEmpty(param.getSearchBeginTime())) {
                queryWrapper.lambda().ge(SendApplyArrest::getFssj, param.getSearchBeginTime());
            }
            if (ObjectUtil.isNotEmpty(param.getSearchEndTime())) {
                queryWrapper.lambda().le(SendApplyArrest::getFssj, param.getSearchEndTime());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(SendApplyArrest::getXm, param.getXm().trim());
            }
        }
        queryWrapper.lambda().orderByDesc(SendApplyArrest::getFssj);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<SendApplyArrest> list(SendApplyArrestParam sendApplyArrestParam) {
        return this.list();
    }

    @Override
    public void add(SendApplyArrestParam sendApplyArrestParam) {
        SendApplyArrest sendApplyArrest = new SendApplyArrest();
        BeanUtil.copyProperties(sendApplyArrestParam, sendApplyArrest);
        this.save(sendApplyArrest);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(SendApplyArrestParam sendApplyArrestParam) {
        this.removeById(sendApplyArrestParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(SendApplyArrestParam sendApplyArrestParam) {
        SendApplyArrest sendApplyArrest = this.querySendApplyArrest(sendApplyArrestParam);
        BeanUtil.copyProperties(sendApplyArrestParam, sendApplyArrest);
        this.updateById(sendApplyArrest);
        //System.out.println(sendService.xTbd1(sendApplyArrest));
    }

    @Override
    public SendApplyArrest detail(SendApplyArrestParam sendApplyArrestParam) {
        return this.querySendApplyArrest(sendApplyArrestParam);
    }

    /**
     * 获取发送提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-20 14:47:19
     */
    private SendApplyArrest querySendApplyArrest(SendApplyArrestParam sendApplyArrestParam) {
        SendApplyArrest sendApplyArrest = this.getById(sendApplyArrestParam.getId());
        if (ObjectUtil.isNull(sendApplyArrest)) {
            throw new ServiceException(SendApplyArrestExceptionEnum.NOT_EXIST);
        }
        return sendApplyArrest;
    }

    @Override
    public void add(String id){
        if ("all".equals(id)) {
            LambdaQueryWrapper<ArrestDc> queryWrapper = new LambdaQueryWrapper<>();
            Page<ArrestDc> page = new Page<>(1, 50);
            PageResult<ArrestDc> pageResult = new PageResult<>(arrestDcService.page(page, queryWrapper));
            System.out.println(pageResult.getTotalPage());
            while (pageResult.getTotalPage() >= pageResult.getPageNo()) {
                System.out.println(pageResult.getPageNo());
                pageResult.getRows().forEach(this::add);
                page.setCurrent(pageResult.getPageNo()+1);
                pageResult = new PageResult<>(arrestDcService.page(page, queryWrapper));
            }
        }else if (ObjectUtil.isNotEmpty(id)) {
            ArrestDc dc = arrestDcService.getById(id);
            if (dc == null) {
                return;
            }
            add(dc);
        }
    }

    @Override
    public void add(ArrestDc dc) {
        CorrectionObjectInformation obj = objInfoDcService.getById(dc.getPid());
        if (obj == null) {
            return;
        }
        SendApplyArrest applyArrest = new SendApplyArrest();
        applyArrest.setZt("0");
        applyArrest.setId(dc.getId());
        applyArrest.setTyfh(dc.getId());
        applyArrest.setSqjzajbh(dc.getPid());
        applyArrest.setZfbh(dc.getPid());
        applyArrest.setXm(dc.getPname());
        applyArrest.setXb(obj.getXb());
        applyArrest.setZjlx("111");
        applyArrest.setZjhm(obj.getSfzh());
        applyArrest.setCsrq(obj.getCsrq());
        applyArrest.setHjd(distInfoService.getAreaCode(obj.getHjszxq()));
        applyArrest.setHjdxz(obj.getHjszdmx());
        applyArrest.setXzd(distInfoService.getAreaCode(obj.getGdjzdszxq()));
        applyArrest.setXzdxz(obj.getGdjzdmx());

        applyArrest.setSxpjjg(obj.getSxpjjg());
        applyArrest.setPjwswh(obj.getPjszh());
        applyArrest.setPjrq(obj.getPjrq());
        applyArrest.setPjzm(obj.getZmName());
        applyArrest.setPjqtzm(obj.getPjqtzm());
        applyArrest.setYpxf(obj.getZx());
        applyArrest.setFjx(obj.getFjx());

        List<CorrectionBanDc> ban = correctionBanDcService.lambdaQuery().eq(CorrectionBanDc::getPid, dc.getPid()).list();
        if (ban.size()>0) {
            applyArrest.setJzlnr(ban.get(0).getJzllxName());
            applyArrest.setJzqxqr(ban.get(0).getJzqxksrq());
            applyArrest.setJzqxzr(ban.get(0).getJzqxjsrq());
        }

        applyArrest.setJzjgId(obj.getJzjg());
        OrgCommon org = orgCommonService.getById(obj.getJzjg());
        applyArrest.setJzjgCode(org.getCode());
        applyArrest.setJzjgPids(org.getPids());
        applyArrest.setJzlb(DictWdToHyUtils.jzlb(obj.getJzlb()));
        applyArrest.setJdjg(extOrgInfoService.getExtOrgCode(obj.getJdjglx() + obj.getJdjgmc()));
        applyArrest.setSfs(obj.getJzjgName());
        applyArrest.setSqjzksrq(obj.getSqjzksrq());
        applyArrest.setSqjzjsrq(obj.getSqjzjsrq());
        applyArrest.setJzqx(obj.getSqjzqx());
        applyArrest.setSqjzzxd(applyArrest.getXzd());

        applyArrest.setSyjyj(dc.getTqly());
        applyArrest.setTqrq(dc.getSfssqsj());

        this.saveOrUpdate(applyArrest);
    }

    @Override
    public List<AcceptCorrectionDocParam> updateByTyfh(SendApplyArrestParam param, String step) {
        SendApplyArrest data = this.lambdaQuery().eq(SendApplyArrest::getTyfh, param.getTyfh())
                .last("limit 1")
                .one();
        if (data == null) {
            System.out.println("tyfh: "+param.getTyfh());
            return null;
        }
        param.setId(data.getId());
        param.setZt(step);
        BeanUtil.copyProperties(param, data);
        this.updateById(data);
        param.getDocList().forEach(doc ->acceptCorrectionDocService.add(doc));

        OrgCommon org = orgCommonService.getById(data.getJzjgId());
        if ("2".equals(step)) {
            ywxtNoticeService.buildNoticeByOrgId(
                    YwxtNoticeTypeEnum.ARREST_01,
                    org.getName()+","+ DateUtil.today() +","+param.getXtfyName()+","+param.getXm(),
                    param.getXtfyName()+","+param.getXm(),
                    org.getId(),org.getPid());
        }
        if ("4".equals(step)) {
            ywxtNoticeService.buildNoticeByOrgId(
                    YwxtNoticeTypeEnum.ARREST_02,
                    org.getName()+","+ DateUtil.today() +","+param.getXtfyName()+","+param.getXm(),
                    param.getXtfyName()+","+param.getXm(),
                    org.getId(),org.getPid());
        }

        return param.getDocList();
    }
}
