package com.concise.gen.ywxtwslog.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* ws接口获取日志参数类
 *
 * <AUTHOR>
 * @date 2023-05-11 14:49:29
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class YwxtWsLogParam extends BaseParam {

    /**
     * 主键
     */
    private Long id;

    /**
     * 操作时间
     */
    private String opTime;

    /**
     * 类别
     */
    private String typeName;
    private String taskId;
    private String dataId;

    /**
     * 矫正对象
     */
    private String correctionObject;

    /**
     * 矫正对象id
     */
    private String correctionObjectId;

    /**
     * 解码后的xml
     */
    private String decodeXml;
    private String jsdw;

}
