package com.concise.gen.placechange.param;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 迁出导出
 *
 * <AUTHOR>
 * @date 2025-04-01 11:09:06
 */
@Data
public class EmiExport {

    @Excel(name = "状态", replace = {"待外省调查_1","待移送档案_2","待人员报到入矫_3","已完结_4"}, width = 30)
    private String processStatus;
    @Excel(name = "迁出结果", replace = {"迁入申请不通过_1","迁入单位同意_2","迁入单位同意且人员已入矫_3","_null"}, width = 30)
    private String receiveStatus;
    @Excel(name = "姓名", width = 20)
    private String correctionObjName;
    @Excel(name = "矫正单位", width = 40)
    private String deptName;
    @Excel(name = "迁出原因", width = 40)
    private String changeReason;
    @Excel(name = "迁入省（市）", width = 20)
    private String destinationProvName;
    @Excel(name = "迁入单位", width = 40)
    private String extDeptName;
    @Excel(name = "报到时间", width = 20)
    private String correctionStartAt;

}
