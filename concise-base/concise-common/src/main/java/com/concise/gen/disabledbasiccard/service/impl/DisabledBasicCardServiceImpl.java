package com.concise.gen.disabledbasiccard.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.disabledbasiccard.entity.DisabledBasicCard;
import com.concise.gen.disabledbasiccard.enums.DisabledBasicCardExceptionEnum;
import com.concise.gen.disabledbasiccard.mapper.DisabledBasicCardMapper;
import com.concise.gen.disabledbasiccard.param.DisabledBasicCardParam;
import com.concise.gen.disabledbasiccard.service.DisabledBasicCardService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * 残疾人信息service接口实现类
 *
 * <AUTHOR>
 * @date 2024-01-09 11:23:58
 */
@DS("dataCenter")
@Service
public class DisabledBasicCardServiceImpl extends ServiceImpl<DisabledBasicCardMapper, DisabledBasicCard> implements DisabledBasicCardService {

    @Override
    public PageResult<DisabledBasicCard> page(DisabledBasicCardParam disabledBasicCardParam, Set<String> orgSet) {
        QueryWrapper<DisabledBasicCard> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(DisabledBasicCard::getUpdateTime);
        if (CollectionUtil.isNotEmpty(orgSet)) {
            queryWrapper.in("disabled_basic_card.jzjg", orgSet);
        }
        if (ObjectUtil.isNotNull(disabledBasicCardParam)) {

            // 根据身份证号 查询
            if (ObjectUtil.isNotEmpty(disabledBasicCardParam.getIdentityCard())) {
                queryWrapper.lambda().like(DisabledBasicCard::getIdentityCard, disabledBasicCardParam.getIdentityCard());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(disabledBasicCardParam.getName())) {
                queryWrapper.lambda().like(DisabledBasicCard::getName, disabledBasicCardParam.getName());
            }
            // 根据残疾类型 查询
            if (ObjectUtil.isNotEmpty(disabledBasicCardParam.getDisableType())) {
                queryWrapper.lambda().eq(DisabledBasicCard::getDisableType, disabledBasicCardParam.getDisableType());
            }
            // 根据残疾人证号 查询
            if (ObjectUtil.isNotEmpty(disabledBasicCardParam.getDisableCardNum())) {
                queryWrapper.lambda().eq(DisabledBasicCard::getDisableCardNum, disabledBasicCardParam.getDisableCardNum());
            }
            // 根据残疾等级 查询
            if (ObjectUtil.isNotEmpty(disabledBasicCardParam.getDisableLevel())) {
                queryWrapper.lambda().eq(DisabledBasicCard::getDisableLevel, disabledBasicCardParam.getDisableLevel());
            }
            // 根据发证日期 查询
            if (ObjectUtil.isNotEmpty(disabledBasicCardParam.getIssueDate())) {
                queryWrapper.lambda().eq(DisabledBasicCard::getIssueDate, disabledBasicCardParam.getIssueDate());
            }
            // 根据有效期开始 查询
            if (ObjectUtil.isNotEmpty(disabledBasicCardParam.getStime())) {
                queryWrapper.lambda().eq(DisabledBasicCard::getStime, disabledBasicCardParam.getStime());
            }
            // 根据有效期结束 查询
            if (ObjectUtil.isNotEmpty(disabledBasicCardParam.getEtime())) {
                queryWrapper.lambda().eq(DisabledBasicCard::getEtime, disabledBasicCardParam.getEtime());
            }
            // 根据注销时间 查询
            if (ObjectUtil.isNotEmpty(disabledBasicCardParam.getZxtime())) {
                queryWrapper.lambda().eq(DisabledBasicCard::getZxtime, disabledBasicCardParam.getZxtime());
            }
            // 根据矫正对象id 查询
            if (ObjectUtil.isNotEmpty(disabledBasicCardParam.getJzdxId())) {
                queryWrapper.lambda().eq(DisabledBasicCard::getJzdxId, disabledBasicCardParam.getJzdxId());
            }
            // 根据矫正机构 查询
            if (ObjectUtil.isNotEmpty(disabledBasicCardParam.getJzjgName())) {
                queryWrapper.lambda().eq(DisabledBasicCard::getJzjgName, disabledBasicCardParam.getJzjgName());
            }
            if (ObjectUtil.isNotEmpty(disabledBasicCardParam.getZhuangtai())) {
                if ("0".equals(disabledBasicCardParam.getZhuangtai())) {
                    queryWrapper.eq("correction_object_information.zhuangtai", "200");
                } else {
                    queryWrapper.ne("correction_object_information.zhuangtai", "200");
                }
            } else {
                queryWrapper.eq("correction_object_information.zhuangtai", "200");
            }
        } else {
            queryWrapper.eq("correction_object_information.zhuangtai", "200");
        }
        return new PageResult<>(this.baseMapper.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<DisabledBasicCard> list(DisabledBasicCardParam disabledBasicCardParam) {
        return this.list();
    }

    @Override
    public void add(DisabledBasicCardParam disabledBasicCardParam) {
        DisabledBasicCard disabledBasicCard = new DisabledBasicCard();
        BeanUtil.copyProperties(disabledBasicCardParam, disabledBasicCard);
        this.save(disabledBasicCard);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(DisabledBasicCardParam disabledBasicCardParam) {
        this.removeById(disabledBasicCardParam.getPkId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(DisabledBasicCardParam disabledBasicCardParam) {
        DisabledBasicCard disabledBasicCard = this.queryDisabledBasicCard(disabledBasicCardParam);
        BeanUtil.copyProperties(disabledBasicCardParam, disabledBasicCard);
        this.updateById(disabledBasicCard);
    }

    @Override
    public DisabledBasicCard detail(DisabledBasicCardParam disabledBasicCardParam) {
        return this.queryDisabledBasicCard(disabledBasicCardParam);
    }

    /**
     * 获取残疾人信息
     *
     * <AUTHOR>
     * @date 2024-01-09 11:23:58
     */
    private DisabledBasicCard queryDisabledBasicCard(DisabledBasicCardParam disabledBasicCardParam) {
        DisabledBasicCard disabledBasicCard = this.getById(disabledBasicCardParam.getPkId());
        if (ObjectUtil.isNull(disabledBasicCard)) {
            throw new ServiceException(DisabledBasicCardExceptionEnum.NOT_EXIST);
        }
        return disabledBasicCard;
    }
}
