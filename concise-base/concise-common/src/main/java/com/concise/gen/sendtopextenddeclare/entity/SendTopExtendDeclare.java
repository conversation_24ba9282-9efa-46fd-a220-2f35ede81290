package com.concise.gen.sendtopextenddeclare.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.file.param.SysFileInfoVO;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 发送暂外续报
 *
 * <AUTHOR>
 * @date 2023-05-25 17:23:48
 */
@Data
@TableName("send_top_extend_declare")
public class SendTopExtendDeclare{

    /**
     * 协同使用的发送单位信息(区县司法局)
     */
    @TableField(exist = false)
    private String fsdw;

    private String zt;
    private String jcyCode;
    private String fyCode;
    @TableField(exist = false)
    private List<AcceptCorrectionDoc> docList;
    @TableField(exist = false)
    private List<SysFileInfoVO> fileList;
    @TableField(exist = false)
    private List<SysFileInfoVO> fileList2;
    @TableField(exist = false)
    private List<SysFileInfoVO> fileList3;
    private String file1;
    private String file2;
    private String taskId4053;
    private String taskId4054;
    private String taskId4055;
    private String taskId4056;
    private Date jcyfksj;
    private Date fyfksj;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 统一赋号
     */
    private String tyfh;

    /**
     * 社区矫正案件编号
     */
    private String sqjzajbh;

    /**
     * 公安嫌疑人编号
     */
    private String gaxyrbh;

    /**
     * 检察院嫌疑人编号
     */
    private String jcyxyrbh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 矫正机构code
     */
    private String jzjg;
    private String jzjgName;
    private String jzjgId;
    private String jzjgPids;


    /**
     * 曾用名
     */
    private String cym;

    /**
     * 性别
     */
    private String xb;

    /**
     * 证件类型
     */
    private String zjlx;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 出生日期
     */

    private Date csrq;

    /**
     * 作案时年龄
     */
    private String zasnl;

    /**
     * 国籍
     */
    private String gj;

    /**
     * 民族
     */
    private String mz;

    /**
     * 出生地
     */
    private String csd;

    /**
     * 户籍地
     */
    private String hjd;

    /**
     * 户籍地详址
     */
    private String hjdxz;

    /**
     * 现住地
     */
    private String xzd;

    /**
     * 现住地详址
     */
    private String xzdxz;

    /**
     * 文化程度
     */
    private String whcd;

    /**
     * 生效判决机关
     */
    private String sxpjjg;

    /**
     * 判决文书文号
     */
    private String pjwswh;

    /**
     * 判决日期
     */

    private Date pjrq;

    /**
     * 判决罪名
     */
    private String pjzm;

    /**
     * 判决其他罪名
     */
    private String pjqtzm;

    /**
     * 主刑
     */
    private String zx;

    /**
     * 原判刑期
     */
    private String ypxq;

    /**
     * 原刑期起日
     */

    private Date yxqqr;

    /**
     * 原刑期止日
     */

    private Date yxqzr;

    /**
     * 附加刑
     */
    private String fjx;

    /**
     * 附加刑具体情况
     */
    private String fjxjtqk;

    /**
     * 缓刑考验期
     */
    private String hxkyq;

    /**
     * 财产性判项
     */
    private String ccxpx;

    /**
     * 罚金金额（万元）
     */
    private String fjjewy;

    /**
     * 没收财产金额（万元）
     */
    private String msccjewy;

    /**
     * 其他财产性判项金额（万元）
     */
    private String qtccxpxjewy;

    /**
     * 剥夺政治权利期限
     */
    private String bdzzqlqx;

    /**
     * 决定书文号
     */
    private String jdswh;

    /**
     * 决定暂予监外执行日期
     */

    private Date jdzyjwzxrq;

    /**
     * 暂予监外执行决定机关
     */
    private String zyjwzxjdjg;

    /**
     * 暂予监外执行原因
     */
    private String zyjwzxyy;


    /**
     * 交付执行日期
     */

    private Date jfzxrq;

    /**
     * 移交罪犯机关类型
     */
    private String yjzfjglx;

    /**
     * 移交罪犯机关名称
     */
    private String yjzfjgmc;

    /**
     * 矫正期限
     */
    private String jzqx;

    /**
     * 暂予监外执行起日
     */

    private Date zyjwzxqr;

    /**
     * 暂予监外执行止日
     */

    private Date zyjwzxzr;

    /**
     * 入矫日期
     */

    private Date rjrq;

    /**
     * 奖惩情况
     */
    private String jcqk;

    /**
     * 在矫期间改造表现
     */
    private String zjqjgzbx;

    /**
     * 鉴定医院
     */
    private String jdyy;

    /**
     * 鉴定日期
     */

    private Date jdrq;

    /**
     * 诊断结果
     */
    private String zdjg;

    /**
     * 申请人姓名
     */
    private String sqrxm;

    /**
     * 与罪犯关系(文本)
     */
    private String yzfgx;

    /**
     * 续报申请事由
     */
    private String xbsqsy;

    /**
     * 申请日期
     */

    private Date sqrq;

    /**
     * 检察院部门受案号
     */
    private String jcybmsah;

    /**
     * 审查文书文号
     */
    private String scwswh;

    /**
     * 暂予监外执行检察意见
     */
    private String zyjwzxjcyj;

    /**
     * 意见反馈时间
     */

    private Date yjfksj;

    /**
     * 检察意见备注
     */
    private String jcyjbz;

    /**
     * 文书文号
     */
    private String wswh;

    /**
     * 签收日期
     */

    private Date qsrq;

    /**
     * 送达文书内容
     */
    private String sdwsnr;

    /**
     * 签收单位
     */
    private String qsdw;

    /**
     * 签收人
     */
    private String qsr;

    /**
     * 回执文书号
     */
    private String hzwsh;

}
