package com.concise.gen.dataCenter.orgDc.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.dataCenter.orgDc.entity.OrgDc;
import com.concise.gen.dataCenter.orgDc.vo.CcgfUser;

import java.util.List;

/**
 * 数据中心机构表
 *
 * <AUTHOR>
 * @date 2023-11-23 16:15:33
 */
@DS("dataCenter")
public interface OrgDcMapper extends BaseMapper<OrgDc> {

    /**
     * 获取浙政钉通知用id
     * @param orgId 所属单位
     * @return GE_xxxx
     */
    List<CcgfUser> getZwddIds(String orgId);
}
