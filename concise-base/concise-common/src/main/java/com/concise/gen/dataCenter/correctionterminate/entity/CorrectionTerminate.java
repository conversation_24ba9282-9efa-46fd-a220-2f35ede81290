package com.concise.gen.dataCenter.correctionterminate.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 矫正对象解矫列表
 *
 * <AUTHOR>
 * @date 2022-06-17 15:09:52
 */
@Data
@TableName("correction_terminate")
public class CorrectionTerminate {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 状态
     */
    private String zhuangtai;

    /**
     * 状态中文值
     */
    private String zhuangtaiName;

    /**
     * 终止日期
     */
    @Excel(name = "终止日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date zhongzhiriqi;

    /**
     * 最后修改时间
     */
    @Excel(name = "最后修改时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date lastModifiedTime;

    /**
     * 收监执行原因
     */
    private String sjzxyy;
    /**
     * 收监执行原因中文值
     */
    private String sjzxyyName;
}
