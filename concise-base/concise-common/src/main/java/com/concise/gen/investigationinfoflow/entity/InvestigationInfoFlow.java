package com.concise.gen.investigationinfoflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.file.param.SysFileInfoVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 调查评估-流程记录
 *
 * <AUTHOR>
 * @date 2025-03-25 09:02:58
 */
@Data
@NoArgsConstructor // 自动生成无参构造方法
@TableName("investigation_info_flow")
public class InvestigationInfoFlow {

    /**  主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**  调查评估主键 */
    private String investigationInfoId;

    /**  外层节点code, 1: 接收委托建档 2: 调查评估 3: 评估审批阶段 4: 反馈结果阶段 */
    private Integer nodeLevel;

    /**  类型：0：退回 1：通过 2：流程终止 9: 未开始 */
    private Integer type;

    /**  流程节点(调查评估状态的字典值:PGZT) */
    private String stepCode;

    /**  流程节点名称 */
    private String stepName;

    /**  机构id */
    private String deptId;

    /**  机构名称 */
    private String deptName;

    /**  用户id */
    private String userId;

    /**  用户名称 */
    private String userName;

    /**  意见/原因 */
    private String msg;

    /**  公告/委托时间 */
    private Date wtTime;

    /**  公告/委托单位id */
    private String gwDeptId;

    /**  公告/委托单位名称 */
    private String gwDeptName;

    /**  审批时间 */
    private Date spTime;

    /**  是否暂存, 0: 否 1：是 */
    private Integer draft;

    /**  表单信息 */
    private String formVal;

    /**  审批时间 */
    @TableField(exist = false)
    private List<SysFileInfoVO> fileList;

    public InvestigationInfoFlow(String investigationInfoId, Integer nodeLevel, Integer type, String stepCode, String stepName, Date spTime) {
        this.investigationInfoId = investigationInfoId;
        this.nodeLevel = nodeLevel;
        this.type = type;
        this.stepCode = stepCode;
        this.stepName = stepName;
        this.spTime = spTime;
    }
}
