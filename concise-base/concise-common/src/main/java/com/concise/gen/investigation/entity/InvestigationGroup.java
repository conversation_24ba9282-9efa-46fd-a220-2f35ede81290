package com.concise.gen.investigation.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.file.param.SysFileInfoVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 调查评估_小组调查
 *
 * <AUTHOR>
 * @date 2025-03-20 10:49:49
 */
@Data
@TableName("investigation_p1_group")
public class InvestigationGroup{

    /**  主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**  调查对象姓名 */
    private String correctionObjName;

    /**  调查机构 */
    private String inveDept;

    /**  调查小组名称 */
    private String inveGroupName;

    /**  调查人员 */
    private String invePsnList;

    /**  公告标题 */
    private String noticeTitle;

    /**  公告时间 */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date noticeTime;

    /**  公告文书号 (2025)杭余矫调评字第001号 */
    private String noticeDocNum;

    /**
     * 公告文书号1  (2025)杭余矫调评字第
     */
    @TableField(exist = false)
    private String noticeDocNumOne;

    /**
     * 公告文书号2   001
     */
    @TableField(exist = false)
    private String noticeDocNumTwo;

    /**
     * 公告文书号3   号
     */
    @TableField(exist = false)
    private String noticeDocNumThree;

    /**  公告正文 */
    private String noticeContext;

    /**  办理人 */
    private String opPsn;

    /**  收到委托时间 */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date entrustmentReceiveTime;

    /**  调查截止时间 */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date inveTimeLimit;

    /**  审核结果 */
    private String approvalResult;

    /**  审核备注 */
    private String approvalRemark;

    /**  审核人 */
    private String approvalPsn;

    /**  审核时间 */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    /**  退回意见 */
    private String returnRemark;
    /**
     * 评估得分
     * **/
    private String totalScore;

    /**  调查评估日期 */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date inveTime;

    /**  调查评估结论 */
    private String conclusion;

    /**  调查评估意见 */
    private String opinion;

    /**  是否删除 */
    private Integer deleted;
    /**
     * 小组公告 */
    @TableField(exist = false)
    private List<SysFileInfoVO> noticeDocList;

    /**
     * 管理评估清单-其它调查材料 */
    @TableField(exist = false)
    private List<SysFileInfoVO> pgqdFileList;

    /**
     * 调查提交-其它调查材料 */
    @TableField(exist = false)
    private List<SysFileInfoVO> dctjFileList;

    /**  流程流转提醒人员 */
    private String msgPsnList;

    /**  查看和提交评估结果-评估表单信息 */
    private String pgForm;
}
