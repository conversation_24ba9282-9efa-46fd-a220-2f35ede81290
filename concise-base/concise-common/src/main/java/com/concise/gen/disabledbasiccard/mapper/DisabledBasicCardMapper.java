package com.concise.gen.disabledbasiccard.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.concise.gen.disabledbasiccard.entity.DisabledBasicCard;
import org.apache.ibatis.annotations.Param;

/**
 * 残疾人信息
 *
 * <AUTHOR>
 * @date 2024-01-09 11:23:58
 */
public interface DisabledBasicCardMapper extends BaseMapper<DisabledBasicCard> {
    Page<DisabledBasicCard> page(@Param("page") Page page, @Param("ew") QueryWrapper queryWrapper);

}
