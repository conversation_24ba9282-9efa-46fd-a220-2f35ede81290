package com.concise.gen.petitionletterinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.petitionletterinfo.entity.PetitionLetterInfo;
import com.concise.gen.petitionletterinfo.param.PetitionLetterInfoParam;
import java.util.List;

/**
 * 信访信息service接口
 *
 * <AUTHOR>
 * @date 2024-04-02 14:11:20
 */
public interface PetitionLetterInfoService extends IService<PetitionLetterInfo> {

    /**
     * 查询信访信息
     *
     * <AUTHOR>
     * @date 2024-04-02 14:11:20
     */
    PageResult<PetitionLetterInfo> page(PetitionLetterInfoParam petitionLetterInfoParam);

    /**
     * 信访信息列表
     *
     * <AUTHOR>
     * @date 2024-04-02 14:11:20
     */
    List<PetitionLetterInfo> list(PetitionLetterInfoParam petitionLetterInfoParam);
    void export(PetitionLetterInfoParam petitionLetterInfoParam);
    void saveOrUpdateByLetterNo(PetitionLetterInfo model);
}
