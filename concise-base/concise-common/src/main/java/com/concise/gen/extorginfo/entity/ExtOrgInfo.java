package com.concise.gen.extorginfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 外部单位信息
 *
 * <AUTHOR>
 * @date 2022-06-01 10:56:17
 */
@Data
@TableName("ext_org_info")
public class ExtOrgInfo {

    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Integer id;

    /**
     * 单位代码
     */
    private String orgCode;

    /**
     * 单位名称
     */
    private String orgName;

    /**
     * 类别
     * OrgTypeEnum
     */
    private Integer type;
    private String dist;
    private String orgLevel;

}
