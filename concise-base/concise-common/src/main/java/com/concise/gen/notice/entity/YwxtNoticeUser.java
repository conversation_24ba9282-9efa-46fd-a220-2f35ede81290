package com.concise.gen.notice.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 协同信息通知_用户表
 *
 * <AUTHOR>
 * @date 2024-03-28 13:41:14
 */
@Data
@TableName("ywxt_notice_user")
public class YwxtNoticeUser{

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * ywxt_notice.id
     */
    private String noticeId;
    /**
     * 查询关联冗余
     */
    private String noticeType;
    @Excel(name = "通知事项", width = 20,orderNum = "2")
    private String noticeTypeName;

    /**
     * 用户id
     */
    private String userId;
    @Excel(name = "姓名", width = 20)
    private String userName;
    private String orgIds;
    @Excel(name = "所属单位", width = 20,orderNum = "1")
    private String orgName;

    /**
     * 是否发送
     */
    private Boolean send;
    @Excel(name = "发送状态", width = 20,orderNum = "4")
    @TableField(exist = false)
    private Boolean sendStr;

    /**
     * 发送时间
     */
    @Excel(name = "通知时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd HH:mm:ss", width = 20,orderNum = "3")
    private Date sendTime;

    private String msg;

    public String getSendStr(){
        return send ? "已发送" : "未发送";
    }
}
