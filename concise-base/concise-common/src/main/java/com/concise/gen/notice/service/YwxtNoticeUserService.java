package com.concise.gen.notice.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.notice.entity.YwxtNoticeUser;
import com.concise.gen.notice.param.YwxtNoticeUserParam;

/**
 * 协同信息通知_用户表service接口
 *
 * <AUTHOR>
 * @date 2024-03-28 13:41:14
 */
public interface YwxtNoticeUserService extends IService<YwxtNoticeUser> {

    /**
     * 查询协同信息通知_用户表
     *
     * <AUTHOR>
     * @date 2024-03-28 13:41:14
     */
    PageResult<YwxtNoticeUser> page(YwxtNoticeUserParam ywxtNoticeUserParam);

    void export(YwxtNoticeUserParam ywxtNoticeUserParam);
}
