package com.concise.gen.ywxtsendlog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.gen.ywxtsendlog.entity.YwxtSendLog;

/**
 * ws发送日志service接口
 *
 * <AUTHOR>
 * @date 2024-11-18 13:22:13
 */
public interface YwxtSendLogService extends IService<YwxtSendLog> {
    /**
     * 新增
     * @param typeName typeName
     * @param correctionObject correctionObject
     * @param deptName deptName
     * @param dataId dataId
     * @param sendOrgName sendOrgName
     * @param sendOrgId sendOrgId
     * @param sendOrgPids sendOrgPids
     * @param receiveOrgName receiveOrgName
     * @param receiveOrgType receiveOrgType
     * @return taskId
     */
    String add(String typeName,
               String correctionObject,
               String deptName,
               String dataId,
               String sendOrgName,
               String sendOrgId,
               String sendOrgPids,
               String receiveOrgName,
               String receiveOrgType);

    /**
     * 发送失败
     * @param taskId
     */
    void fail(String taskId);
}
