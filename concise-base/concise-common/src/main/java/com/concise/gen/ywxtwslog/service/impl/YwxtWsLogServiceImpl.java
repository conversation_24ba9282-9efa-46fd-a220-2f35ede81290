package com.concise.gen.ywxtwslog.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ywxtwslog.entity.YwxtWsLog;
import com.concise.gen.ywxtwslog.mapper.YwxtWsLogMapper;
import com.concise.gen.ywxtwslog.param.YwxtWsLogParam;
import com.concise.gen.ywxtwslog.service.YwxtWsLogService;
import org.springframework.stereotype.Service;

/**
 * ws接口获取日志service接口实现类
 *
 * <AUTHOR>
 * @date 2023-05-11 14:49:29
 */
@Service
public class YwxtWsLogServiceImpl extends ServiceImpl<YwxtWsLogMapper, YwxtWsLog> implements YwxtWsLogService {

    @Override
    public PageResult<YwxtWsLog> page(YwxtWsLogParam ywxtWsLogParam) {
        QueryWrapper<YwxtWsLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ne(YwxtWsLog::getSuccess, 1);
        if (ObjectUtil.isNotNull(ywxtWsLogParam)) {
            if (ObjectUtil.isNotEmpty(ywxtWsLogParam.getJsdw())) {
                queryWrapper.lambda().likeRight(YwxtWsLog::getJsdw, ywxtWsLogParam.getJsdw());
            }
            if (ObjectUtil.isNotEmpty(ywxtWsLogParam.getDataId())) {
                queryWrapper.lambda().eq(YwxtWsLog::getDataId, ywxtWsLogParam.getDataId());
            }
            if (ObjectUtil.isNotEmpty(ywxtWsLogParam.getTaskId())) {
                queryWrapper.lambda().eq(YwxtWsLog::getTaskId, ywxtWsLogParam.getTaskId());
            }
            if (ObjectUtil.isNotEmpty(ywxtWsLogParam.getTypeName())) {
                queryWrapper.lambda().eq(YwxtWsLog::getTypeName, ywxtWsLogParam.getTypeName());
            }
            if (ObjectUtil.isNotEmpty(ywxtWsLogParam.getCorrectionObject())) {
                queryWrapper.lambda().like(YwxtWsLog::getCorrectionObject, ywxtWsLogParam.getCorrectionObject());
            }
        }
        queryWrapper.lambda().orderByDesc(YwxtWsLog::getOpTime);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }


}
