package com.concise.gen.ywxtsendlog.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * ws发送日志
 *
 * <AUTHOR>
 * @date 2024-11-18 13:22:13
 */
@Data
@TableName("ywxt_send_log")
public class YwxtSendLog{

    /**  主键 */
    @TableId(type = IdType.ASSIGN_ID)
    private String taskId;

    /**  操作时间 */
    private Date opTime;

    private Integer success;

    /**  类别 */
    private String typeName;

    /**  矫正对象 */
    private String correctionObject;
    /**矫正单位*/
    private String deptName;

    private String dataId;
    /**发送单位名称*/
    private String sendOrgName;
    private String sendOrgId;
    private String sendOrgPids;
    /**接收单位名称*/
    private String receiveOrgName;
    private String receiveOrgType;

}
