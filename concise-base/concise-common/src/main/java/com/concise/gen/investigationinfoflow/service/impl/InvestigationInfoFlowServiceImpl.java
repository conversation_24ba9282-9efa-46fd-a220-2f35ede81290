package com.concise.gen.investigationinfoflow.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.gen.investigation.param.InvestigationApprovalParam;
import com.concise.gen.investigation.param.InvestigationDeliberationParam;
import com.concise.gen.investigation.param.InvestigationGroupParam;
import com.concise.gen.investigation.param.InvestigationReviewParam;
import com.concise.gen.investigation.service.InvestigationInfoService;
import com.concise.gen.investigationinfoflow.entity.InvestigationInfoFlow;
import com.concise.gen.investigationinfoflow.mapper.InvestigationInfoFlowMapper;
import com.concise.gen.investigationinfoflow.param.InvestigationInfoFlowParam;
import com.concise.gen.investigationinfoflow.service.InvestigationInfoFlowService;
import com.concise.gen.investigationinfoflow.util.InvestigationFlowUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;

/**
 * 调查评估-流程记录service接口实现类
 *
 * <AUTHOR>
 * @date 2025-03-25 09:02:58
 */
@Service
public class InvestigationInfoFlowServiceImpl extends ServiceImpl<InvestigationInfoFlowMapper, InvestigationInfoFlow> implements InvestigationInfoFlowService {

    @Resource
    private SysFileInfoService sysFileInfoService;
    @Resource
    private InvestigationInfoService investigationInfoService;

    @Override
    public List<InvestigationInfoFlow> list(String investigationInfoId) {
        LambdaQueryWrapper<InvestigationInfoFlow> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(InvestigationInfoFlow::getInvestigationInfoId, investigationInfoId);
        queryWrapper.orderByAsc(InvestigationInfoFlow::getSpTime);
        List<InvestigationInfoFlow> list = this.list(queryWrapper);
        for (InvestigationInfoFlow inf : list) {
            if (inf.getType() == 2 || 0 == inf.getType()) {
                //流程终止的拼附件
                inf.setFileList(sysFileInfoService.getFileList(investigationInfoId,"LCZZ"));
            }
        }
        return list;
    }

    @Override
    public void add(InvestigationInfoFlowParam investigationInfoFlowParam) {
        InvestigationInfoFlow investigationInfoFlow = new InvestigationInfoFlow();
        BeanUtil.copyProperties(investigationInfoFlowParam, investigationInfoFlow);
        this.save(investigationInfoFlow);
    }

    /**
     * 开启流程时，初始化所有流程节点
     */
    @Override
    public void initFlow(String investigationInfoId) {
        Date date = getDay(1000);
        List<InvestigationInfoFlow> list = Arrays.asList(
            new InvestigationInfoFlow(investigationInfoId, 1, 9, "PGZT01", "接收调查评估委托", addOneMinute(date, 10)),
            new InvestigationInfoFlow(investigationInfoId, 1, 9, "PGZT02", "调查小组公告", addOneMinute(date, 20)),
            new InvestigationInfoFlow(investigationInfoId, 2, 9, "PGZT03_1", "调查单位接收", addOneMinute(date, 30)),
            new InvestigationInfoFlow(investigationInfoId, 2, 9, "PGZT03_2", "管理评估清单", addOneMinute(date, 40)),
            new InvestigationInfoFlow(investigationInfoId, 2, 9, "PGZT03_3", "查看和提交评估结果", addOneMinute(date, 50)),
            new InvestigationInfoFlow(investigationInfoId, 3, 9, "PGZT04", "初审/小组合议", addOneMinute(date, 60)),
            new InvestigationInfoFlow(investigationInfoId, 3, 9, "PGZT05", "初审/集体评议", addOneMinute(date, 70)),
            new InvestigationInfoFlow(investigationInfoId, 3, 9, "PGZT06", "审批", addOneMinute(date, 80)),
            new InvestigationInfoFlow(investigationInfoId, 4, 9, "PGZT07", "反馈", addOneMinute(date, 90))
        );
        this.saveBatch(list);
    }


    /**
     * 待调查退回时更新流程记录
     */
    @Override
    public void ddcBack(InvestigationGroupParam param) {
        Date date = getDay(1000);
        //更新流程记录
        LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        flowLambdaUpdateWrapper.set(InvestigationInfoFlow::getUserId, param.getApprovalPsnId())
                .set(InvestigationInfoFlow::getUserName, param.getApprovalPsn())
                .set(InvestigationInfoFlow::getDeptId, param.getApprovalDeptId())
                .set(InvestigationInfoFlow::getDeptName, param.getApprovalDeptName())
                .set(InvestigationInfoFlow::getMsg, param.getApprovalRemark())
                .set(InvestigationInfoFlow::getSpTime, new Date())
                .set(InvestigationInfoFlow::getType, 0)
                .set(InvestigationInfoFlow::getFormVal, investigationInfoService.detail(param.getId(), null).toJSONString())
                .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                .eq(InvestigationInfoFlow::getStepCode, "PGZT03_1")
                .eq(InvestigationInfoFlow::getType, 9);
        this.update(flowLambdaUpdateWrapper);
        //先删除状态type=9 未开始的数据
        this.remove(new LambdaQueryWrapper<InvestigationInfoFlow>()
                .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                .eq(InvestigationInfoFlow::getType, 9));
        //初始化未开始流程
        List<InvestigationInfoFlow> list = Arrays.asList(
                new InvestigationInfoFlow(param.getId(), 1, 9, "PGZT02", "调查小组公告", addOneMinute(date, 20)),
                new InvestigationInfoFlow(param.getId(), 2, 9, "PGZT03_1", "调查单位接收", addOneMinute(date, 30)),
                new InvestigationInfoFlow(param.getId(), 2, 9, "PGZT03_2", "管理评估清单", addOneMinute(date, 40)),
                new InvestigationInfoFlow(param.getId(), 2, 9, "PGZT03_3", "查看和提交评估结果", addOneMinute(date, 50)),
                new InvestigationInfoFlow(param.getId(), 3, 9, "PGZT04", "初审/小组合议", addOneMinute(date, 60)),
                new InvestigationInfoFlow(param.getId(), 3, 9, "PGZT05", "初审/集体评议", addOneMinute(date, 70)),
                new InvestigationInfoFlow(param.getId(), 3, 9, "PGZT06", "审批", addOneMinute(date, 80)),
                new InvestigationInfoFlow(param.getId(), 4, 9, "PGZT07", "反馈", addOneMinute(date, 90))
        );
        this.saveBatch(list);
    }

    /**
     * 待初审/小组意见退回时更新流程记录
     */
    @Override
    public void xzyjBack(InvestigationDeliberationParam param) {
        Date date = getDay(1000);
        //更新流程记录
        LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        flowLambdaUpdateWrapper.set(InvestigationInfoFlow::getUserId, param.getApprovalPsnId())
                .set(InvestigationInfoFlow::getUserName, param.getApprovalPsn())
                .set(InvestigationInfoFlow::getDeptId, param.getApprovalDeptId())
                .set(InvestigationInfoFlow::getDeptName, param.getApprovalDeptName())
                .set(InvestigationInfoFlow::getMsg, param.getApprovalRemark())
                .set(InvestigationInfoFlow::getSpTime, new Date())
                .set(InvestigationInfoFlow::getType, 0)
                .set(InvestigationInfoFlow::getFormVal, investigationInfoService.detail(param.getId(), null).toJSONString())
                .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                .eq(InvestigationInfoFlow::getStepCode, "PGZT04")
                .eq(InvestigationInfoFlow::getType, 9);
        this.update(flowLambdaUpdateWrapper);
        //先删除状态type=9 未开始的数据
        this.remove(new LambdaQueryWrapper<InvestigationInfoFlow>()
                .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                .eq(InvestigationInfoFlow::getType, 9));
        //初始化未开始流程
        List<InvestigationInfoFlow> list = Arrays.asList(
                new InvestigationInfoFlow(param.getId(), 2, 9, "PGZT03_1", "调查单位接收", addOneMinute(date, 30)),
                new InvestigationInfoFlow(param.getId(), 2, 9, "PGZT03_2", "管理评估清单", addOneMinute(date, 40)),
                new InvestigationInfoFlow(param.getId(), 2, 9, "PGZT03_3", "查看和提交评估结果", addOneMinute(date, 50)),
                new InvestigationInfoFlow(param.getId(), 3, 9, "PGZT04", "初审/小组合议", addOneMinute(date, 60)),
                new InvestigationInfoFlow(param.getId(), 3, 9, "PGZT05", "初审/集体评议", addOneMinute(date, 70)),
                new InvestigationInfoFlow(param.getId(), 3, 9, "PGZT06", "审批", addOneMinute(date, 80)),
                new InvestigationInfoFlow(param.getId(), 4, 9, "PGZT07", "反馈", addOneMinute(date, 90))
        );
        this.saveBatch(list);
    }

    /**
     * 待初审/评议退回时更新流程记录
     */
    @Override
    public void pyBack(InvestigationReviewParam param) {
        Date date = getDay(1000);
        //更新流程记录
        LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        flowLambdaUpdateWrapper.set(InvestigationInfoFlow::getUserId, param.getApprovalPsnId())
                .set(InvestigationInfoFlow::getUserName, param.getApprovalPsn())
                .set(InvestigationInfoFlow::getDeptId, param.getApprovalDeptId())
                .set(InvestigationInfoFlow::getDeptName, param.getApprovalDeptName())
                .set(InvestigationInfoFlow::getMsg, param.getApprovalRemark())
                .set(InvestigationInfoFlow::getSpTime, new Date())
                .set(InvestigationInfoFlow::getType, 0)
                .set(InvestigationInfoFlow::getFormVal, investigationInfoService.detail(param.getId(), null).toJSONString())
                .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                .eq(InvestigationInfoFlow::getStepCode, "PGZT05")
                .eq(InvestigationInfoFlow::getType, 9);
        this.update(flowLambdaUpdateWrapper);
        //先删除状态type=9 未开始的数据
        this.remove(new LambdaQueryWrapper<InvestigationInfoFlow>()
                .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                .eq(InvestigationInfoFlow::getType, 9));
        //初始化未开始流程
        List<InvestigationInfoFlow> list = Arrays.asList(
                new InvestigationInfoFlow(param.getId(), 3, 9, "PGZT04", "初审/小组合议", addOneMinute(date, 60)),
                new InvestigationInfoFlow(param.getId(), 3, 9, "PGZT05", "初审/集体评议", addOneMinute(date, 70)),
                new InvestigationInfoFlow(param.getId(), 3, 9, "PGZT06", "审批", addOneMinute(date, 80)),
                new InvestigationInfoFlow(param.getId(), 4, 9, "PGZT07", "反馈", addOneMinute(date, 90))
        );
        this.saveBatch(list);

    }

    /**
     * 待审批退回时更新流程记录
     */
    @Override
    public void dspBack(InvestigationApprovalParam param) {
        Date date = getDay(1000);
        //更新待审批流程
        LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        flowLambdaUpdateWrapper.set(InvestigationInfoFlow::getUserId, param.getApprovalPsnId())
                .set(InvestigationInfoFlow::getUserName, param.getApprovalPsn())
                .set(InvestigationInfoFlow::getDeptId, param.getApprovalDeptId())
                .set(InvestigationInfoFlow::getDeptName, param.getApprovalDeptName())
                .set(InvestigationInfoFlow::getMsg, param.getApprovalRemark())
                .set(InvestigationInfoFlow::getSpTime, new Date())
                .set(InvestigationInfoFlow::getType, 0)
                .set(InvestigationInfoFlow::getFormVal, investigationInfoService.detail(param.getId(), null).toJSONString())
                .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                .eq(InvestigationInfoFlow::getStepCode, "PGZT06")
                .eq(InvestigationInfoFlow::getType, 9);
        this.update(flowLambdaUpdateWrapper);
        //先删除状态type=9 未开始的数据
        this.remove(new LambdaQueryWrapper<InvestigationInfoFlow>()
                .eq(InvestigationInfoFlow::getInvestigationInfoId, param.getId())
                .eq(InvestigationInfoFlow::getType, 9));
        //初始化未开始流程
        List<InvestigationInfoFlow> list = Arrays.asList(
                new InvestigationInfoFlow(param.getId(), 3, 9, "PGZT05", "初审/集体评议", addOneMinute(date, 70)),
                new InvestigationInfoFlow(param.getId(), 3, 9, "PGZT06", "审批", addOneMinute(date, 80)),
                new InvestigationInfoFlow(param.getId(), 4, 9, "PGZT07", "反馈", addOneMinute(date, 90))
        );
        this.saveBatch(list);
    }

    /**
     * 获取参数日期 一分钟之后的时间
     * @param date
     * @param second 秒
     * @return
     */
    private Date addOneMinute(Date date, int second) {
        Instant instant = date.toInstant().plusSeconds(second);
        return Date.from(instant);
    }

    /**
     * 获取当前时间 N 天前后的日期
     * @param dayNum 天数
     * @return
     */
    public static Date getDay(int dayNum) {
        //请求参数 更新时间
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DATE, dayNum);
        Date date = c.getTime();
        return date;
    }

    @Override
    public int uptBackNum(LambdaUpdateWrapper<InvestigationInfoFlow> flowLambdaUpdateWrapper) {
        return this.baseMapper.update(null, flowLambdaUpdateWrapper);
    }

    @Override
    public List<Map<String, Object>> listFlow(String investigationInfoId) {
        LambdaQueryWrapper<InvestigationInfoFlow> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(InvestigationInfoFlow::getInvestigationInfoId, investigationInfoId);
        queryWrapper.orderByAsc(InvestigationInfoFlow::getSpTime);
        List<InvestigationInfoFlow> list = this.list(queryWrapper);
        for (InvestigationInfoFlow inf : list) {
            if (inf.getType() == 2) {
                //流程终止的拼附件
                inf.setFileList(sysFileInfoService.getFileList(investigationInfoId,"LCZZ"));
            }
            if (0 == inf.getType()) {
                //退回 附件
                if ("PGZT03_1".equals(inf.getStepCode())) {
                    inf.setFileList(sysFileInfoService.getFileList(investigationInfoId,"inve_return2"));
                }
            }
        }

        // 调用工具类进行转换(按nodeLevel排序，连续相同的nodeLevel为一组)
        // List<Map<String, Object>> groupedFlows = InvestigationFlowUtils.groupFlowsByNodeLevel(list);
        // 调用工具类进行转换(按nodeLevel排序，相同的nodeLevel为一组)
        List<Map<String, Object>> groupedFlows = InvestigationFlowUtils.convertToGroupedList(list);

        // 构建转换后的结构
        return groupedFlows;
    }

    @Override
    public void draftTag(String investigationInfoId, String stepCode) {
        //标记暂存
        this.lambdaUpdate()
                .set(InvestigationInfoFlow::getDraft, 1)
                .eq(InvestigationInfoFlow::getInvestigationInfoId, investigationInfoId)
                .eq(InvestigationInfoFlow::getStepCode, stepCode)
                .eq(InvestigationInfoFlow::getType, 9)
                .update();
    }
}
