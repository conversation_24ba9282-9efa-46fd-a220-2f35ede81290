package com.concise.gen.documentserial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.documentserial.entity.DocumentSerial;
import com.concise.gen.documentserial.mapper.DocumentSerialMapper;
import com.concise.gen.documentserial.param.DocumentSerialParam;
import com.concise.gen.documentserial.service.DocumentSerialService;
import com.concise.gen.investigation.entity.InvestigationInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 文书号维护service接口实现类
 *
 * <AUTHOR>
 * @date 2025-03-27 10:09:11
 */
@Service
@Slf4j
public class DocumentSerialServiceImpl extends ServiceImpl<DocumentSerialMapper, DocumentSerial> implements DocumentSerialService {

    @Override
    public String getSerial(String deptId, String type, String sName, String referId) {
        //TODO 因为是按每个区县维护的文号，且调查评估量不大，暂不考虑文书号唯一性的问题，后续有时间可优化
        int year = LocalDate.now().getYear();
        Integer serialNumber = this.baseMapper.getMaxSerialNumber(deptId, type, year);
        if (null == serialNumber) {
            DocumentSerial documentSerial = new DocumentSerial();
            documentSerial.setType(type);
            documentSerial.setDeptId(deptId);
            documentSerial.setSName(sName);
            documentSerial.setCurrentYear(year);
            documentSerial.setSerialNumber(1);
            documentSerial.setReferId(referId);
            this.save(documentSerial);
            return  "(" + year + ")" + sName + "矫调评字第001号";
        } else {
            DocumentSerial documentSerial = new DocumentSerial();
            documentSerial.setType(type);
            documentSerial.setDeptId(deptId);
            documentSerial.setSName(sName);
            documentSerial.setCurrentYear(year);
            documentSerial.setSerialNumber(serialNumber + 1);
            documentSerial.setReferId(referId);
            this.save(documentSerial);
            String docNum = "";
            if (documentSerial.getSerialNumber() < 100) {
                docNum = "(" + year + ")" + sName + "矫调评字第" + String.format("%03d", documentSerial.getSerialNumber()) + "号";
            } else {
                docNum = "(" + year + ")" + sName + "矫调评字第" + documentSerial.getSerialNumber() + "号";
            }
            return docNum;
        }
    }

    @Override
    public String checkSerialNum(DocumentSerialParam param) {
        LambdaQueryWrapper<DocumentSerial> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(DocumentSerial::getDeptId, param.getDeptId());
        queryWrapper.eq(DocumentSerial::getCurrentYear, LocalDate.now().getYear());
        queryWrapper.eq(DocumentSerial::getType, param.getType());
        queryWrapper.eq(DocumentSerial::getSerialNumber, param.getSerialNumber());
        queryWrapper.notIn(DocumentSerial::getReferId, param.getReferId().split(","));
        int num = this.count(queryWrapper);
        //ds不需做非空判断，因为调该方法前，该文书号肯定已经有值
        if (num > 0) {
            //文书号已使用
            return "该文书号已经被使用";
        } else {
            //文书号未使用，将文书号维护进表
            this.lambdaUpdate()
                    .set(DocumentSerial::getSerialNumber, param.getSerialNumber())
                    .eq(DocumentSerial::getDeptId, param.getDeptId())
                    .eq(DocumentSerial::getCurrentYear, LocalDate.now().getYear())
                    .eq(DocumentSerial::getType, param.getType())
                    .eq(DocumentSerial::getReferId, param.getReferId())
                    .update();
            return null;
        }
    }
}
