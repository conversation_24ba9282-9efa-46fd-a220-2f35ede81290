package com.concise.gen.placechange.param;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 迁入导出
 *
 * <AUTHOR>
 * @date 2025-04-01 11:09:06
 */
@Data
public class ImmExport {

    @Excel(name = "状态", replace = {"待审核_1","待外省移送档案_2","待反馈入矫情况_3","已完结_4"}, width = 30)
    private String processStatus;
    @Excel(name = "姓名", width = 20)
    private String correctionObjName;
    @Excel(name = "迁出单位", width = 40)
    private String extDeptName;
    @Excel(name = "迁入单位", width = 40)
    private String deptName;
    @Excel(name = "申请时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date applicationDate;
    @Excel(name = "审核结果", replace = {"同意接收_1","不同意接收_2"}, width = 30)
    private String opinionResult;
    @Excel(name = "入矫情况", replace = {"在规定时限内报到_1","超出规定时限报到_2","未报到且下落不明_3","其它_4"}, width = 30)
    private String registrationResult;
    @Excel(name = "迁出省（市）", width = 20)
    private String destinationProvName;

}
