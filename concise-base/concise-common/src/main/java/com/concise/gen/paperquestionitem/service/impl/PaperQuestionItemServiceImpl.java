package com.concise.gen.paperquestionitem.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.paperquestionitem.entity.PaperQuestionItem;
import com.concise.gen.paperquestionitem.enums.PaperQuestionItemExceptionEnum;
import com.concise.gen.paperquestionitem.mapper.PaperQuestionItemMapper;
import com.concise.gen.paperquestionitem.param.PaperQuestionItemParam;
import com.concise.gen.paperquestionitem.service.PaperQuestionItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 题库-指标选项service接口实现类
 *
 * <AUTHOR>
 * @date 2025-03-20 15:03:00
 */
@Service
public class PaperQuestionItemServiceImpl extends ServiceImpl<PaperQuestionItemMapper, PaperQuestionItem> implements PaperQuestionItemService {

    @Override
    public PageResult<PaperQuestionItem> page(PaperQuestionItemParam paperQuestionItemParam) {
        QueryWrapper<PaperQuestionItem> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(paperQuestionItemParam)) {

            // 根据题库主键 查询
            if (ObjectUtil.isNotEmpty(paperQuestionItemParam.getPaperQuestionBankId())) {
                queryWrapper.lambda().eq(PaperQuestionItem::getPaperQuestionBankId, paperQuestionItemParam.getPaperQuestionBankId());
            }
            // 根据指标选项内容 查询
            if (ObjectUtil.isNotEmpty(paperQuestionItemParam.getContent())) {
                queryWrapper.lambda().eq(PaperQuestionItem::getContent, paperQuestionItemParam.getContent());
            }
            // 根据分数 查询
            if (ObjectUtil.isNotEmpty(paperQuestionItemParam.getItemScore())) {
                queryWrapper.lambda().eq(PaperQuestionItem::getItemScore, paperQuestionItemParam.getItemScore());
            }
            // 根据排序号 查询
            if (ObjectUtil.isNotEmpty(paperQuestionItemParam.getSerialNumber())) {
                queryWrapper.lambda().eq(PaperQuestionItem::getSerialNumber, paperQuestionItemParam.getSerialNumber());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<PaperQuestionItem> list(PaperQuestionItemParam paperQuestionItemParam) {
        return this.list();
    }

    @Override
    public void add(PaperQuestionItemParam paperQuestionItemParam) {
        PaperQuestionItem paperQuestionItem = new PaperQuestionItem();
        BeanUtil.copyProperties(paperQuestionItemParam, paperQuestionItem);
        this.save(paperQuestionItem);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(PaperQuestionItemParam paperQuestionItemParam) {
        this.removeById(paperQuestionItemParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(PaperQuestionItemParam paperQuestionItemParam) {
        PaperQuestionItem paperQuestionItem = this.queryPaperQuestionItem(paperQuestionItemParam);
        BeanUtil.copyProperties(paperQuestionItemParam, paperQuestionItem);
        this.updateById(paperQuestionItem);
    }

    @Override
    public PaperQuestionItem detail(PaperQuestionItemParam paperQuestionItemParam) {
        return this.queryPaperQuestionItem(paperQuestionItemParam);
    }

    /**
     * 获取题库-指标选项
     *
     * <AUTHOR>
     * @date 2025-03-20 15:03:00
     */
    private PaperQuestionItem queryPaperQuestionItem(PaperQuestionItemParam paperQuestionItemParam) {
        PaperQuestionItem paperQuestionItem = this.getById(paperQuestionItemParam.getId());
        if (ObjectUtil.isNull(paperQuestionItem)) {
            throw new ServiceException(PaperQuestionItemExceptionEnum.NOT_EXIST);
        }
        return paperQuestionItem;
    }
}
