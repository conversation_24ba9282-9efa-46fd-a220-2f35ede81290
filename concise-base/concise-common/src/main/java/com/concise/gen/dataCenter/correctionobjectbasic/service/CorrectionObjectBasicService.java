package com.concise.gen.dataCenter.correctionobjectbasic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.dataCenter.correctionobjectbasic. entity.CorrectionObjectBasic;
import com.concise.gen.dataCenter.correctionobjectbasic. param.CorrectionObjectBasicParam;
import java.util.List;
import java.util.Set;

/**
 * 社区矫正对象信息-基本信息service接口
 *
 * <AUTHOR>
 * @date 2021-08-27 11:35:18
 */
public interface CorrectionObjectBasicService extends IService<CorrectionObjectBasic> {

    /**
     * 分页社区矫正对象信息-基本信息
     *
     * <AUTHOR>
     * @date 2021-08-27 11:35:18
     */
    PageResult<CorrectionObjectBasic> page(CorrectionObjectBasicParam correctionObjectBasicParam, Set<String> org, boolean b);

    /**
     * 社区矫正对象信息-基本信息列表
     *
     * <AUTHOR>
     * @date 2021-08-27 11:35:18
     */
    List<CorrectionObjectBasic> list(CorrectionObjectBasicParam correctionObjectBasicParam);

    /**
     * 添加社区矫正对象信息-基本信息
     *
     * <AUTHOR>
     * @date 2021-08-27 11:35:18
     */
    void add(CorrectionObjectBasicParam correctionObjectBasicParam);

    /**
     * 删除社区矫正对象信息-基本信息
     *
     * <AUTHOR>
     * @date 2021-08-27 11:35:18
     */
    void delete(CorrectionObjectBasicParam correctionObjectBasicParam);

    /**
     * 编辑社区矫正对象信息-基本信息
     *
     * <AUTHOR>
     * @date 2021-08-27 11:35:18
     */
    void edit(CorrectionObjectBasicParam correctionObjectBasicParam);

    /**
     * 查看社区矫正对象信息-基本信息
     *
     * <AUTHOR>
     * @date 2021-08-27 11:35:18
     */
        CorrectionObjectBasic detail(CorrectionObjectBasicParam correctionObjectBasicParam);
}
