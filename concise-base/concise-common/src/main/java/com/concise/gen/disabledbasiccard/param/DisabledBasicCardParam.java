package com.concise.gen.disabledbasiccard.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
* 残疾人信息参数类
 *
 * <AUTHOR>
 * @date 2024-01-09 11:23:58
*/
@Data
public class DisabledBasicCardParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查pkId参数", groups = {edit.class, delete.class, detail.class})
    private String pkId;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空，请检查identityCard参数", groups = {add.class, edit.class})
    private String identityCard;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查name参数", groups = {add.class, edit.class})
    private String name;

    /**
     * 残疾类型
     */
    @NotBlank(message = "残疾类型不能为空，请检查disableType参数", groups = {add.class, edit.class})
    private String disableType;

    /**
     * 残疾人证号
     */
    @NotBlank(message = "残疾人证号不能为空，请检查disableCardNum参数", groups = {add.class, edit.class})
    private String disableCardNum;

    /**
     * 残疾等级
     */
    @NotBlank(message = "残疾等级不能为空，请检查disableLevel参数", groups = {add.class, edit.class})
    private String disableLevel;

    /**
     * 发证日期
     */
    @NotBlank(message = "发证日期不能为空，请检查issueDate参数", groups = {add.class, edit.class})
    private String issueDate;

    /**
     * 有效期开始
     */
    @NotBlank(message = "有效期开始不能为空，请检查stime参数", groups = {add.class, edit.class})
    private String stime;

    /**
     * 有效期结束
     */
    @NotBlank(message = "有效期结束不能为空，请检查etime参数", groups = {add.class, edit.class})
    private String etime;

    /**
     * 注销时间
     */
    @NotBlank(message = "注销时间不能为空，请检查zxtime参数", groups = {add.class, edit.class})
    private String zxtime;

    /**
     * 矫正对象id
     */
    @NotBlank(message = "矫正对象id不能为空，请检查jzdxId参数", groups = {add.class, edit.class})
    private String jzdxId;

    /**
     * 矫正机构id
     */
    @NotBlank(message = "矫正机构id不能为空，请检查jzjg参数", groups = {add.class, edit.class})
    private String jzjg;

    /**
     * 矫正机构
     */
    @NotBlank(message = "矫正机构不能为空，请检查jzjgName参数", groups = {add.class, edit.class})
    private String jzjgName;

    private String zhuangtai;

    private Date updateTime;
}
