<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.investigationsign.mapper.InvestigationSignMapper">

    <select id="getDeviceSignHistory" resultType="java.util.HashMap">
        select
        file_before_id as fileBeforeId,
        img_base64_signature as imgBase64Signature,
        img_base64_fingerprint as imgBase64Fingerprint
        from investigation_device_sign_log where info_id = #{param1} and biz_type = #{param2} and success = 'Y'
        order by sign_time desc limit 1
    </select>
    <select id="getDeviceSignHistoryByFileAfterId" resultType="java.util.HashMap">
        select
        file_before_id as fileBeforeId,
        img_base64_signature as imgBase64Signature,
        img_base64_fingerprint as imgBase64Fingerprint
        from investigation_device_sign_log where file_after_id = #{param1} and success = 'Y'
    </select>
    <select id="getDeviceSignedBefore" resultType="java.lang.String">
        select file_before_id from investigation_device_sign_log where file_after_id = #{param1} and success = 'Y'
    </select>
</mapper>
