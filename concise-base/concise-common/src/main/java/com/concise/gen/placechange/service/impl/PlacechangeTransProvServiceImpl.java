package com.concise.gen.placechange.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.consts.SymbolConstant;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.PoiUtil;
import com.concise.gen.acceptcorrectiondoc.service.AcceptCorrectionDocService;
import com.concise.gen.acceptreturninfo.service.AcceptReturnInfoService;
import com.concise.gen.areainfo.service.AreaInfoService;
import com.concise.gen.dataCenter.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.dataCenter.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.gen.dataCenter.correctplacechange.entity.PlaceChangeDc;
import com.concise.gen.dataCenter.correctplacechange.service.PlaceChangeDcService;
import com.concise.gen.placechange.entity.PlacechangeTransProv;
import com.concise.gen.placechange.mapper.PlacechangeTransProvMapper;
import com.concise.gen.placechange.param.EmiExport;
import com.concise.gen.placechange.param.ImmExport;
import com.concise.gen.placechange.param.PlacechangeTransProvParam;
import com.concise.gen.placechange.service.PlacechangeTransProvService;
import com.concise.gen.sysorg.entity.OrgCommon;
import com.concise.gen.sysorg.service.OrgCommonService;
import com.concise.gen.webservice.service.SendPlacechangeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 跨省执行地变更service接口实现类
 *
 * <AUTHOR>
 * @date 2025-04-01 11:09:06
 */
@Service
public class PlacechangeTransProvServiceImpl extends ServiceImpl<PlacechangeTransProvMapper, PlacechangeTransProv> implements PlacechangeTransProvService {

    @Resource
    private CorrectionObjectInformationService objInfoDcService;
    @Resource
    private PlaceChangeDcService placeChangeDcService;
    @Resource
    private OrgCommonService orgService;
    @Resource
    private SysFileInfoService sysFileInfoService;
    @Resource
    private AreaInfoService areaInfoService;
    @Resource
    private SendPlacechangeService sendService;
    @Resource
    private AcceptCorrectionDocService acceptCorrectionDocService;
    @Resource
    private AcceptReturnInfoService acceptReturnInfoService;
    @Override
    public PageResult<PlacechangeTransProv> page(PlacechangeTransProvParam param) {
        QueryWrapper<PlacechangeTransProv> queryWrapper = buildQueryWrapper(param);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    private QueryWrapper<PlacechangeTransProv> buildQueryWrapper(PlacechangeTransProvParam param) {
        QueryWrapper<PlacechangeTransProv> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据变更类型 查询
            if (ObjectUtil.isNotEmpty(param.getTransType())) {
                queryWrapper.lambda().eq(PlacechangeTransProv::getTransType, param.getTransType());
            }
            if (ObjectUtil.isNotEmpty(param.getProcessStatus())) {
                queryWrapper.lambda().eq(PlacechangeTransProv::getProcessStatus, param.getProcessStatus());
            }
            // 根据矫正单位 查询
            if (ObjectUtil.isNotEmpty(param.getDeptId())) {
                queryWrapper.lambda().and(i->i.eq(PlacechangeTransProv::getDeptId, param.getDeptId()).or().like(PlacechangeTransProv::getDeptPids, param.getDeptId()));
            }
            // 根据社区矫正对象姓名 查询
            if (ObjectUtil.isNotEmpty(param.getCorrectionObjName())) {
                queryWrapper.lambda().like(PlacechangeTransProv::getCorrectionObjName, param.getCorrectionObjName());
            }
            // 根据申请日期 查询
            if (ObjectUtil.isNotEmpty(param.getSearchBeginTime())) {
                queryWrapper.lambda().ge(PlacechangeTransProv::getApplicationDate, param.getSearchBeginTime());
            }
            if (ObjectUtil.isNotEmpty(param.getSearchEndTime())) {
                queryWrapper.lambda().le(PlacechangeTransProv::getApplicationDate, param.getSearchEndTime());
            }
        }
        queryWrapper.lambda().eq(PlacechangeTransProv::getDeleted, 0).orderByDesc(PlacechangeTransProv::getCreateTime);
        return queryWrapper;
    }

    @Override
    public List<PlacechangeTransProv> list(PlacechangeTransProvParam placechangeTransProvParam) {
        return this.list();
    }

    @Override
    public void add(PlacechangeTransProvParam param) {
        PlacechangeTransProv data = new PlacechangeTransProv();
        BeanUtil.copyProperties(param, data);
        String uuid = IdUtil.fastSimpleUUID();
        data.setTransType("0");
        data.setId(uuid);
        data.setUniformCode(uuid);
        data.setProcessStatus("1");
        data.setDestinationProvName(areaInfoService.getById(param.getDestinationCode().split(SymbolConstant.COMMA)[0]).getAreaname());
        data.setCreateTime(DateUtil.date());
        this.save(data);
        sysFileInfoService.setBiz(param.getFiles(), data.getId(),"46001");
        sendService.requestXTBH46001(data, sysFileInfoService.getDocList(data.getId(),"46001"));
    }

    @Override
    public void receive(PlacechangeTransProvParam param) {

        DateTime now = DateUtil.date();
        /*PlacechangeTransProv old = this.lambdaQuery().eq(PlacechangeTransProv::getTransType, "1")
                .eq(PlacechangeTransProv::getDeptCode, param.getDeptCode())
                .eq(PlacechangeTransProv::getCertNum, param.getCertNum())
                .orderByDesc(PlacechangeTransProv::getCreateTime)
                .last("limit 1")
                .one();

        if (old != null && "1".equals(old.getProcessStatus())) {
            List<AcceptCorrectionDoc> oldDocList = acceptCorrectionDocService.list(old.getId());
            List<AcceptCorrectionDoc> newDocList = acceptCorrectionDocService.list(param.getId());
            Boolean check = acceptReturnInfoService.check1(old.getCreateTime(), newDocList, oldDocList);
            if (check) {
                acceptReturnInfoService.save("XTBH46001", param.getId(), param.getUniformCode(), now, old.getCreateTime(), old.getCorrectionObjName(), old.getDeptCode(), old.getDeptId(), old.getDeptPids(), old.getDeptName());
            }
        }*/

        param.setRegisteredAddressCode(areaInfoService.transToAreaIds(param.getRegisteredAddressCode()));
        param.setResidenceCode(areaInfoService.transToAreaIds(param.getResidenceCode()));
        param.setDestinationCode(areaInfoService.transToAreaIds(param.getDestinationCode()));
        PlacechangeTransProv data = new PlacechangeTransProv();
        BeanUtil.copyProperties(param, data);
        data.setTransType("1");
        data.setProcessStatus("1");
        data.setDestinationProvName("贵州省");
        data.setCreateTime(now);
        this.save(data);
    }

    @Override
    public String receivePsn(PlacechangeTransProvParam param) {
        PlacechangeTransProv data = this.lambdaQuery()
                .eq(PlacechangeTransProv::getUniformCode, param.getUniformCode())
                .orderByDesc(PlacechangeTransProv::getCreateTime)
                .last("limit 1").one();
        if (data == null) {
            return "";
        }
        data.setProcessStatus("3");
        data.setTransDate(DateUtil.date());
        data.setTaskId3(param.getTaskId3());
        this.updateById(data);
        return data.getId();
    }

    @Override
    public String receiveOpinion(PlacechangeTransProvParam param) {
        PlacechangeTransProv data = this.getById(param.getUniformCode());
        if (data == null) {
            return "";
        }
        data.setOpinionResult(param.getOpinionResult());
        data.setOpinionRemark(param.getOpinionRemark());
        data.setOpinionDate(DateUtil.parseDate(param.getOpinionDate()));
        data.setExtProcessDept(param.getExtProcessDept());
        data.setExtProcessDeptTel(param.getExtProcessDeptTel());
        data.setExtProcessPsn(param.getExtProcessPsn());
        data.setTaskId2(param.getTaskId2());
        data.setProcessStatus("2");
        this.updateById(data);
        return data.getId();
    }

    @Override
    public String receiveResult(PlacechangeTransProvParam param) {
        PlacechangeTransProv data = this.getById(param.getUniformCode());
        if (data == null) {
            return "";
        }
        data.setRegistrationResult(param.getRegistrationResult());
        data.setRegistrationRemark(param.getRegistrationRemark());
        data.setCorrectionStartAt(param.getCorrectionStartAt());
        data.setTaskId4(param.getTaskId4());
        data.setProcessStatus("4");
        this.updateById(data);
        return data.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(PlacechangeTransProvParam placechangeTransProvParam) {
        this.lambdaUpdate().set(PlacechangeTransProv::getDeleted, 1).eq(PlacechangeTransProv::getId, placechangeTransProvParam.getId()).update();
    }

    @Override
    public void trans(PlacechangeTransProvParam param) {
        PlacechangeTransProv data = this.getById(param.getId());
        BeanUtil.copyProperties(param, data);
        data.setProcessStatus("3");
        this.updateById(data);
        sysFileInfoService.setBiz(param.getFiles(), data.getId(),"46003");
        sendService.requestXTBH46003(this.getById(param.getId()), sysFileInfoService.getDocList(data.getId(),"46003"));
    }

    @Override
    public void approval(PlacechangeTransProvParam param) {
        PlacechangeTransProv data = this.getById(param.getId());
        BeanUtil.copyProperties(param, data);
        data.setProcessStatus("2");
        this.updateById(data);
        sysFileInfoService.setBiz(param.getFiles(), data.getId(),"46002");
        sendService.requestXTBH46002(this.getById(param.getId()), sysFileInfoService.getDocList(data.getId(),"46002"));
    }

    @Override
    public void feedback(PlacechangeTransProvParam param) {
        PlacechangeTransProv data = this.getById(param.getId());
        BeanUtil.copyProperties(param, data);
        data.setProcessStatus("4");
        this.updateById(data);
        sysFileInfoService.setBiz(param.getFiles(), data.getId(),"46004");
        sendService.requestXTBH46004(this.getById(param.getId()), sysFileInfoService.getDocList(data.getId(),"46004"));
    }

    @Override
    public PlacechangeTransProv detail(PlacechangeTransProvParam placechangeTransProvParam) {
        PlacechangeTransProv data = this.getById(placechangeTransProvParam.getId());
        if ("1".equals(data.getTransType())) {
            data.setFiles46001(acceptCorrectionDocService.getSysFileInfoList(data.getId(),"XTBH46001"));
            data.setFiles46002(sysFileInfoService.getDocList(data.getId(),"46002"));
            data.setFiles46003(acceptCorrectionDocService.getSysFileInfoList(data.getId(),"XTBH46003"));
            data.setFiles46004(sysFileInfoService.getDocList(data.getId(),"46004"));
        }else {
            data.setFiles46001(sysFileInfoService.getDocList(data.getId(),"46001"));
            data.setFiles46002(acceptCorrectionDocService.getSysFileInfoList(data.getId(),"XTBH46002"));
            data.setFiles46003(sysFileInfoService.getDocList(data.getId(),"46003"));
            data.setFiles46004(acceptCorrectionDocService.getSysFileInfoList(data.getId(),"XTBH46004"));
        }
        return data;
    }

    @Override
    public PlacechangeTransProv fillInfo(String id) {
        PlacechangeTransProv placechange = new PlacechangeTransProv();
        PlaceChangeDc dc = placeChangeDcService.getById(id);
        if (dc == null) {
            return placechange;
        }
        CorrectionObjectInformation obj = objInfoDcService.getById(dc.getPid());
        if (obj == null) {
            return placechange;
        }
        OrgCommon org = orgService.getById(orgService.getById(obj.getJzjg()).getPid());
        placechange.setDeptCode(org.getCode());
        placechange.setDeptName(org.getName());
        placechange.setDeptId(org.getId());
        placechange.setDeptPids(org.getPids());
        placechange.setCorrectionObjName(obj.getXm());
        placechange.setCorrectionObjId(obj.getId());
        placechange.setCorrectionObjCode(obj.getSqjzrybh());
        placechange.setCertType("111");
        placechange.setCertNum(obj.getSfzh());
        placechange.setNation(obj.getMz());
        if (!"02".equals(obj.getGj())&& !"03".equals(obj.getGj())) {
            placechange.setNationality("CHN");
        }else {
            placechange.setNationality("ZZZ");
        }
        placechange.setEducationalBackground(obj.getWhcd());
        placechange.setMaritalStatus(obj.getHyzk());
        placechange.setPoliticalStatus(obj.getXzzmn());
        placechange.setJob(obj.getPqzy());
        placechange.setEmployment(obj.getJyjxqk());
        placechange.setCorrectionType(obj.getJzlb().replace("0",""));
        placechange.setResidenceCode(obj.getGdjzdszs()+","+obj.getGdjzdszds()+","+obj.getGdjzdszxq());
        placechange.setResidence(obj.getGdjzdmx());
        placechange.setRegisteredAddressCode(obj.getHjszs()+","+obj.getHjszds()+","+obj.getHjszxq());
        placechange.setRegisteredAddress(obj.getHjszdmx());
        placechange.setCorrectionStart(obj.getSqjzksrq());
        placechange.setCorrectionEnd(obj.getSqjzjsrq());
        placechange.setCorrectionDuration(obj.getSqjzqx());
        placechange.setDeptContactPsn(org.getLxr());
        placechange.setDeptContactTel(org.getLxdh());
        placechange.setApplicationDate(dc.getSqsj());
        placechange.setDestination(dc.getQrdszsName());
        placechange.setDestinationCode(dc.getQrdszs()+","+dc.getQrdszd()+","+dc.getQrdszx());
        placechange.setDestination(dc.getQrdmx());
        placechange.setChangeReason(dc.getJzdbgsy());
        return placechange;
    }

    @Override
    public void test(String id) {
        PlacechangeTransProv data = this.getById(id);
        sendService.requestXTBH46001(data, sysFileInfoService.getDocList(data.getId(),"46001"));
        //sendService.requestXTBH46003(data, sysFileInfoService.getDocList(data.getId(),"46003"));
        //sendService.requestXTBH46002(data, sysFileInfoService.getDocList(data.getId(),"46002"));
        //sendService.requestXTBH46004(data, sysFileInfoService.getDocList(data.getId(),"46004"));
    }

    @Override
    public void export(PlacechangeTransProvParam param) {
        QueryWrapper<PlacechangeTransProv> queryWrapper = buildQueryWrapper(param);
        if ("0".equals(param.getTransType())) {
            // 跨省执行地迁出
            List<EmiExport> collect = this.list(queryWrapper).stream().map(item -> {
                EmiExport emiExport = new EmiExport();
                BeanUtil.copyProperties(item, emiExport);
                return emiExport;
            }).collect(Collectors.toList());
            PoiUtil.exportExcelWithStream("跨省执行地迁出.xls", EmiExport.class, collect);
        }
        if ("1".equals(param.getTransType())) {
            // 跨省执行地迁入
            List<ImmExport> collect = this.list(queryWrapper).stream().map(item -> {
                ImmExport immExport = new ImmExport();
                BeanUtil.copyProperties(item, immExport);
                return immExport;
            }).collect(Collectors.toList());
            PoiUtil.exportExcelWithStream("跨省执行地迁入.xls", ImmExport.class, collect);
        }
    }


}
