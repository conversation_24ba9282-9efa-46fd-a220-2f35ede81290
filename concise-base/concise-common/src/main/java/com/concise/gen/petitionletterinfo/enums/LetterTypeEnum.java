package com.concise.gen.petitionletterinfo.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 信访信息
 *
 * <AUTHOR>
 * @date 2024-04-02 14:11:20
 */
@Getter
public enum LetterTypeEnum {

    /**
     * 数据不存在
     */
    TYPE_01("01", "建议意见"),
    TYPE_02("02", "申诉"),
    TYPE_03("03", "求决"),
    TYPE_04("04", "检举控告"),
    TYPE_05("05", "咨询 "),
    TYPE_06("06", "其他");

    private final String code;

    private final String value;
        LetterTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }
    public static LetterTypeEnum getEnumByCode(String code){
        LetterTypeEnum[] enums = LetterTypeEnum.class.getEnumConstants();
        Optional<LetterTypeEnum> any = Arrays.stream(enums).filter(e -> e.getCode().equals(code) ).findAny();
        return any.orElse(LetterTypeEnum.TYPE_01);
    }

}
