package com.concise.gen.papermaintenance.enums;

import com.concise.common.annotion.ExpEnumType;
import com.concise.common.consts.SysExpEnumConstant;
import com.concise.common.exception.enums.abs.AbstractBaseExceptionEnum;
import com.concise.common.factory.ExpEnumCodeFactory;

/**
 * 量卷维护
 *
 * <AUTHOR>
 * @date 2025-03-20 15:02:55
 */
@ExpEnumType(module = SysExpEnumConstant.SNOWY_SYS_MODULE_EXP_CODE, kind = SysExpEnumConstant.SYS_POS_EXCEPTION_ENUM)
public enum PaperMaintenanceExceptionEnum implements AbstractBaseExceptionEnum {

    /**
     * 数据不存在
     */
    NOT_EXIST(1, "此数据不存在"), 
    
    /**
     * 题目列表不能为空
     */
    TOPIC_LIST_NOT_NULL(2, "题目列表不能为空"),
    
    /**
     * 量卷ID不能为空
     */
    PAPER_MAINTENANCE_ID_EMPTY(3, "量卷ID不能为空"),
    
    /**
     * 答题结果不能为空
     */
    ANSWER_LIST_EMPTY(4, "答题结果不能为空"),
    
    /**
     * 计算分数时发生错误
     */
    CALCULATE_SCORE_ERROR(5, "计算分数时发生错误");

    private final Integer code;

    private final String message;
        PaperMaintenanceExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return ExpEnumCodeFactory.getExpEnumCode(this.getClass(), code);
    }

    @Override
    public String getMessage() {
        return message;
    }

}
