package com.concise.gen.notice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.notice.entity.YwxtNotice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 协同信息通知表
 *
 * <AUTHOR>
 * @date 2023-12-08 11:38:39
 */
public interface YwxtNoticeMapper extends BaseMapper<YwxtNotice> {

    /**
     * 保存通知用户
     * @param noticeId 通知id
     * @param userIdList 用户id集合
     * @param noticeType 通知类型
     */
    void insertNoticeUserIdList(@Param("noticeId") String noticeId, @Param("noticeType") String noticeType, @Param("userIdList") List<String> userIdList);

    /**
     * 保存通知机构
     * @param noticeId 通知id
     * @param noticeType 通知类型
     * @param orgIdList 机构id集合
     */
    void insertNoticeOrgIdList(@Param("noticeId") String noticeId, @Param("noticeType") String noticeType, @Param("orgIdList") List<String> orgIdList);

    /**
     * 获取所有类型最新一条通知
     * @param id 用户id
     * @return YwxtNotice
     */
    List<YwxtNotice> getAllTypeLatestNoticeList(String id);
    /**
     * 获取最新通知
     * @param userId 用户id
     * @param size 数量
     * @return YwxtNotice
     */
    List<YwxtNotice> getLastNoticeList(String userId,int size);

    /**
     * 获取最新通知
     * @param orgId 机构id
     * @param size 数量
     * @return YwxtNotice
     */
    List<YwxtNotice> getLastNoticeListByOrgId(@Param("orgId") String orgId, @Param("size") int size);
}
