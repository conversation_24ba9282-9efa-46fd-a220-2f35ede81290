<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.disabledbasiccard.mapper.DisabledBasicCardMapper">

    <select id="page" resultType="com.concise.gen.disabledbasiccard.entity.DisabledBasicCard">
        select disabled_basic_card.*,correction_object_information.zhuangtai as zhuangtai
        from disabled_basic_card left join correction_object_information on disabled_basic_card.jzdx_id=correction_object_information.id
            ${ew.customSqlSegment}
    </select>
</mapper>
