package com.concise.gen.disabledbasiccard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.disabledbasiccard.entity.DisabledBasicCard;
import com.concise.gen.disabledbasiccard.param.DisabledBasicCardParam;
import java.util.List;
import java.util.Set;

/**
 * 残疾人信息service接口
 *
 * <AUTHOR>
 * @date 2024-01-09 11:23:58
 */
public interface DisabledBasicCardService extends IService<DisabledBasicCard> {

    /**
     * 查询残疾人信息
     *
     * <AUTHOR>
     * @date 2024-01-09 11:23:58
     */
    PageResult<DisabledBasicCard> page(DisabledBasicCardParam disabledBasicCardParam, Set<String> orgSet);

    /**
     * 残疾人信息列表
     *
     * <AUTHOR>
     * @date 2024-01-09 11:23:58
     */
    List<DisabledBasicCard> list(DisabledBasicCardParam disabledBasicCardParam);

    /**
     * 添加残疾人信息
     *
     * <AUTHOR>
     * @date 2024-01-09 11:23:58
     */
    void add(DisabledBasicCardParam disabledBasicCardParam);

    /**
     * 删除残疾人信息
     *
     * <AUTHOR>
     * @date 2024-01-09 11:23:58
     */
    void delete(DisabledBasicCardParam disabledBasicCardParam);

    /**
     * 编辑残疾人信息
     *
     * <AUTHOR>
     * @date 2024-01-09 11:23:58
     */
    void edit(DisabledBasicCardParam disabledBasicCardParam);

    /**
     * 查看残疾人信息
     *
     * <AUTHOR>
     * @date 2024-01-09 11:23:58
     */
     DisabledBasicCard detail(DisabledBasicCardParam disabledBasicCardParam);
}
