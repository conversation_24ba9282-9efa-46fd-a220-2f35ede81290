package com.concise.gen.outofcirclewarning.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.PoiUtil;
import com.concise.gen.outofcirclewarning.entity.OutOfCircleWarning;
import com.concise.gen.outofcirclewarning.enums.OutOfCircleWarningExceptionEnum;
import com.concise.gen.outofcirclewarning.mapper.OutOfCircleWarningMapper;
import com.concise.gen.outofcirclewarning.param.OutOfCircleWarningParam;
import com.concise.gen.outofcirclewarning.service.OutOfCircleWarningService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 出圈预警信息service接口实现类
 *
 * <AUTHOR>
 * @date 2023-07-21 17:39:55
 */
@Service
@DS("dataCenter")
public class OutOfCircleWarningServiceImpl extends ServiceImpl<OutOfCircleWarningMapper, OutOfCircleWarning> implements OutOfCircleWarningService {

    @Override
    public PageResult<OutOfCircleWarning> page(OutOfCircleWarningParam outOfCircleWarningParam) {
        QueryWrapper<OutOfCircleWarning> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(outOfCircleWarningParam)) {

            // 根据矫正对象id 查询
            if (ObjectUtil.isNotEmpty(outOfCircleWarningParam.getJzdxId())) {
                queryWrapper.lambda().eq(OutOfCircleWarning::getJzdxId, outOfCircleWarningParam.getJzdxId());
            }
            // 根据矫正对象姓名 查询
            if (ObjectUtil.isNotEmpty(outOfCircleWarningParam.getName())) {
                queryWrapper.lambda().like(OutOfCircleWarning::getName, outOfCircleWarningParam.getName().trim());
            }
            // 根据矫正单位id 查询
            if (ObjectUtil.isNotEmpty(outOfCircleWarningParam.getJzjg())) {
                queryWrapper.lambda().eq(OutOfCircleWarning::getJzjg, outOfCircleWarningParam.getJzjg());
            }
            // 根据身份证号 查询
            if (ObjectUtil.isNotEmpty(outOfCircleWarningParam.getSfzh())) {
                queryWrapper.lambda().eq(OutOfCircleWarning::getSfzh, outOfCircleWarningParam.getSfzh().trim());
            }
            // 根据预警时间 查询
            if (ObjectUtil.isAllNotEmpty(outOfCircleWarningParam.getSearchBeginTime(), outOfCircleWarningParam.getSearchEndTime())) {
                queryWrapper.apply("date_format (warning_time,'%Y-%m-%d') >= date_format('" + outOfCircleWarningParam.getSearchBeginTime() + "','%Y-%m-%d')")
                        .apply("date_format (warning_time,'%Y-%m-%d') <= date_format('" + outOfCircleWarningParam.getSearchEndTime() + "','%Y-%m-%d')");
            }
            // 根据是否出界 查询
            if (ObjectUtil.isNotEmpty(outOfCircleWarningParam.getOutOfBound())) {
                queryWrapper.lambda().eq(OutOfCircleWarning::getOutOfBound, outOfCircleWarningParam.getOutOfBound());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<OutOfCircleWarning> list(OutOfCircleWarningParam outOfCircleWarningParam) {
        return this.list();
    }

    @Override
    public void add(OutOfCircleWarningParam outOfCircleWarningParam) {
        OutOfCircleWarning outOfCircleWarning = new OutOfCircleWarning();
        BeanUtil.copyProperties(outOfCircleWarningParam, outOfCircleWarning);
        this.save(outOfCircleWarning);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(OutOfCircleWarningParam outOfCircleWarningParam) {
        this.removeById(outOfCircleWarningParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(OutOfCircleWarningParam outOfCircleWarningParam) {
        OutOfCircleWarning outOfCircleWarning = this.queryOutOfCircleWarning(outOfCircleWarningParam);
        BeanUtil.copyProperties(outOfCircleWarningParam, outOfCircleWarning);
        this.updateById(outOfCircleWarning);
    }

    @Override
    public OutOfCircleWarning detail(OutOfCircleWarningParam outOfCircleWarningParam) {
        return this.queryOutOfCircleWarning(outOfCircleWarningParam);
    }

    @Override
    public void export(OutOfCircleWarningParam outOfCircleWarningParam) {
        QueryWrapper<OutOfCircleWarning> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(outOfCircleWarningParam)) {

            // 根据矫正对象id 查询
            if (ObjectUtil.isNotEmpty(outOfCircleWarningParam.getJzdxId())) {
                queryWrapper.lambda().eq(OutOfCircleWarning::getJzdxId, outOfCircleWarningParam.getJzdxId());
            }
            // 根据矫正对象姓名 查询
            if (ObjectUtil.isNotEmpty(outOfCircleWarningParam.getName())) {
                queryWrapper.lambda().like(OutOfCircleWarning::getName, outOfCircleWarningParam.getName().trim());
            }
            // 根据矫正单位id 查询
            if (ObjectUtil.isNotEmpty(outOfCircleWarningParam.getJzjg())) {
                queryWrapper.lambda().eq(OutOfCircleWarning::getJzjg, outOfCircleWarningParam.getJzjg());
            }
            // 根据身份证号 查询
            if (ObjectUtil.isNotEmpty(outOfCircleWarningParam.getSfzh())) {
                queryWrapper.lambda().eq(OutOfCircleWarning::getSfzh, outOfCircleWarningParam.getSfzh().trim());
            }
            // 根据预警时间 查询
            if (ObjectUtil.isAllNotEmpty(outOfCircleWarningParam.getSearchBeginTime(), outOfCircleWarningParam.getSearchEndTime())) {
                queryWrapper.apply("date_format (warning_time,'%Y-%m-%d') >= date_format('" + outOfCircleWarningParam.getSearchBeginTime() + "','%Y-%m-%d')")
                        .apply("date_format (warning_time,'%Y-%m-%d') <= date_format('" + outOfCircleWarningParam.getSearchEndTime() + "','%Y-%m-%d')");
            }
            // 根据是否出界 查询
            if (ObjectUtil.isNotEmpty(outOfCircleWarningParam.getOutOfBound())) {
                queryWrapper.lambda().eq(OutOfCircleWarning::getOutOfBound, outOfCircleWarningParam.getOutOfBound());
            }
        }
        List<OutOfCircleWarning> list = this.list(queryWrapper);
        PoiUtil.exportExcelWithStream("OutOfCircleWarning.xls", OutOfCircleWarning.class, list);
    }

    /**
     * 获取出圈预警信息
     *
     * <AUTHOR>
     * @date 2023-07-21 17:39:55
     */
    private OutOfCircleWarning queryOutOfCircleWarning(OutOfCircleWarningParam outOfCircleWarningParam) {
        OutOfCircleWarning outOfCircleWarning = this.getById(outOfCircleWarningParam.getId());
        if (ObjectUtil.isNull(outOfCircleWarning)) {
            throw new ServiceException(OutOfCircleWarningExceptionEnum.NOT_EXIST);
        }
        return outOfCircleWarning;
    }
}
