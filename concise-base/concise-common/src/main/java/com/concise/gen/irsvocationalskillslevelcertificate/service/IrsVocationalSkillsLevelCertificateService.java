package com.concise.gen.irsvocationalskillslevelcertificate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.irsvocationalskillslevelcertificate.entity.IrsVocationalSkillsLevelCertificate;
import com.concise.gen.irsvocationalskillslevelcertificate.param.IrsVocationalSkillsLevelCertificateParam;

import java.util.List;
import java.util.Set;

/**
 * 职业技能等级证书service接口
 *
 * <AUTHOR>
 * @date 2024-01-11 09:21:53
 */
public interface IrsVocationalSkillsLevelCertificateService extends IService<IrsVocationalSkillsLevelCertificate> {

    /**
     * 查询职业技能等级证书
     *
     * <AUTHOR>
     * @date 2024-01-11 09:21:53
     */
    PageResult<IrsVocationalSkillsLevelCertificate> page(IrsVocationalSkillsLevelCertificateParam irsVocationalSkillsLevelCertificateParam, Set<String> orgIds);

    /**
     * 职业技能等级证书列表
     *
     * <AUTHOR>
     * @date 2024-01-11 09:21:53
     */
    List<IrsVocationalSkillsLevelCertificate> list(IrsVocationalSkillsLevelCertificateParam irsVocationalSkillsLevelCertificateParam);

    /**
     * 添加职业技能等级证书
     *
     * <AUTHOR>
     * @date 2024-01-11 09:21:53
     */
    void add(IrsVocationalSkillsLevelCertificateParam irsVocationalSkillsLevelCertificateParam);

    /**
     * 删除职业技能等级证书
     *
     * <AUTHOR>
     * @date 2024-01-11 09:21:53
     */
    void delete(IrsVocationalSkillsLevelCertificateParam irsVocationalSkillsLevelCertificateParam);

    /**
     * 编辑职业技能等级证书
     *
     * <AUTHOR>
     * @date 2024-01-11 09:21:53
     */
    void edit(IrsVocationalSkillsLevelCertificateParam irsVocationalSkillsLevelCertificateParam);

    /**
     * 查看职业技能等级证书
     *
     * <AUTHOR>
     * @date 2024-01-11 09:21:53
     */
     IrsVocationalSkillsLevelCertificate detail(IrsVocationalSkillsLevelCertificateParam irsVocationalSkillsLevelCertificateParam);


}
