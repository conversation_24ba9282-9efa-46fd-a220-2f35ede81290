package com.concise.gen.dataCenter.orgDc.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.consts.SymbolConstant;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.dataCenter.orgDc.entity.OrgDc;
import com.concise.gen.dataCenter.orgDc.mapper.OrgDcMapper;
import com.concise.gen.dataCenter.orgDc.param.OrgDcParam;
import com.concise.gen.dataCenter.orgDc.service.OrgDcService;
import com.concise.gen.dataCenter.orgDc.vo.CcgfUser;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据中心机构表service接口实现类
 *
 * <AUTHOR>
 * @date 2023-11-23 16:15:33
 */
@Service
@DS("dataCenter")
public class OrgDcServiceImpl extends ServiceImpl<OrgDcMapper, OrgDc> implements OrgDcService {

    public static final String ROOT_ID = "0";
    @Override
    public PageResult<OrgDc> page(OrgDcParam orgDcParam) {
        QueryWrapper<OrgDc> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(orgDcParam)) {

            // 根据父id 查询
            if (ObjectUtil.isNotEmpty(orgDcParam.getPid())) {
                queryWrapper.lambda().eq(OrgDc::getPid, orgDcParam.getPid());
            }
            // 根据父ids 查询
            if (ObjectUtil.isNotEmpty(orgDcParam.getPids())) {
                queryWrapper.lambda().eq(OrgDc::getPids, orgDcParam.getPids());
            }
            // 根据名称 查询
            if (ObjectUtil.isNotEmpty(orgDcParam.getName())) {
                queryWrapper.lambda().eq(OrgDc::getName, orgDcParam.getName());
            }
            // 根据编码 查询
            if (ObjectUtil.isNotEmpty(orgDcParam.getCode())) {
                queryWrapper.lambda().eq(OrgDc::getCode, orgDcParam.getCode());
            }
            // 根据排序 查询
            if (ObjectUtil.isNotEmpty(orgDcParam.getSort())) {
                queryWrapper.lambda().eq(OrgDc::getSort, orgDcParam.getSort());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(orgDcParam.getType())) {
                queryWrapper.lambda().eq(OrgDc::getType, orgDcParam.getType());
            }
            // 根据描述 查询
            if (ObjectUtil.isNotEmpty(orgDcParam.getRemark())) {
                queryWrapper.lambda().eq(OrgDc::getRemark, orgDcParam.getRemark());
            }
            // 根据状态（字典 0正常 1停用 2删除） 查询
            if (ObjectUtil.isNotEmpty(orgDcParam.getStatus())) {
                queryWrapper.lambda().eq(OrgDc::getStatus, orgDcParam.getStatus());
            }
            // 根据机构完整路径 查询
            if (ObjectUtil.isNotEmpty(orgDcParam.getFullName())) {
                queryWrapper.lambda().eq(OrgDc::getFullName, orgDcParam.getFullName());
            }
            if (ObjectUtil.isNotEmpty(orgDcParam.getLevel())) {
                queryWrapper.lambda().eq(OrgDc::getLevel, orgDcParam.getLevel());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(orgDcParam.getAdcode())) {
                queryWrapper.lambda().eq(OrgDc::getAdcode, orgDcParam.getAdcode());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }


    @Cacheable(cacheNames = "getOrgCodeById",key = "#id")
    @Override
    public String getOrgCodeById(String id) {
        OrgDc byId = this.getById(id);
        if (byId != null) {
            return byId.getCode();
        }
        return "";
    }

    @Override
    public List<CcgfUser> getZwddUserList(String orgId) {
        return this.baseMapper.getZwddIds(orgId);
    }

    @Override
    public String getIds(OrgDc org) {
        return getPids(org) + getWrappedId(org.getId());
    }


    private String getPids(OrgDc org){
        String pids = org.getPids();
        if (!pids.contains(getWrappedId(ROOT_ID))) {
            // 需要获取上级重新拼接
            OrgDc parent = this.getById(org.getPid());
            if (parent != null) {
                org.setPids(getPids(parent)+ getWrappedId(parent.getId()));
            }else {
                org.setPids(getWrappedId(ROOT_ID));
            }
            this.updateById(org);
        }
        return org.getPids();
    }

    private String getWrappedId(String id){
        return SymbolConstant.LEFT_SQUARE_BRACKETS + id + SymbolConstant.RIGHT_SQUARE_BRACKETS + SymbolConstant.COMMA;
    }
}
