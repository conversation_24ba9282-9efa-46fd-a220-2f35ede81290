package com.concise.modular.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.StringJoiner;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.concise.common.annotion.BusinessLog;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.exception.ServiceException;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.param.SysFileInfoParam;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.response.ErrorResponseData;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.common.util.LibreOfficeUtil;
import com.concise.common.util.PoiUtil;
import com.concise.core.context.login.LoginContext;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.gen.holidays.service.SysHolidayService;
import com.concise.gen.investigation.entity.InvestigationGroup;
import com.concise.gen.investigation.entity.InvestigationHistoryField;
import com.concise.gen.investigation.entity.InvestigationInfo;
import com.concise.gen.investigation.entity.InvestigationTranscript;
import com.concise.gen.investigation.mapper.InvestigationGroupMapper;
import com.concise.gen.investigation.param.InvestigationApprovalParam;
import com.concise.gen.investigation.param.InvestigationDeliberationParam;
import com.concise.gen.investigation.param.InvestigationFeedbackParam;
import com.concise.gen.investigation.param.InvestigationGroupParam;
import com.concise.gen.investigation.param.InvestigationInfoParam;
import com.concise.gen.investigation.param.InvestigationRecordParam;
import com.concise.gen.investigation.param.InvestigationReviewParam;
import com.concise.gen.investigation.param.InvestigationTranscriptParam;
import com.concise.gen.investigation.service.InvestigationDeliberationService;
import com.concise.gen.investigation.service.InvestigationFeedbackService;
import com.concise.gen.investigation.service.InvestigationGroupService;
import com.concise.gen.investigation.service.InvestigationInfoService;
import com.concise.gen.investigation.service.InvestigationReviewService;
import com.concise.gen.investigation.service.InvestigationTranscriptService;
import com.concise.gen.investigationdelay.param.InvestigationDelayParam;
import com.concise.gen.investigationdelay.service.InvestigationDelayService;
import com.concise.gen.investigationinfoflow.service.InvestigationInfoFlowService;
import com.concise.gen.investigationsign.entity.InvestigationSign;
import com.concise.gen.investigationsign.service.InvestigationSignService;
import com.concise.gen.papermaintenance.param.PaperMaintenanceParam;
import com.concise.gen.papertopic.param.PaperTopicParam;
import com.concise.gen.papertopicitem.param.PaperTopicItemParam;
import com.concise.modular.util.WordUtil;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.service.SysOrgService;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 调查评估信息表控制器
 *
 * <AUTHOR>
 * @date 2025-03-20 10:30:38
 */
@Slf4j
@Api(tags = "调查评估信息表")
@RestController
public class InvestigationInfoController {

    @Resource
    private InvestigationGroupService investigationGroupService;
    @Resource
    private InvestigationInfoService investigationInfoService;
    @Resource
    private InvestigationTranscriptService investigationTranscriptService;
    @Resource
    private InvestigationReviewService investigationReviewService;
    @Resource
    private SysOrgService sysOrgService;
    @Resource
    private InvestigationInfoFlowService investigationInfoFlowService;
    @Resource
    private InvestigationFeedbackService investigationFeedbackService;

    @Resource
    private SysFileInfoService sysFileInfoService;
    @Value("${pythonTool.pdfToWord}")
    private String pdfToWordUrl;

    @Value("${electronicSignature.bl_bgr}")
    private String bl_bgr;

    @Value("${electronicSignature.bl_bhr}")
    private String bl_bhr;

    // @Value("${electronicSignature.bl_csgb}")
    private String bl_csgb;

    // @Value("${electronicSignature.bl_fjsbzrhjs}")
    private String bl_fjsbzrhjs;

    // @Value("${electronicSignature.bl_jsbzrhjs}")
    private String bl_jsbzrhjs;

    @Value("${electronicSignature.dcpgyjsPath}")
    private String dcpgyjsPath;

    @Value("${electronicSignature.noticePath}")
    private String noticePath;

    @Resource
    private InvestigationDeliberationService investigationDeliberationService;

    @Resource
    private SysHolidayService sysHolidayService;
    @Resource
    private InvestigationGroupMapper investigationGroupMapper;
    @Value("${spring.profiles.active}")
    private String active;

    @Resource
    private InvestigationSignService investigationSignService;

    @Resource
    private InvestigationDelayService investigationDelayService;

    /**
     * 查询调查评估信息表
     *
     * <AUTHOR>
     * @date 2025-03-20 10:30:38
     */
    @GetMapping("/investigationInfo/page")
    @ApiOperation("调查评估信息表_分页查询")
    public ResponseData page(InvestigationInfoParam param) {
        param.setReceiveDeptId(sysOrgService.getSearchOrgId(param.getReceiveDeptId()));
        return new SuccessResponseData(investigationInfoService.page(param));
    }

    /**
     * 编辑调查评估信息表
     *
     * <AUTHOR>
     * @date 2025-03-20 10:30:38
     */
    @PostMapping("/investigationInfo/accept")
    @ApiOperation("调查评估_接收")
    @BusinessLog(title = "调查评估_接收", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData accept(@RequestBody @Validated(InvestigationInfoParam.edit.class) InvestigationInfoParam param) {
        LoginContext loginContext = LoginContextHolder.me();
        param.setApprovalPsn(loginContext.getSysLoginUser().getName());
        param.setApprovalPsnId(loginContext.getSysLoginUserId());
        if (param.getId().length() < 21) {
            //id小于21，则为页面新增，将接收单位设置为登录人所在机构
            SysOrg org = sysOrgService.getById(param.getApprovalDeptId());
            if (null != org) {
                if ("sp".equals(org.getType())) {
                    org = sysOrgService.getById(org.getPid());
                }
                param.setSName(org.getAddress());
                //手动新增的设置接收单位
                param.setReceiveDeptName(org.getName());
                param.setReceiveDeptId(org.getId());
                param.setReceiveDeptPids(org.getPids());
            }
        } else {
            //一体化接收
            SysOrg org = sysOrgService.getById(param.getReceiveDeptId());
            if (null != org) {
                param.setSName(org.getAddress());
            }
        }
        return new SuccessResponseData(investigationInfoService.accept(param));
    }

    @PostMapping("/investigationInfo/publishNotice")
    @ApiOperation("调查评估_小组公告")
    @BusinessLog(title = "调查评估_小组公告", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData publishNotice(@RequestBody @Validated(InvestigationGroupParam.edit.class) InvestigationGroupParam param) {
        LoginContext loginContext = LoginContextHolder.me();
        param.setApprovalPsn(loginContext.getSysLoginUser().getName());
        param.setApprovalPsnId(loginContext.getSysLoginUserId());
        investigationInfoService.publishNotice(param);
        return new SuccessResponseData();
    }


    @GetMapping("/investigationInfo/transcriptList")
    @ApiOperation("调查评估_笔录列表")
    public ResponseData transcriptList(String id) {
        if (id == null) {
            return ResponseData.error("id不能为空");
        }
        InvestigationInfo investigationInfo = investigationInfoService.getById(id);
        if (investigationInfo == null) {
            return ResponseData.error("id不存在");
        }
        investigationTranscriptService.genEmptyTranscript(investigationInfo);
        return new SuccessResponseData(investigationTranscriptService.lambdaQuery().eq(InvestigationTranscript::getPid, id).eq(InvestigationTranscript::getDeleted, 0).list());
    }

    /**
     * 调查评估_手动新增笔录
     *
     * @param investigationTranscriptParam
     * @return
     */
    @PostMapping("/investigationInfo/transcriptAdd")
    @ApiOperation("调查评估_手动新增笔录")
    @BusinessLog(title = "调查评估_手动新增笔录", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData transcriptAdd(@RequestBody @Validated(InvestigationTranscriptParam.add.class) InvestigationTranscriptParam investigationTranscriptParam) {
        investigationTranscriptService.transcriptAdd(investigationTranscriptParam);
        return new SuccessResponseData();
    }

    @PostMapping("/investigationInfo/transcriptCommit")
    @ApiOperation("调查评估_笔录提交")
    @BusinessLog(title = "调查评估_笔录提交", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData transcriptCommit(@RequestBody InvestigationTranscriptParam investigationTranscriptParam) {
        //系统填写
        investigationTranscriptParam.setTag(1);
        InvestigationTranscript tt = investigationTranscriptService.fillAndSaveTranscript(investigationTranscriptParam);
        if (null != tt && 9 == tt.getTag()) {
            //判断之前有无生成的文书笔录，已有则不生成，需页面重新制作， 移动端提交时会重新生成，因为得签字才提交
            LambdaQueryWrapper<SysFileInfo> fileWrapper = new LambdaQueryWrapper<>();
            fileWrapper.eq(SysFileInfo::getDelFlag, 0)
                    .eq(SysFileInfo::getBizId, investigationTranscriptParam.getPid())
                    .eq(SysFileInfo::getBlId, investigationTranscriptParam.getId())
                    .eq(SysFileInfo::getBizType, "BLLX");
            if (sysFileInfoService.count(fileWrapper) == 0) {
                //笔录第一次完成时初始化笔录文书 并上传至Oss
                initTranscript(tt.getId(), "1", null);
            };
        }
        LambdaQueryWrapper<SysFileInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SysFileInfo::getBizId, tt.getPid());
        lambdaQueryWrapper.eq(SysFileInfo::getBizType, "BLLX");
        lambdaQueryWrapper.eq(SysFileInfo::getDelFlag, 0);
        lambdaQueryWrapper.orderByAsc(SysFileInfo::getCreateTime);
        return new SuccessResponseData(sysFileInfoService.list(lambdaQueryWrapper));
    }

    /**
     * 调查评估_根据id查询笔录
     *
     * @param id
     */
    @GetMapping("/investigationInfo/getTranscriptById")
    @ApiOperation("调查评估_根据id查询笔录")
    public ResponseData getTranscriptById(@RequestParam String id) {
        return new SuccessResponseData(investigationTranscriptService.getTranscriptById(id));
    }

    /**
     * 调查评估_删除笔录
     *
     * @param param
     */
    @PostMapping("/investigationInfo/transcriptDelete")
    @ApiOperation("调查评估_删除笔录")
    @BusinessLog(title = "调查评估_删除笔录", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData transcriptDelete(@RequestBody @Validated(InvestigationTranscriptParam.delete.class) InvestigationTranscriptParam param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            return new ErrorResponseData(400, "id为空，请选择要删除的数据");
        }
        List<String> idList = Arrays.asList(param.getId().split(","));
        String pid = "";
        for (String id : idList) {
            try {
                LambdaUpdateWrapper<InvestigationTranscript> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.eq(InvestigationTranscript::getId, id);
                lambdaUpdateWrapper.set(InvestigationTranscript::getDeleted, 1);
                investigationTranscriptService.update(lambdaUpdateWrapper);
                InvestigationTranscript investigationTranscript = investigationTranscriptService.getById(id);
                pid = investigationTranscript.getPid();
                investigationTranscriptService.updateMainTableTranscriptCount(investigationTranscript.getPid());
                //重新生成意见
                String opinion = investigationTranscriptService.createOpinionString(investigationTranscript.getPid());
                //更新小组调查意见
                InvestigationGroup investigationGroup = investigationGroupMapper.selectById(investigationTranscript.getPid());
                investigationGroup.setOpinion(opinion);
                investigationGroupMapper.updateById(investigationGroup);
                //同步删除附件表的笔录文书数据
                //InvestigationTranscript tt = investigationTranscriptService.getById(id);
                //if (null != tt && "2".equals(tt.getProgress())) {
                    sysFileInfoService.lambdaUpdate()
                            .set(SysFileInfo::getDelFlag, 1)
                            .eq(SysFileInfo::getBlId, id)
                            .update();
                //}
            } catch (Exception e) {
                log.error("删除笔录失败", e);
            }
        }
        LambdaQueryWrapper<SysFileInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SysFileInfo::getBizId, pid);
        lambdaQueryWrapper.eq(SysFileInfo::getBizType, "BLLX");
        lambdaQueryWrapper.eq(SysFileInfo::getDelFlag, 0);
        lambdaQueryWrapper.orderByAsc(SysFileInfo::getCreateTime);
        return new SuccessResponseData(sysFileInfoService.list(lambdaQueryWrapper));
    }

    /**
     * 调查评估_根据id获取笔录评估类型
     *
     * @param id
     * @return
     */
    @GetMapping("/investigationInfo/getTranscriptTypeById")
    @ApiOperation("调查评估_根据id获取笔录评估类型")
    public ResponseData getTranscriptTypeById(@RequestParam String id) {
        return new SuccessResponseData(investigationTranscriptService.getTranscriptTypeById(id));
    }

    /**
     * 调查评估_根据笔录id获取回填信息·
     */
    @GetMapping("/investigationInfo/getTranscriptFillInfoById")
    @ApiOperation("调查评估_根据笔录id获取回填信息")
    public ResponseData getTranscriptFillInfoById(@RequestParam String id) {
        InvestigationTranscript investigationTranscript = investigationTranscriptService.getById(id);
        if (investigationTranscript == null) {
            throw new ServiceException(500, "笔录不存在");
        }
        return new SuccessResponseData(investigationTranscriptService.getTranscriptFillInfo(investigationTranscript));
    }


    /**
     * 调查评估_下载笔录（同时填充对象和题目）
     * 该方法使用新的组合方法导出笔录
     *
     * @param id       笔录ID
     * @param response HTTP响应对象
     */
    @GetMapping("/investigationInfo/transcriptDownload")
    @ApiOperation("调查评估_下载笔录（同时填充对象和题目）")
    public void transcriptDownload(@RequestParam String id, @RequestParam(required = false) Integer type, HttpServletResponse response) {
        // 笔录下载
        InvestigationTranscript investigationTranscript = investigationTranscriptService.getById(id);
        if (investigationTranscript == null) {
            throw new ServiceException(500, "笔录不存在");
        }
        // 获取笔录内容
        String context = investigationTranscript.getContext();
        if (context == null) {
            throw new ServiceException(500, "笔录内容为空");
        }
        // 转换为PaperMaintenance对象
        PaperMaintenanceParam paperMaintenance = JSON.parseObject(context, PaperMaintenanceParam.class);

        //基本信息转换
        InvestigationRecordParam investigationRecordParam = JSON.parseObject(investigationTranscript.getBasicInfo(), InvestigationRecordParam.class);
        // 获取题目列表
        List<PaperTopicParam> topicParamList = paperMaintenance.getTopicList();

        if (topicParamList == null || topicParamList.isEmpty()) {
            throw new RuntimeException("请提供题目列表");
        }

        //根据笔录类型来选择模板
        String templatePath = null;
        //判断是否假释
        boolean sfjs = false;
        InvestigationInfo investigationInfo = investigationInfoService.getById(investigationTranscript.getPid());
        // 添加空值检查，防止空指针异常
        if (investigationInfo != null && "3".equals(investigationInfo.getCorrectionType())) {
            sfjs = true;
        }
        switch (investigationTranscript.getPaperType()) {
            //被告人
            case "BLLX06":
                templatePath = bl_bgr;
                break;
            //其他所有类型都使用bl_bhr模板
            default:
                templatePath = bl_bhr;
                break;
        }

        // 将题目转换为问答格式
        List<Map<String, Object>> questionAnswerList = new ArrayList<>();
        for (PaperTopicParam topicParam : topicParamList) {
            Map<String, Object> qaItem = new HashMap<>();
            qaItem.put("question", topicParam.getTopicName());
            qaItem.put("answer", ""); // 留空表示显示下划线

            // 添加控制空间的参数，当type不为空时减少空行
            qaItem.put("reduceSpace", type != null);

            // 只有当type为空时才填充选项
            if (type == null) {
                // 处理选项
                List<String> options = new ArrayList<>();
                List<String> optionIds = new ArrayList<>(); // 存储选项ID
                if (topicParam.getItemList() != null && !topicParam.getItemList().isEmpty()) {
                    for (PaperTopicItemParam item : topicParam.getItemList()) {
                        options.add(item.getContent());
                        optionIds.add(item.getId()); // 添加选项ID
                    }
                }
                qaItem.put("options", options);
                qaItem.put("optionIds", optionIds);
            } else {
                // 当type不为空时，添加空的选项列表
                qaItem.put("options", new ArrayList<>());
                qaItem.put("optionIds", new ArrayList<>());
            }

            // 备注
            qaItem.put("remark", topicParam.getRemark());

            // 用户回答 - 只有当用户回答非空时才添加
            if (topicParam.getUserAnswer() != null && !topicParam.getUserAnswer().isEmpty()) {
                qaItem.put("userAnswer", topicParam.getUserAnswer());
            }

            // 用户选择 - 只有当用户选择非空时才添加
            if (topicParam.getUserSelectId() != null && !topicParam.getUserSelectId().isEmpty()) {
                // 将选择的ID转换为选项内容的列表
                List<String> selectedContents = new ArrayList<>();
                String[] selectedIds = topicParam.getUserSelectId().split(",");

                if (topicParam.getItemList() != null && !topicParam.getItemList().isEmpty()) {
                    // 根据ID查找选项内容
                    for (String selectedId : selectedIds) {
                        for (PaperTopicItemParam item : topicParam.getItemList()) {
                            if (selectedId.equals(item.getId())) {
                                selectedContents.add(item.getContent());
                                break;
                            }
                        }
                    }
                }

                // 处理用户选择内容
                if (!selectedContents.isEmpty()) {
                    // 检查题目类型，单选题直接使用第一个选项，多选题才用逗号拼接
                    if (topicParam.getTopicType() != null && "1".equals(topicParam.getTopicType())) {
                        // 单选题，只取第一个选项
                        qaItem.put("userSelectId", selectedContents.get(0));
                    } else {
                        // 多选题或其他类型，使用逗号拼接
                        qaItem.put("userSelectId", String.join(",", selectedContents));
                    }
                } else {
                    // 如果没有找到对应内容，则保留原始ID
                    qaItem.put("userSelectId", topicParam.getUserSelectId());
                }
            }

            questionAnswerList.add(qaItem);
        }

        // 调用工具类生成PDF文档
        String paperTitle = paperMaintenance.getBlTypeName() != null ? paperMaintenance.getBlTypeName() : "自定义问答题";
        String fileName = investigationTranscript.getTitle();

        try {
            // 调用新的组合方法，同时填充对象和题目，并转换为PDF
            WordUtil.exportQuestionsWithAnswersAndObjectAsPdf(response, fileName, templatePath, paperTitle, questionAnswerList, investigationRecordParam);

            log.debug("导出笔录PDF成功，同时填充了对象和题目");
        } catch (Exception e) {
            log.error("导出笔录PDF失败", e);
            throw new ServiceException(500, "导出笔录PDF失败：" + e.getMessage());
        }
    }

    /**
     * 调查评估_下载笔录Word版本
     * 该方法使用新的组合方法导出笔录Word版本
     *
     * @param id       笔录ID
     * @param response HTTP响应对象
     */
    @GetMapping("/investigationInfo/transcriptDownloadWord")
    @ApiOperation("调查评估_下载笔录Word版本")
    public void transcriptDownloadWord(@RequestParam String id, @RequestParam(required = false) Integer type, HttpServletResponse response) {
        // 笔录下载
        InvestigationTranscript investigationTranscript = investigationTranscriptService.getById(id);
        if (investigationTranscript == null) {
            throw new ServiceException(500, "笔录不存在");
        }
        // 获取笔录内容
        String context = investigationTranscript.getContext();
        if (context == null) {
            throw new ServiceException(500, "笔录内容为空");
        }
        // 转换为PaperMaintenance对象
        PaperMaintenanceParam paperMaintenance = JSON.parseObject(context, PaperMaintenanceParam.class);

        //基本信息转换
        InvestigationRecordParam investigationRecordParam = JSON.parseObject(investigationTranscript.getBasicInfo(), InvestigationRecordParam.class);
        // 获取题目列表
        List<PaperTopicParam> topicParamList = paperMaintenance.getTopicList();

        if (topicParamList == null || topicParamList.isEmpty()) {
            throw new RuntimeException("请提供题目列表");
        }

        //根据笔录类型来选择模板
        String templatePath = null;
        //判断是否假释
        boolean sfjs = false;
        InvestigationInfo investigationInfo = investigationInfoService.getById(investigationTranscript.getPid());
        // 添加空值检查，防止空指针异常
        if (investigationInfo != null && "3".equals(investigationInfo.getCorrectionType())) {
            sfjs = true;
        }
        switch (investigationTranscript.getPaperType()) {
            //被告人
            case "BLLX06":
                templatePath = bl_bgr;
                break;
            //其他所有类型都使用bl_bhr模板
            default:
                templatePath = bl_bhr;
                break;
        }

        // 将题目转换为问答格式
        List<Map<String, Object>> questionAnswerList = new ArrayList<>();
        for (PaperTopicParam topicParam : topicParamList) {
            Map<String, Object> qaItem = new HashMap<>();
            qaItem.put("question", topicParam.getTopicName());
            qaItem.put("answer", ""); // 留空表示显示下划线

            // 添加控制空间的参数，当type不为空时减少空行
            qaItem.put("reduceSpace", type != null);

            // 只有当type为空时才填充选项
            if (type == null) {
                // 处理选项
                List<String> options = new ArrayList<>();
                List<String> optionIds = new ArrayList<>(); // 存储选项ID
                if (topicParam.getItemList() != null && !topicParam.getItemList().isEmpty()) {
                    for (PaperTopicItemParam item : topicParam.getItemList()) {
                        options.add(item.getContent());
                        optionIds.add(item.getId()); // 添加选项ID
                    }
                }
                qaItem.put("options", options);
                qaItem.put("optionIds", optionIds);
            } else {
                // 当type不为空时，添加空的选项列表
                qaItem.put("options", new ArrayList<>());
                qaItem.put("optionIds", new ArrayList<>());
            }

            // 备注
            qaItem.put("remark", topicParam.getRemark());

            // 用户回答 - 只有当用户回答非空时才添加
            if (topicParam.getUserAnswer() != null && !topicParam.getUserAnswer().isEmpty()) {
                qaItem.put("userAnswer", topicParam.getUserAnswer());
            }

            // 用户选择 - 只有当用户选择非空时才添加
            if (topicParam.getUserSelectId() != null && !topicParam.getUserSelectId().isEmpty()) {
                // 将选择的ID转换为选项内容的列表
                List<String> selectedContents = new ArrayList<>();
                String[] selectedIds = topicParam.getUserSelectId().split(",");

                if (topicParam.getItemList() != null && !topicParam.getItemList().isEmpty()) {
                    // 根据ID查找选项内容
                    for (String selectedId : selectedIds) {
                        for (PaperTopicItemParam item : topicParam.getItemList()) {
                            if (selectedId.equals(item.getId())) {
                                selectedContents.add(item.getContent());
                                break;
                            }
                        }
                    }
                }

                // 处理用户选择内容
                if (!selectedContents.isEmpty()) {
                    // 检查题目类型，单选题直接使用第一个选项，多选题才用逗号拼接
                    if (topicParam.getTopicType() != null && "1".equals(topicParam.getTopicType())) {
                        // 单选题，只取第一个选项
                        qaItem.put("userSelectId", selectedContents.get(0));
                    } else {
                        // 多选题或其他类型，使用逗号拼接
                        qaItem.put("userSelectId", String.join(",", selectedContents));
                    }
                } else {
                    // 如果没有找到对应内容，则保留原始ID
                    qaItem.put("userSelectId", topicParam.getUserSelectId());
                }
            }

            questionAnswerList.add(qaItem);
        }

        // 调用工具类生成Word文档
        String paperTitle = paperMaintenance.getBlTypeName() != null ? paperMaintenance.getBlTypeName() : "自定义问答题";
        String fileName = investigationTranscript.getTitle();

        try {
            // 调用导出Word方法，同时填充对象和题目
            WordUtil.exportQuestionsWithAnswersAndObjectAsWord(response, fileName, templatePath, paperTitle, questionAnswerList, investigationRecordParam);

            log.debug("导出笔录Word成功，同时填充了对象和题目");
        } catch (Exception e) {
            log.error("导出笔录Word失败", e);
            throw new ServiceException(500, "导出笔录Word失败：" + e.getMessage());
        }
    }

    @PostMapping("/investigationInfo/groupInvestigateAccept")
    @ApiOperation("调查评估_调查接收")
    @BusinessLog(title = "调查评估_调查接收", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData groupInvestigateAccept(@RequestBody @Validated(InvestigationGroupParam.edit.class) InvestigationGroupParam param) {
        LoginContext loginContext = LoginContextHolder.me();
        param.setApprovalPsn(loginContext.getSysLoginUser().getName());
        param.setApprovalPsnId(loginContext.getSysLoginUserId());
        investigationInfoService.groupInvestigateAccept(param);
        return new SuccessResponseData();
    }

    @PostMapping("/investigationInfo/groupInvestigateList")
    @ApiOperation("调查评估_管理评估清单")
    @BusinessLog(title = "调查评估_管理评估清单", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData groupInvestigateList(@RequestBody @Validated(InvestigationGroupParam.edit.class) InvestigationGroupParam param) {
        LoginContext loginContext = LoginContextHolder.me();
        param.setApprovalPsn(loginContext.getSysLoginUser().getName());
        param.setApprovalPsnId(loginContext.getSysLoginUserId());
        if (null != param.getTag() && 2 == param.getTag()) {
            //手动上传的笔录
            if (ObjectUtil.isEmpty(param.getBlFileIds()) && !"1".equals(param.getIsDraft())) {
                return new ErrorResponseData(400, "未上传笔录附件");
            }
            InvestigationTranscriptParam investigationTranscriptParam = new InvestigationTranscriptParam();
            investigationTranscriptParam.setId(param.getTranscriptId());
            investigationTranscriptParam.setPid(param.getId());
            investigationTranscriptParam.setTag(2);
            investigationTranscriptParam.setContext(param.getContext());
            investigationTranscriptParam.setBlFileIds(param.getBlFileIds());
            //手动上传的笔录，一起保存
            investigationTranscriptService.saveTranscriptHandMode(investigationTranscriptParam, param.getConclusion(), param.getIsDraft());
        }
        /*if (null != param.getTag() && 1 == param.getTag()) {
            //TODO 系统制作的笔录，更新调查评估笔录文书  trip: 笔录文书制作时同步就绑定业务数据了，暂存和提交先忽略

        }*/
        if ("1".equals(param.getIsDraft())) {
            //暂存
            investigationInfoService.lambdaUpdate()
                    .set(InvestigationInfo::getTranscriptTag, param.getTag())
                    .eq(InvestigationInfo::getId, param.getId())
                    .update();
            //暂存只保存手动笔录
            //保存附件（系统制作有附件）
            if (ObjectUtil.isNotEmpty(param.getOtherFiles())) {
                LambdaQueryWrapper<SysFileInfo> fileInfoWrapper = new LambdaQueryWrapper<>();
                fileInfoWrapper.eq(SysFileInfo::getBizId, param.getId());
                fileInfoWrapper.eq(SysFileInfo::getBizType, "inve_pgqd");
                fileInfoWrapper.notIn(SysFileInfo::getId, param.getOtherFiles().split(","));
                sysFileInfoService.remove(fileInfoWrapper);
            }
            sysFileInfoService.setBiz(param.getOtherFiles(), param.getId(), "inve_pgqd");
            //保存基层组织意见附件和辖区公安派出所意见附件
            if (ObjectUtil.isNotEmpty(param.getJcyjFile()) || ObjectUtil.isNotEmpty(param.getGayjFile())) {
                StringJoiner newIdsJoiner = new StringJoiner(",");
                Optional.ofNullable(param.getJcyjFile()).ifPresent(newIdsJoiner::add);
                Optional.ofNullable(param.getGayjFile()).ifPresent(newIdsJoiner::add);
                String newIds = newIdsJoiner.toString();
                sysFileInfoService.lambdaUpdate()
                        .set(SysFileInfo::getDelFlag, 1)
                        .eq(SysFileInfo::getBizId, param.getId())
                        .in(SysFileInfo::getBizType, "JCZZYJ", "PCSYJ")
                        .notIn(SysFileInfo::getId, newIds.split(","))
                        .update();
            }
            sysFileInfoService.setBiz(param.getJcyjFile(), param.getId(), "JCZZYJ");
            sysFileInfoService.setBiz(param.getGayjFile(), param.getId(), "PCSYJ");
            return new SuccessResponseData();
        }
        investigationInfoService.groupInvestigateList(param);
        return new SuccessResponseData();
    }

    @PostMapping("/investigationInfo/groupInvestigateCommit")
    @ApiOperation("调查评估_调查提交")
    @BusinessLog(title = "调查评估_调查提交", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData groupInvestigateCommit(@RequestBody @Validated(InvestigationGroupParam.edit.class) InvestigationGroupParam param) {
        LoginContext loginContext = LoginContextHolder.me();
        param.setApprovalPsn(loginContext.getSysLoginUser().getName());
        param.setApprovalPsnId(loginContext.getSysLoginUserId());
        investigationInfoService.groupInvestigateCommit(param);
        return new SuccessResponseData();
    }

    @PostMapping("/investigationInfo/deliberation")
    @ApiOperation("调查评估_合议")
    @BusinessLog(title = "调查评估_合议", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData deliberation(@RequestBody @Validated(InvestigationDeliberationParam.edit.class) InvestigationDeliberationParam param) {
        LoginContext loginContext = LoginContextHolder.me();
        param.setApprovalPsn(loginContext.getSysLoginUser().getName());
        param.setApprovalPsnId(loginContext.getSysLoginUserId());
        investigationInfoService.deliberation(param);
        //处理未签名的,如果非草稿
        if ("0".equals(param.getIsDraft()) || ObjectUtil.isEmpty(param.getIsDraft())) {
            investigationSignService.handleNoSign(param.getId(), "PGZT04");
        }
        return new SuccessResponseData();
    }

    @PostMapping("/investigationInfo/review")
    @ApiOperation("调查评估_评议")
    @BusinessLog(title = "调查评估_评议", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData review(@RequestBody @Validated(InvestigationReviewParam.edit.class) InvestigationReviewParam param) {
        LoginContext loginContext = LoginContextHolder.me();
        param.setApprovalPsn(loginContext.getSysLoginUser().getName());
        param.setApprovalPsnId(loginContext.getSysLoginUserId());
        investigationInfoService.review(param);
        //处理未签名的,如果非草稿
        if ("0".equals(param.getIsDraft()) || ObjectUtil.isEmpty(param.getIsDraft())) {
            investigationSignService.handleNoSign(param.getId(), "PGZT05");
        }
        return new SuccessResponseData();
    }

    @PostMapping("/investigationInfo/approval")
    @ApiOperation("调查评估_审批")
    @BusinessLog(title = "调查评估_审批", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData approval(@RequestBody @Validated(InvestigationApprovalParam.edit.class) InvestigationApprovalParam param) {
        LoginContext loginContext = LoginContextHolder.me();
        param.setApprovalPsn(loginContext.getSysLoginUser().getName());
        param.setApprovalPsnId(loginContext.getSysLoginUserId());
        String receiveDeptId = param.getOtherInfo().getString("receiveDeptId");
        SysOrg org = sysOrgService.getById(receiveDeptId);
        if (null != org) {
            param.setInveDeptContactPsn(org.getLxr());
            param.setInveDeptTel(org.getLxdh());
        }
        investigationInfoService.approval(param);
        //处理未签名的,如果非草稿
        if ("0".equals(param.getIsDraft()) || ObjectUtil.isEmpty(param.getIsDraft())) {
            investigationSignService.handleNoSign(param.getId(), "PGZT06");
        }
        return new SuccessResponseData();
    }

    @PostMapping("/investigationInfo/feedback")
    @ApiOperation("调查评估_反馈")
    @BusinessLog(title = "调查评估_反馈", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData feedback(@RequestBody @Validated(InvestigationFeedbackParam.edit.class) InvestigationFeedbackParam param) {
        LoginContext loginContext = LoginContextHolder.me();
        param.setApprovalPsn(loginContext.getSysLoginUser().getName());
        param.setApprovalPsnId(loginContext.getSysLoginUserId());
        investigationInfoService.feedback(param);
        return new SuccessResponseData();
    }

    /**
     * 查看调查评估信息表
     *
     * <AUTHOR>
     * @date 2025-03-20 10:30:38
     */
    @GetMapping("/investigationInfo/detail")
    @ApiOperation("调查评估_查看")
    public ResponseData detail(@Validated(InvestigationInfoParam.detail.class) InvestigationInfoParam investigationInfoParam) {
        if (ObjectUtil.isNotEmpty(investigationInfoParam.getApprovalDeptId())) {
            SysOrg org = sysOrgService.getById(investigationInfoParam.getApprovalDeptId());
            if (null != org && "sp".equals(org.getType())) {
                //类型是部门则找父级机构的名称
                org = sysOrgService.getById(org.getPid());
                if (null != org) {
                    investigationInfoParam.setApprovalDeptId(org.getId());
                }
            }
        }
        JSONObject detail = investigationInfoService.detail(investigationInfoParam.getId(), investigationInfoParam.getApprovalDeptId());
        if (ObjectUtil.isNotEmpty(detail.getString("receiveDeptId")) && "PGZT02".equals(detail.getString("status"))) {
            //小组公告这一步时从接收机构的文书落款字段取值
            SysOrg org = sysOrgService.getById(detail.getString("receiveDeptId"));
            detail.put("wslk", org.getWslk());
            if (null != org && "sp".equals(org.getType())) {
                //类型是部门则找父级机构的章
                org = sysOrgService.getById(org.getPid());
                if (null != org) {
                    detail.put("wslk", org.getWslk());
                }
            }
        }
        return new SuccessResponseData(detail);
    }

    /**
     * 终止流程
     *
     * <AUTHOR>
     * @date 2025-03-20 10:30:38
     */
    @PostMapping("/investigationInfo/stop")
    @ApiOperation("调查评估_终止流程")
    public ResponseData stop(@RequestBody @Validated(InvestigationInfoParam.detail.class) InvestigationInfoParam investigationInfoParam) {
        // ApprovalDeptId 和 approvalDeptName 由前台推送，后台获取太麻烦
        InvestigationInfo investigationInfo = investigationInfoService.getById(investigationInfoParam.getId());
        if (null != investigationInfo && "PGZT01".equals(investigationInfo.getStatus())) {
            return new ErrorResponseData(1013002, "待接收状态无法终止流程！");
        }
        LoginContext loginContext = LoginContextHolder.me();
        investigationInfoParam.setApprovalPsn(loginContext.getSysLoginUser().getName());
        investigationInfoParam.setApprovalPsnId(loginContext.getSysLoginUserId());
        return new SuccessResponseData(investigationInfoService.stop(investigationInfoParam));
    }

    /**
     * 流程记录
     *
     * <AUTHOR>
     * @date 2025-03-20 10:30:38
     */
    @GetMapping("/investigationInfo/flowList")
    @ApiOperation("调查评估_流程记录")
    public ResponseData flowList(@Validated(InvestigationInfoParam.detail.class) InvestigationInfoParam investigationInfoParam) {
        return new SuccessResponseData(investigationInfoFlowService.list(investigationInfoParam.getId()));
    }

    /**
     * 导出word(调用python方法转换)
     *
     * <AUTHOR>
     * @date 2025-03-20 10:30:38
     */
    @GetMapping("/investigationInfo/downloadDoc")
    @ApiOperation("导出word")
    public void downloadDoc(String fileId, String ossUrl, String fileName, HttpServletResponse response) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(pdfToWordUrl);
            //TODO 服务器python没ssl模块，https的地址报错，暂时先替换为http
            if (!"prod".equals(active)) {
                //开发环境
                ossUrl = ossUrl.replace("https", "http");
            } else {
                //正式环境
                ossUrl = ossUrl.replace("https://znhf.zjsft.gov.cn", "http://59.202.53.111:8093").replace("https://sjxt.zjsft.gov.cn", "http://59.202.53.111:8093");
            }
            // 设置请求体
            String json = String.format("{\"oss_url\": \"%s\"}", ossUrl);
            StringEntity entity = new StringEntity(json);
            httpPost.setEntity(entity);
            httpPost.setHeader("Content-type", "application/json");

            // 发送请求
            try (CloseableHttpResponse httpResponse = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = httpResponse.getEntity();

                if (httpResponse.getStatusLine().getStatusCode() == 200) {
                    response.reset();
                    // 设置响应头
                    response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=UTF-8");
                    // response.setHeader("Content-Disposition", "attachment; filename=" + fileName.replaceAll(".pdf", "") + ".docx");
                    String encodedFileName = URLEncoder.encode(fileName.replaceAll(".pdf", "") + ".docx", CharsetUtil.UTF_8);
                    response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
                    response.setHeader("Access-Control-Allow-Origin", "*");
                    response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
                    response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
                    response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

                    // 直接将Python服务的响应流写入客户端响应
                    try (InputStream in = responseEntity.getContent();
                         OutputStream out = response.getOutputStream()) {
                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = in.read(buffer)) != -1) {
                            out.write(buffer, 0, bytesRead);
                        }
                        out.flush();
                    }
                } else {
                    response.setContentType("application/json");
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    response.getWriter().write(EntityUtils.toString(responseEntity));
                }
            }
        } catch (Exception e) {
            try {
                response.setContentType("application/json");
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("{\"error\": \"" + e.getMessage() + "\"}");
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }

    @PostMapping("/investigationInfo/createNotice")
    @ApiOperation("调查评估_小组公告_制作文书")
    @BusinessLog(title = "小组公告_制作文书", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData createNotice(@RequestBody InvestigationGroupParam param) {
        InvestigationInfo info = investigationInfoService.getById(param.getId());
        if (ObjectUtil.isNotEmpty(info.getReceiveDeptId())) {
            //从接收机构的文书落款字段取
            SysOrg org = sysOrgService.getById(info.getReceiveDeptId());
            param.setApprovalDeptName(org.getWslk());
            if (null != org && "sp".equals(org.getType())) {
                //类型是部门则找父级机构的章
                org = sysOrgService.getById(org.getPid());
                if (null != org) {
                    param.setApprovalDeptName(org.getWslk());
                }
            }
        }
        if (ObjectUtil.isEmpty(param.getApprovalDeptName())) {
            if (null != info) {
                param.setApprovalDeptName(info.getReceiveDeptName());
            } else {
                SysOrg org = sysOrgService.getById(param.getApprovalDeptId());
                param.setApprovalDeptName(org.getName());
                if (null != org && "sp".equals(org.getType())) {
                    //类型是部门则找父级机构的章
                    org = sysOrgService.getById(org.getPid());
                    if (null != org) {
                        param.setApprovalDeptName(org.getName());
                    }
                }
            }
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日", Locale.CHINESE);
            Map<String, String> map = new HashMap<>();
            map.put("noticeTitle", param.getNoticeTitle());
            map.put("noticeDocNum", param.getNoticeDocNumOne() + (Integer.parseInt(param.getNoticeDocNumTwo()) > 99 ? param.getNoticeDocNumTwo() : String.format("%03d", Integer.parseInt(param.getNoticeDocNumTwo())))
                    + param.getNoticeDocNumThree());
            map.put("noticeContext", param.getNoticeContext());
            map.put("approvalDeptName", param.getApprovalDeptName());
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            map.put("noticeTime", sdf.format(sf.parse(param.getNoticeTime())));
            // 生成文件名
            String fileName = param.getCorrectionObjName() + "的小组公告.pdf";

            // 创建临时文件目录
            File tempDir = new File(System.getProperty("java.io.tmpdir"));
            File tempWordFile = File.createTempFile("wordxzgg_", ".docx", tempDir);
            File tempPdfFile = File.createTempFile("pdfxzgg_", ".pdf", tempDir);

            try {
                // 使用WordUtil填充模板并生成Word文档
                WordUtil.fillTemplateWithObject(noticePath, tempWordFile.getAbsolutePath(), map);

                // 将Word转换为PDF
                try (FileInputStream fis = new FileInputStream(tempWordFile);
                     FileOutputStream fos = new FileOutputStream(tempPdfFile)) {
                    LibreOfficeUtil.convertToPdf(fis, fos, "docx");
                }

                // 将PDF文件上传到存储系统
                SysFileInfo sysFileInfo = sysFileInfoService.uploadTemplatedFileOss(fileName, tempPdfFile);
//                String pdfPath = sysFileInfo.getFilePath();

                // 返回文件路径
                return new SuccessResponseData(sysFileInfo);

            } finally {
                // 清理临时文件
                if (tempWordFile.exists()) {
                    tempWordFile.delete();
                }
                if (tempPdfFile.exists()) {
                    tempPdfFile.delete();
                }
            }

        } catch (Exception e) {
            log.error("生成小组公告PDF失败", e);
            return ResponseData.error("生成小组公告PDF失败");
        }
    }

    @PostMapping("/investigationInfo/createPdfDeliberation")
    @ApiOperation("调查评估_小组合议_制作文书")
    @BusinessLog(title = "小组合议_制作文书", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData createPdfDeliberation(@RequestBody InvestigationDeliberationParam param) {
        InvestigationInfo info = investigationInfoService.getById(param.getId());
        if (null != info && ObjectUtil.isNotEmpty(info.getInveDeptName())) {
            //不为空则用调查单位(司法所)，为空取当前登录人
            param.setInveDeptName(info.getInveDeptName());
        } else {
            SysOrg org = sysOrgService.getById(param.getApprovalDeptId());
            param.setInveDeptName(org.getName());
            if (null != org && "sp".equals(org.getType())) {
                //类型是部门则找父级机构的名称
                org = sysOrgService.getById(org.getPid());
                if (null != org) {
                    param.setInveDeptName(org.getName());
                }
            }
        }
        SysFileInfo pdfDeliberation = investigationDeliberationService.createPdfDeliberation(param);
        if (null != pdfDeliberation) {
            //删除旧的签名
            investigationSignService.deleteOld(info.getId(), "opinion_deliberate");
        }
        return new SuccessResponseData(pdfDeliberation);
    }

    @PostMapping("/investigationInfo/createPdfReview")
    @ApiOperation("调查评估_集体评议_制作文书")
    @BusinessLog(title = "集体评议_制作文书", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData createPdfReview(@RequestBody InvestigationReviewParam param) {
        InvestigationInfo info = investigationInfoService.getById(param.getId());
        if (null != info && ObjectUtil.isNotEmpty(info.getReceiveDeptName())) {
            //不为空则用接收单位(司法局)，为空取当前登录人
            param.setApprovalDeptName(info.getReceiveDeptName());
        } else {
            SysOrg org = sysOrgService.getById(param.getApprovalDeptId());
            param.setApprovalDeptName(org.getName());
            if (null != org && "sp".equals(org.getType())) {
                //类型是部门则找父级机构的章
                org = sysOrgService.getById(org.getPid());
                if (null != org) {
                    param.setApprovalDeptName(org.getName());
                }
            }
        }
        SysFileInfo pdfReview = investigationReviewService.createPdfReview(param);
        if (null != pdfReview) {
            //删除旧的签名
            investigationSignService.deleteOld(info.getId(), "opinion_review");
        }
        return new SuccessResponseData(pdfReview);
    }

    /**
     * 根据所有笔录信息生成调查评估意见
     */
    @GetMapping("/investigationInfo/createOpinionString")
    @ApiOperation("调查评估_根据所有笔录信息生成调查评估意见")
    public ResponseData createOpinionString(@RequestParam String id) {
        return new SuccessResponseData(investigationTranscriptService.createOpinionString(id));
    }


    @PostMapping("/investigationInfo/createPdfOpinion")
    @ApiOperation("调查评估_评估意见书")
    public ResponseData createPdfOpinion(@RequestBody InvestigationFeedbackParam param, HttpServletResponse response) {
        try {
            // 调用Service层获取填充好的数据
            InvestigationFeedbackParam preparedData = investigationFeedbackService.preparePdfOpinionData(param);

            // 生成文件名
            String fileName = preparedData.getCorrectionObjName() + "的调查评估意见书.pdf";

            // 创建临时文件目录
            File tempDir = new File(System.getProperty("java.io.tmpdir"));
            File tempWordFile = File.createTempFile(fileName, ".docx", tempDir);
            File tempPdfFile = File.createTempFile(fileName, ".pdf", tempDir);

            try {
                // 使用WordUtil填充模板并生成Word文档
                WordUtil.fillTemplateWithObject(dcpgyjsPath, tempWordFile.getAbsolutePath(), preparedData);

                // 将Word转换为PDF
                try (FileInputStream fis = new FileInputStream(tempWordFile);
                     FileOutputStream fos = new FileOutputStream(tempPdfFile)) {
                    LibreOfficeUtil.convertToPdf(fis, fos, "docx");
                }

                // 将PDF文件上传到存储系统
                SysFileInfo sysFileInfo = sysFileInfoService.uploadTemplatedFileOss(fileName, tempPdfFile);
//                String pdfPath = sysFileInfo.getFilePath();

                // 返回文件路径
                return new SuccessResponseData(sysFileInfo);

            } finally {
                // 清理临时文件
                if (tempWordFile.exists()) {
                    tempWordFile.delete();
                }
                if (tempPdfFile.exists()) {
                    tempPdfFile.delete();
                }
            }

        } catch (Exception e) {
            log.error("生成评估意见书PDF失败", e);
            return ResponseData.error("生成评估意见书PDF失败");
        }
    }


    @PostMapping("/investigationInfo/inveFormPdf")
    @ApiOperation("调查评估_调查评估表")
    @BusinessLog(title = "调查评估_调查评估表", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData createInvestigationFormPdf(@RequestBody JSONObject param) {
        String bizId = param.getString("id");
        String status = param.getString("status");
        SysFileInfo lastFile;
        String bizType = "";
        if ("PGZT03".equals(status)) {
            return new SuccessResponseData(investigationGroupService.createInvestigationFormPdf(param));
        } else if ("PGZT04".equals(status)) {
            bizType = "eval_draft";
        } else if ("PGZT05".equals(status)) {
            bizType = "eval_deliberate";
        } else if ("PGZT06".equals(status)) {
            bizType = "eval_review";
        } else {
            return ResponseData.error("status error");
        }
        lastFile = sysFileInfoService.lambdaQuery().eq(SysFileInfo::getBizId, bizId).eq(SysFileInfo::getBizType, bizType).orderByDesc(SysFileInfo::getCreateTime).last("limit 1").one();
        if (lastFile != null && "1".equals(lastFile.getType())) {
            // 上一步手动上传的情况
            return new SuccessResponseData(lastFile);
        }
        SysFileInfo investigationFormPdf = investigationGroupService.createInvestigationFormPdf(param);
        if (null != investigationFormPdf) {
            //删除旧的签名
            if ("PGZT04".equals(status)) {
                bizType = "eval_deliberate";
            } else if ("PGZT05".equals(status)) {
                bizType = "eval_review";
            } else if ("PGZT06".equals(status)) {
                bizType = "eval_approval";
            }
            investigationSignService.deleteOld(bizId, bizType);
        }
        return new SuccessResponseData(investigationFormPdf);
    }


    @GetMapping("/investigationInfo/batchDownLoad")
    @ApiOperation("打包下载")
    @BusinessLog(title = "打包下载", opType = LogAnnotionOpTypeEnum.OTHER)
    public void batchDownLoad(String bizId, String bizType, String xm) {
        investigationGroupService.batchDownLoad(bizId, bizType, xm);
    }

    @GetMapping("/investigationInfo/initFlow")
    @ApiOperation("调查评估_新建_初始化流程记录")
    @BusinessLog(title = "调查评估_新建_初始化流程记录", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData initFlow() {
        String investigationInfoId = String.valueOf(IdWorker.getId());
        investigationInfoFlowService.initFlow(investigationInfoId);
        return new SuccessResponseData(investigationInfoId);
    }

    /**
     * 流程记录结构化数据
     *
     * <AUTHOR>
     * @date 2025-03-20 10:30:38
     */
    @GetMapping("/investigationInfo/flowData")
    @ApiOperation("调查评估_流程记录")
    public ResponseData flowData(@Validated(InvestigationInfoParam.detail.class) InvestigationInfoParam investigationInfoParam) {
        return new SuccessResponseData(investigationInfoFlowService.listFlow(investigationInfoParam.getId()));
    }

    /**
     * 根据状态返回签章信息
     *
     * <AUTHOR>
     * @date 2025-03-20 10:30:38
     */
    @GetMapping("/investigationInfo/getSignature")
    @ApiOperation("根据状态返回签章信息")
    @BusinessLog(title = "调查评估_根据状态返回签章信息", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData getSignature(String deptId, String status) {
        SysOrg org = sysOrgService.getById(deptId);
        if (null != org && "sp".equals(org.getType())) {
            //类型是部门则找父级机构的章
            deptId = org.getPid();
        }
        return new SuccessResponseData(investigationGroupService.getSignature(deptId, status));
    }

    /**
     * 计算调查截止时间
     *
     * <AUTHOR>
     * @date 2025-03-20 10:30:38
     */
    @GetMapping("/investigationInfo/initJzsj")
    @ApiOperation("计算调查截止时间")
    @BusinessLog(title = "计算调查截止时间", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData initJzsj(String entrustmentReceiveTime, int totalNum) {
        Date date = sysHolidayService.countEndDate(entrustmentReceiveTime, totalNum);
        if (null != date) {
            JSONObject obj = new JSONObject();
            obj.put("date", date);
            return new SuccessResponseData(obj);
        } else {
            return new ErrorResponseData(400, "调查截止时间计算异常");
        }
    }

    /**
     * 反馈_选择文书
     *
     * @param id
     * @param choosedFileIds
     * @return
     */
    @GetMapping("/investigationInfo/chooseDocument")
    @ApiOperation("反馈_选择文书")
    @BusinessLog(title = "反馈_选择文书", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData chooseDocument(String id, String choosedFileIds) {
        LambdaQueryWrapper<SysFileInfo> fileWrapper = new LambdaQueryWrapper<>();
        fileWrapper.eq(SysFileInfo::getBizId, id);
        fileWrapper.isNotNull(SysFileInfo::getBizType);
        fileWrapper.notIn(SysFileInfo::getBizType, "eval_draft", "eval_deliberate", "eval_review");
        if (ObjectUtil.isNotEmpty(choosedFileIds)) {
            fileWrapper.notIn(SysFileInfo::getId, choosedFileIds.split(","));
        }
        return new SuccessResponseData(sysFileInfoService.list(fileWrapper));
    }

    @GetMapping("/investigationInfo/export")
    @ApiOperation("调查评估_导出")
    public void export(InvestigationInfoParam param) {
        param.setReceiveDeptId(sysOrgService.getSearchOrgId(param.getReceiveDeptId()));
        QueryWrapper<InvestigationInfo> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据状态 查询
            if (ObjectUtil.isNotEmpty(param.getStatus())) {
                queryWrapper.lambda().eq(InvestigationInfo::getStatus, param.getStatus());
            }
            // 根据委托类别(字典) 查询
            if (ObjectUtil.isNotEmpty(param.getEntrustmentType())) {
                queryWrapper.lambda().eq(InvestigationInfo::getEntrustmentType, param.getEntrustmentType());
            }
            // 根据委托单位名称 查询
            if (ObjectUtil.isNotEmpty(param.getEntrustmentDeptName())) {
                queryWrapper.lambda().like(InvestigationInfo::getEntrustmentDeptName, param.getEntrustmentDeptName());
            }
            // 委托时间开始
            if (ObjectUtil.isNotEmpty(param.getEntrustmentTimeStart())) {
                queryWrapper.lambda().ge(InvestigationInfo::getEntrustmentTime, param.getEntrustmentTimeStart());
            }
            // 委托时间结束
            if (ObjectUtil.isNotEmpty(param.getEntrustmentTimeEnd())) {
                queryWrapper.lambda().le(InvestigationInfo::getEntrustmentTime, param.getEntrustmentTimeEnd());
            }
            // 调查截止开始
            if (ObjectUtil.isNotEmpty(param.getInveTimeLimitStart())) {
                queryWrapper.lambda().ge(InvestigationInfo::getInveTimeLimit, param.getInveTimeLimitStart());
            }
            // 调查截止结束
            if (ObjectUtil.isNotEmpty(param.getInveTimeLimitEnd())) {
                queryWrapper.lambda().le(InvestigationInfo::getInveTimeLimit, param.getInveTimeLimitEnd());
            }
            // 根据接收单位 查询
            if (ObjectUtil.isNotEmpty(param.getReceiveDeptId())) {
                queryWrapper.lambda().and(i -> i.eq(InvestigationInfo::getReceiveDeptId, param.getReceiveDeptId()).or().like(InvestigationInfo::getReceiveDeptPids, param.getReceiveDeptId())
                        .or().like(InvestigationInfo::getInveDept, param.getReceiveDeptId()));
            }
            // 调查单位
            if (ObjectUtil.isNotEmpty(param.getInveDept())) {
                queryWrapper.apply("inve_dept in (select id from sys_org where pids like '%"+param.getInveDept()+"%' or id = '"+param.getInveDept()+"')");
            }
            // 根据调查对象姓名 查询
            if (ObjectUtil.isNotEmpty(param.getCorrectionObjName())) {
                queryWrapper.lambda().like(InvestigationInfo::getCorrectionObjName, param.getCorrectionObjName());
            }
            if (param.getNodeType() > 0) {
                switch (param.getNodeType()) {
                    case 1:
                        //待接收、待公告
                        queryWrapper.lambda().in(InvestigationInfo::getStatus, "PGZT01,PGZT02".split(","));
                        break;
                    case 2:
                        //待调查
                        queryWrapper.lambda().in(InvestigationInfo::getStatus, "PGZT03".split(","));
                        break;
                    case 3:
                        //待初审/小组意见、待初审/评议、待审批、待反馈
                        queryWrapper.lambda().in(InvestigationInfo::getStatus, "PGZT04,PGZT05,PGZT06,PGZT07".split(","));
                        break;
                    case 4:
                        //已完结、流程终止
                        queryWrapper.lambda().in(InvestigationInfo::getStatus, "PGZT08,PGZT98,PGZT99".split(","));
                        break;
                    default:
                }
            }
        }
        queryWrapper.lambda().eq(InvestigationInfo::getDeleted, 0);
        queryWrapper.lambda().orderByDesc(InvestigationInfo::getEntrustmentReceiveTime);
        List<InvestigationInfo> list = investigationInfoService.list(queryWrapper);
        String criminalCharge = "";
        JSONArray arr = null;
        JSONObject obj = null;
        for (InvestigationInfo inv : list) {
            if (ObjectUtil.isNotEmpty(inv.getCriminalCharge())) {
                criminalCharge = "";
                arr = JSONArray.parseArray(inv.getCriminalCharge());
                for (int i = 0; i < arr.size(); i++) {
                    obj = (JSONObject) arr.get(i);
                    criminalCharge += obj.getString("crimeName") + ",";
                }
                inv.setCriminalCharge(criminalCharge.substring(0, criminalCharge.length() - 1));
            }
        }
        PoiUtil.exportExcelWithStream("调查评估.xls", InvestigationInfo.class, list);
    }

    /**
     * 附件下载方法
     *
     * @param filePath 笔录ID
     * @param response HTTP响应对象
     */
    @GetMapping("/investigationInfo/downloadFile")
    @ApiOperation("附件下载方法")
    public void downloadFile(String filePath, String fileOriginName, HttpServletResponse response) {
        try {
            if (filePath.lastIndexOf('?')>0) {
                filePath = filePath.substring(0, filePath.lastIndexOf('?'));
            }
            // https://sjxt.zjsft.gov.cn/oss/zjgz/dataCollaboration/202505/1923332762768592898.pdf?x-oss-signature=2uSa2QyrNBkZDGCoo6%2F6IkIjLlrNOpQEXlr1dZtN4qc%3D&x-oss-signature-version=OSS2&x-oss-expires=1747998039&x-oss-access-key-id=qkQvXKBRd8CLIvjq?x-oss-signature=fqLdS3j5N1mwuUHXITpmoGi7/JX2/cUxGN6BQe0dLHI=
            String fileName = "";
            if (ObjectUtil.isNotEmpty(fileOriginName)) {
                fileName = fileOriginName;
            } else {
                fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
            }
          /*  // 设置响应头
            response.setContentType("application/octet-stream");

            String encodedFileName = URLEncoder.encode(fileName, "utf-8").replace("+", "%20");
            response.setHeader("Content-Disposition", "attachment;filename*=UTF-8''" + encodedFileName);*/
            // 设置文件下载头信息
            setFileDownloadHeader(response, fileName, "pdf", true);

            // 流式传输文件
            try (InputStream inputStream = new URL(filePath).openStream();
                 OutputStream outputStream = response.getOutputStream()) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }
        } catch (FileNotFoundException e) {
            response.setStatus(HttpStatus.NOT_FOUND.value());
            log.error("File not found: {}", filePath);
        } catch (IOException e) {
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            log.error("Download failed: {}", e.getMessage());
        }

    }

    /**
     * 设置文件下载的响应头信息，处理文件名编码问题
     * 支持中文文件名，兼容各种浏览器和Postman
     *
     * @param response       HTTP响应对象
     * @param fileName       文件名（含扩展名）
     * @param fileType       文件类型，例如 "docx" 或 "pdf"
     * @param addCorsHeaders 是否添加CORS头信息（用于Postman等跨域请求）
     */
    public static void setFileDownloadHeader(HttpServletResponse response, String fileName, String fileType, boolean addCorsHeaders) {
        String fullFileName = fileName;
        try {
            String encodedFileName = URLEncoder.encode(fullFileName, CharsetUtil.UTF_8);
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");

            // 设置响应内容类型
            if ("pdf".equalsIgnoreCase(fileType)) {
                response.setContentType("application/pdf;charset=UTF-8");
            } else {
                response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=UTF-8");
            }

            // 添加额外的CORS头信息以支持Postman等跨域请求
            if (addCorsHeaders) {
                response.setHeader("Access-Control-Allow-Origin", "*");
                response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
                response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
                response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            }


        } catch (Exception e) {
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fullFileName + "\"");
        }
    }


    @GetMapping("/investigationInfo/historyList")
    public ResponseData historyList(String id,String status) {
        InvestigationInfo byId = investigationInfoService.getById(id);
        if (byId == null||ObjectUtil.isEmpty(status)) {
            return new SuccessResponseData();
        }
        List<InvestigationHistoryField> historyList = investigationDeliberationService.historyList(status, byId.getInveDept());
        return new SuccessResponseData(historyList);
    }

    @GetMapping("/investigation/delayList")
    public ResponseData delayList(String id) {
        return new SuccessResponseData(investigationDelayService.list(id));
    }

    @PostMapping("/investigation/delayAdd")
    @ApiOperation("调查评估延期记录_增加")
    @BusinessLog(title = "调查评估延期记录_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData delayAdd(@RequestBody @Validated(InvestigationDelayParam.add.class) InvestigationDelayParam investigationDelayParam) {
        investigationDelayService.add(investigationDelayParam);
        return new SuccessResponseData();
    }

    /**
     * 设置笔录调查方式
     * @param id
     * @return
     */
    @GetMapping("/investigation/setTranscriptTag")
    public ResponseData setTranscriptTag(String id, Integer transcriptTag) {
        investigationInfoService.lambdaUpdate()
                .set(InvestigationInfo::getTranscriptTag, transcriptTag)
                .eq(InvestigationInfo::getId, id)
                .update();
        return new SuccessResponseData();
    }


    /**
     * 笔录第一次完成时初始化笔录文书 并上传至Oss
     * @param id
     * @param type
     */
    public SysFileInfo initTranscript(String id, String type, InvestigationTranscript transcript) {
        // 笔录下载
        InvestigationTranscript investigationTranscript = null;
        if (null != transcript) {
            investigationTranscript = transcript;
            investigationTranscript.setPid("xtzzbl");
            investigationTranscript.setId("xtzzbl");
        } else {
            investigationTranscript = investigationTranscriptService.getById(id);
        }
        if (investigationTranscript == null) {
            throw new ServiceException(500, "笔录不存在");
        }
        // 获取笔录内容
        String context = investigationTranscript.getContext();
        if (context == null) {
            throw new ServiceException(500, "笔录内容为空");
        }
        // 转换为PaperMaintenance对象
        PaperMaintenanceParam paperMaintenance = JSON.parseObject(context, PaperMaintenanceParam.class);

        //基本信息转换
        InvestigationRecordParam investigationRecordParam = JSON.parseObject(investigationTranscript.getBasicInfo(), InvestigationRecordParam.class);
        // 获取题目列表
        List<PaperTopicParam> topicParamList = paperMaintenance.getTopicList();

        if (topicParamList == null || topicParamList.isEmpty()) {
            throw new RuntimeException("请提供题目列表");
        }

        //根据笔录类型来选择模板
        String templatePath = null;
        //判断是否假释
        boolean sfjs = false;
        InvestigationInfo investigationInfo = investigationInfoService.getById(investigationTranscript.getPid());
        // 添加空值检查，防止空指针异常
        if (investigationInfo != null && "3".equals(investigationInfo.getCorrectionType())) {
            sfjs = true;
        }
        switch (investigationTranscript.getPaperType()) {
            //被告人
            case "BLLX06":
                templatePath = bl_bgr;
                break;
            //其他所有类型都使用bl_bhr模板
            default:
                templatePath = bl_bhr;
                break;
        }

        // 将题目转换为问答格式
        List<Map<String, Object>> questionAnswerList = new ArrayList<>();
        for (PaperTopicParam topicParam : topicParamList) {
            Map<String, Object> qaItem = new HashMap<>();
            qaItem.put("question", topicParam.getTopicName());
            qaItem.put("answer", ""); // 留空表示显示下划线

            // 添加控制空间的参数，当type不为空时减少空行
            qaItem.put("reduceSpace", type != null);

            // 只有当type为空时才填充选项
            if (type == null) {
                // 处理选项
                List<String> options = new ArrayList<>();
                List<String> optionIds = new ArrayList<>(); // 存储选项ID
                if (topicParam.getItemList() != null && !topicParam.getItemList().isEmpty()) {
                    for (PaperTopicItemParam item : topicParam.getItemList()) {
                        options.add(item.getContent());
                        optionIds.add(item.getId()); // 添加选项ID
                    }
                }
                qaItem.put("options", options);
                qaItem.put("optionIds", optionIds);
            } else {
                // 当type不为空时，添加空的选项列表
                qaItem.put("options", new ArrayList<>());
                qaItem.put("optionIds", new ArrayList<>());
            }

            // 备注
            qaItem.put("remark", topicParam.getRemark());

            // 用户回答 - 只有当用户回答非空时才添加
            if (topicParam.getUserAnswer() != null && !topicParam.getUserAnswer().isEmpty()) {
                qaItem.put("userAnswer", topicParam.getUserAnswer());
            }

            // 用户选择 - 只有当用户选择非空时才添加
            if (topicParam.getUserSelectId() != null && !topicParam.getUserSelectId().isEmpty()) {
                // 将选择的ID转换为选项内容的列表
                List<String> selectedContents = new ArrayList<>();
                String[] selectedIds = topicParam.getUserSelectId().split(",");

                if (topicParam.getItemList() != null && !topicParam.getItemList().isEmpty()) {
                    // 根据ID查找选项内容
                    for (String selectedId : selectedIds) {
                        for (PaperTopicItemParam item : topicParam.getItemList()) {
                            if (selectedId.equals(item.getId())) {
                                selectedContents.add(item.getContent());
                                break;
                            }
                        }
                    }
                }

                // 处理用户选择内容
                if (!selectedContents.isEmpty()) {
                    // 检查题目类型，单选题直接使用第一个选项，多选题才用逗号拼接
                    if (topicParam.getTopicType() != null && "1".equals(topicParam.getTopicType())) {
                        // 单选题，只取第一个选项
                        qaItem.put("userSelectId", selectedContents.get(0));
                    } else {
                        // 多选题或其他类型，使用逗号拼接
                        qaItem.put("userSelectId", String.join(",", selectedContents));
                    }
                } else {
                    // 如果没有找到对应内容，则保留原始ID
                    qaItem.put("userSelectId", topicParam.getUserSelectId());
                }
            }

            questionAnswerList.add(qaItem);
        }

        // 调用工具类生成PDF文档
        String paperTitle = paperMaintenance.getBlTypeName() != null ? paperMaintenance.getBlTypeName() : "自定义问答题";
        String fileName = investigationTranscript.getTitle();

        try {
            // 调用新的组合方法，同时填充对象和题目，并转换为PDF
            File file = WordUtil.exportQuestionsWithAnswersAndObjectAsPdfFile(fileName, templatePath, paperTitle, questionAnswerList, investigationRecordParam);
            SysFileInfo sysFileInfo = sysFileInfoService.uploadFileOss(file, investigationTranscript.getPid(), "BLLX", investigationRecordParam.getCorrectionPerson() + "的" + investigationTranscript.getTitle() + ".pdf", investigationTranscript.getPaperType(),
                    investigationTranscript.getId(), null);
            log.debug("导出笔录PDF成功，同时填充了对象和题目");
            return sysFileInfo;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("导出笔录PDF失败,id={}", id);
        }
        return null;
    }

    @PostMapping("/investigationInfo/createBl")
    @ApiOperation("调查评估_笔录制作_制作文书")
    @BusinessLog(title = "笔录制作_制作文书", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData createBl(@RequestBody SysFileInfoParam sysFileInfoParam) {
        InvestigationTranscript trans = investigationTranscriptService.getById(sysFileInfoParam.getBlId());
        if (null != trans && !"2".equals(trans.getProgress())) {
            return new ErrorResponseData(1013002, "需先提交对应的笔录，暂存状态下无法重新制作");
        }
        sysFileInfoService.lambdaUpdate()
                .set(SysFileInfo::getDelFlag, 1)
                .eq(SysFileInfo::getBizId, sysFileInfoParam.getBizId())
                .eq(SysFileInfo::getBlId, sysFileInfoParam.getBlId())
                .eq(SysFileInfo::getBizType, "BLLX")
                .update();
        investigationSignService.lambdaUpdate()
                .set(InvestigationSign::getStatus, 2)
                .eq(InvestigationSign::getPid, sysFileInfoParam.getBizId())
                .eq(InvestigationSign::getBlId, sysFileInfoParam.getBlId())
                .update();
        SysFileInfo sysFile = initTranscript(sysFileInfoParam.getBlId() + "", "1", null);
        if (null != sysFile) {
            return new SuccessResponseData(sysFile);
        } else {
            return new ErrorResponseData(500, "重新制作失败");
        }
    }

    /**
     * 移动端笔录确认
     * @param transcriptParam
     * @return
     */
    @PostMapping("/investigationInfo/transcriptSure")
    @ApiOperation("调查评估_移动端笔录确认")
    @BusinessLog(title = "调查评估_移动端笔录确认", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData transcriptSure(@RequestBody InvestigationTranscriptParam transcriptParam) {

        // 计算题目分数
        if (transcriptParam.getPaperMaintenanceParam() != null &&
                transcriptParam.getPaperMaintenanceParam().getTopicList() != null &&
                !transcriptParam.getPaperMaintenanceParam().getTopicList().isEmpty()) {

            // 计算总分
            int totalScore = 0;
            List<PaperTopicParam> topicList = transcriptParam.getPaperMaintenanceParam().getTopicList();

            // 遍历每个题目
            for (PaperTopicParam topic : topicList) {
                // 如果用户选择了答案
                if (StringUtils.hasText(topic.getUserSelectId()) && topic.getItemList() != null) {
                    // 查找用户选择的选项
                    for (PaperTopicItemParam item : topic.getItemList()) {
                        if (item.getId().equals(topic.getUserSelectId()) && item.getItemScore() != null) {
                            // 累加选项分数
                            totalScore += item.getItemScore();
                            break;
                        }
                    }
                }
            }

            // 设置总分
            transcriptParam.setScore(String.valueOf(totalScore));
            transcriptParam.getPaperMaintenanceParam().setScore(totalScore);

            // 将问卷JSON保存到笔录JSON中
            try {
                String paperJson = JSON.toJSONString(transcriptParam.getPaperMaintenanceParam());
                transcriptParam.setContext(paperJson);
            } catch (Exception e) {
                log.error("问卷JSON序列化失败", e);
                throw new ServiceException(500, "问卷JSON序列化失败：" + e.getMessage());
            }
        }
        //用于标记是否要生成笔录文书
        int tag = 0;
        InvestigationTranscript transcript = null;
        // 更新笔录
        // 先检查笔录是否存在
        InvestigationTranscript investigationTranscript = new InvestigationTranscript();
        BeanUtils.copyProperties(transcriptParam, investigationTranscript);
        investigationTranscript.setBasicInfo(JSON.toJSONString(transcriptParam.getInvestigationRecordParam()));
        SysFileInfo file = initTranscript(null, "1", investigationTranscript);
        return new SuccessResponseData(file);
    }
}
